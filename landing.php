<?php
// Include configuration file
require_once 'includes/config.php';

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    header("location: index.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #202124;
        }

        .hero-section {
            background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/AdobeStock_271791778.jpeg');
            background-size: cover;
            background-position: center;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
        }

        .hero-title {
            font-family: 'Google Sans', sans-serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2.5rem;
            font-weight: 300;
        }

        .btn-hero {
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 8px;
            margin: 0 10px;
            font-family: 'Google Sans', sans-serif;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background-color: rgba(26, 115, 232, 0.8);
            border-color: rgba(26, 115, 232, 0.8);
        }

        .btn-primary:hover {
            background-color: rgba(26, 115, 232, 0.9);
            border-color: rgba(26, 115, 232, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .btn-outline-light {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .btn-info {
            background-color: rgba(23, 162, 184, 0.8);
            border-color: rgba(23, 162, 184, 0.8);
        }

        .btn-info:hover {
            background-color: rgba(23, 162, 184, 0.9);
            border-color: rgba(23, 162, 184, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .navbar {
            background-color: transparent;
            padding: 20px 0;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }

        .navbar-brand {
            color: white;
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            margin-right: 10px;
        }

        .navbar-brand:hover {
            color: white;
        }

        .navbar-nav .nav-link {
            color: white;
            margin-left: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .features-section {
            padding: 80px 0;
            background-color: white;
        }

        .section-title {
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            margin-bottom: 50px;
            text-align: center;
        }

        .feature-card {
            text-align: center;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .feature-title {
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .footer {
            background-color: #202124;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-links {
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .copyright {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .modal-header .modal-title {
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            color: var(--primary-color);
        }

        .modal-body {
            padding: 2rem;
        }

        .e-learning-modal-image {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            background-color: #000;
        }

        .e-learning-modal-image img {
            border-radius: 8px;
            transition: transform 0.3s ease;
        }

        .e-learning-modal-image:hover img {
            transform: scale(1.02);
        }

        .feature-item {
            padding: 1rem;
            border-radius: 8px;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-item i {
            font-size: 2rem;
            display: block;
        }

        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(248, 249, 250, 0.9);
            border-radius: 0 0 8px 8px;
        }

        .modal-content {
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .modal-header {
            background-color: rgba(248, 249, 250, 0.9);
        }

        .modal-btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .modal-btn.btn-primary {
            background-color: rgba(26, 115, 232, 0.8);
            border-color: rgba(26, 115, 232, 0.8);
        }

        .modal-btn.btn-primary:hover {
            background-color: rgba(26, 115, 232, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .modal-btn.btn-secondary {
            background-color: rgba(108, 117, 125, 0.8);
            border-color: rgba(108, 117, 125, 0.8);
        }

        .modal-btn.btn-secondary:hover {
            background-color: rgba(108, 117, 125, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">Sign In</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.php">Register</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">Welcome to the E-Learning System</h1>
            <p class="hero-subtitle">A modern platform for online education and classroom management</p>
            <div>
                <a href="login.php" class="btn btn-primary btn-hero">Sign In</a>
                <a href="register.php" class="btn btn-outline-light btn-hero">Register</a>
                <button type="button" class="btn btn-info btn-hero" data-toggle="modal" data-target="#elearningModal">
                    <i class="fas fa-info-circle mr-2"></i>Learn More
                </button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <h2 class="section-title">Key Features</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h3 class="feature-title">Interactive Courses</h3>
                        <p>Access a wide range of interactive courses designed to enhance your learning experience.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">Collaborative Learning</h3>
                        <p>Engage with instructors and fellow students through our collaborative learning tools.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Progress Tracking</h3>
                        <p>Monitor your learning progress with detailed analytics and performance reports.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-links">
                <a href="#">About Us</a>
                <a href="#">Contact</a>
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
            </div>
            <div class="copyright">
                &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.
            </div>
        </div>
    </footer>

    <!-- E-Learning Modal -->
    <div class="modal fade" id="elearningModal" tabindex="-1" role="dialog" aria-labelledby="elearningModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="elearningModalLabel">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Advanced E-Learning Platform
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div class="e-learning-modal-image">
                        <img src="images/AdobeStock_271791778.jpeg" class="img-fluid" alt="E-Learning System">
                    </div>
                    <p class="mt-4">Our e-learning platform provides interactive learning experiences with advanced tools and resources for students and instructors.</p>
                    <div class="e-learning-features mt-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="fas fa-book-reader text-primary mb-2"></i>
                                    <h5>Interactive Learning</h5>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="fas fa-chart-line text-success mb-2"></i>
                                    <h5>Progress Tracking</h5>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="fas fa-users text-info mb-2"></i>
                                    <h5>Collaborative Tools</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-btn" data-dismiss="modal">Close</button>
                    <a href="register.php" class="btn btn-primary modal-btn">Register Now</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Auto show modal after 2 seconds
        $(document).ready(function() {
            setTimeout(function() {
                $('#elearningModal').modal('show');
            }, 2000);
        });
    </script>
</body>
</html>
