<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Password Hash Fix</h1>";

try {
    // Get all users
    $stmt = $pdo->query("SELECT user_id, username, password FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($users) . " users in the database.</p>";
    
    $fixed = 0;
    $already_hashed = 0;
    
    foreach ($users as $user) {
        // Check if the password is already hashed
        $password_info = password_get_info($user['password']);
        
        if ($password_info['algo'] === 0) {
            // Password is not hashed, hash it now
            echo "<p>Fixing password for user: " . htmlspecialchars($user['username']) . " (ID: " . $user['user_id'] . ")</p>";
            
            // For demonstration, we'll assume the unhashed password is the same as the username
            // In a real scenario, you might want to set a default password and notify users
            $default_password = $user['username'];
            $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
            
            // Update the password in the database
            $update_stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :user_id");
            $update_stmt->bindParam(':password', $hashed_password);
            $update_stmt->bindParam(':user_id', $user['user_id']);
            $update_stmt->execute();
            
            echo "<p style='color: green;'>✓ Password hashed and updated for " . htmlspecialchars($user['username']) . "</p>";
            echo "<p>New password is: " . $default_password . " (same as username for demonstration)</p>";
            $fixed++;
        } else {
            // Password is already hashed
            echo "<p style='color: blue;'>✓ Password for " . htmlspecialchars($user['username']) . " is already properly hashed.</p>";
            $already_hashed++;
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p>Total users: " . count($users) . "</p>";
    echo "<p>Users with already hashed passwords: " . $already_hashed . "</p>";
    echo "<p>Users with fixed passwords: " . $fixed . "</p>";
    
    // Create a test user with a properly hashed password
    echo "<h2>Create Test User</h2>";
    echo "<p>Creating a test user with a properly hashed password...</p>";
    
    $test_username = "testuser" . rand(1000, 9999);
    $test_password = "password123";
    $hashed_password = password_hash($test_password, PASSWORD_DEFAULT);
    
    // Check if roles table exists and has student role
    $stmt = $pdo->query("SELECT role_id FROM roles WHERE role_name = 'student'");
    if ($stmt->rowCount() > 0) {
        $role = $stmt->fetch(PDO::FETCH_ASSOC);
        $role_id = $role['role_id'];
    } else {
        $role_id = 3; // Default student role ID
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, first_name, last_name, role_id, is_active) 
            VALUES (:username, :password, :email, :first_name, :last_name, :role_id, :is_active)
        ");
        
        $stmt->bindParam(':username', $test_username);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':email', $test_email);
        $stmt->bindParam(':first_name', $test_first_name);
        $stmt->bindParam(':last_name', $test_last_name);
        $stmt->bindParam(':role_id', $role_id);
        $stmt->bindParam(':is_active', $is_active);
        
        $test_email = $test_username . "@example.com";
        $test_first_name = "Test";
        $test_last_name = "User";
        $is_active = 1;
        
        $stmt->execute();
        
        echo "<p style='color: green;'>✓ Test user created successfully!</p>";
        echo "<p>Username: " . $test_username . "</p>";
        echo "<p>Password: " . $test_password . "</p>";
        echo "<p>You can use these credentials to test the login system.</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error creating test user: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Next Steps</h2>";
    echo "<p>Try logging in with the test user credentials above.</p>";
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
