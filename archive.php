<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to landing page
if (!isLoggedIn()) {
    header("location: landing.php");
    exit;
}

// Check if the user is a student or teacher
if (!isStudent() && !isTeacher()) {
    header("location: index.php");
    exit;
}

// Get archived courses based on user role
if (isTeacher()) {
    $archivedCourses = getArchivedCoursesByTeacher($_SESSION['user_id']);
} else {
    $archivedCourses = getArchivedCoursesByStudent($_SESSION['user_id']);
}

// Check if courses is an error message
if (is_string($archivedCourses)) {
    $error = $archivedCourses;
    $archivedCourses = [];
}

// Set page title
$page_title = "Archive";

// Include header
require_once 'includes/header.php';
?>

<!-- Custom CSS for course cards and buttons -->
<style>
    /* Course card styles */
    .course-card {
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .course-card-header {
        padding: 15px;
        background-color: var(--primary-color);
        color: white;
    }
    
    .course-card-title {
        font-size: 1.25rem;
        margin-bottom: 5px;
    }
    
    .course-card-subtitle {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 0;
    }
    
    .course-card-body {
        padding: 15px;
        flex-grow: 1;
    }
    
    .course-info {
        margin-top: 15px;
        font-size: 0.9rem;
        color: #666;
    }
    
    .course-info i {
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }
    
    .course-card-footer {
        padding: 10px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        justify-content: flex-end;
    }
    
    /* Cute button styles */
    .btn-cute {
        border-radius: 6px;
        padding: 6px 8px;
        font-weight: 500;
        font-size: 11px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 55px;
        height: 50px;
        border: none;
        margin: 0 3px;
        text-decoration: none;
    }
    
    .btn-cute i {
        font-size: 16px;
        margin-bottom: 3px;
    }
    
    .btn-cute-primary {
        background-color: #4285F4;
        color: white;
    }
    
    .btn-cute-primary:hover {
        background-color: #3367d6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .btn-cute-success {
        background-color: #34A853;
        color: white;
    }
    
    .btn-cute-success:hover {
        background-color: #2d8d46;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .course-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .empty-state {
        text-align: center;
        padding: 50px 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #adb5bd;
        margin-bottom: 20px;
    }
    
    .empty-state-text {
        color: #6c757d;
        margin-bottom: 20px;
    }
</style>

<!-- Archive Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Archived Courses</h1>
</div>

<?php if (isset($_SESSION['success'])): ?>
<div class="alert alert-success">
    <?php
    echo $_SESSION['success'];
    unset($_SESSION['success']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
<div class="alert alert-danger">
    <?php
    echo $_SESSION['error'];
    unset($_SESSION['error']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (count($archivedCourses) > 0): ?>
    <div class="course-grid">
        <?php foreach ($archivedCourses as $course): ?>
        <div class="card course-card">
            <div class="course-card-header">
                <h2 class="course-card-title"><?php echo htmlspecialchars($course['title']); ?></h2>
                <p class="course-card-subtitle">
                    <?php if (isset($course['creator_name'])): ?>
                    instructor
                    <?php endif; ?>
                </p>
            </div>
            <div class="course-card-body">
                <p><?php echo htmlspecialchars(substr($course['description'], 0, 100) . (strlen($course['description']) > 100 ? '...' : '')); ?></p>
                
                <div class="course-info">
                    <div>
                        <i class="far fa-calendar-alt"></i> Created: <?php echo date('F j, Y', strtotime($course['created_at'])); ?>
                    </div>
                    
                    <?php if (isset($course['semester']) && !empty($course['semester'])): ?>
                    <div class="mt-1">
                        <i class="fas fa-university"></i> <?php echo $course['semester'] === 'first' ? 'First Semester' : 'Second Semester'; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="mt-3">
                    <?php if (isset($course['created_by']) && $course['created_by'] == $_SESSION['user_id']): ?>
                    <span class="badge badge-primary">Creator</span>
                    <?php elseif (isTeacher() && isInstructor($_SESSION['user_id'], $course['course_id'])): ?>
                    <span class="badge badge-success">Instructor</span>
                    <?php endif; ?>
                    
                    <span class="badge badge-secondary"><i class="fas fa-archive mr-1"></i> Archived</span>
                </div>
            </div>
            <div class="course-card-footer">
                <a href="course_view_full.php?id=<?php echo $course['course_id']; ?>" class="btn-cute btn-cute-primary">
                    <i class="fas fa-folder-open"></i>
                    Open
                </a>
                
                <a href="course_action.php?action=restore&id=<?php echo $course['course_id']; ?>" 
                   class="btn-cute btn-cute-success"
                   onclick="return confirm('Are you sure you want to restore this course?');">
                    <i class="fas fa-undo-alt"></i>
                    Restore
                </a>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
<?php else: ?>
<div class="empty-state">
    <div class="empty-state-icon">
        <i class="fas fa-archive"></i>
    </div>
    <h3>No archived courses</h3>
    <p class="empty-state-text">Completed courses will appear here</p>
</div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
