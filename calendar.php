<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/calendar_events.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Get user's role
$role = $_SESSION['role'];

// Get current month and year
$month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Get filter options
$showCompleted = isset($_GET['show_completed']) && $_GET['show_completed'] == '1';
$showPastDue = isset($_GET['show_past_due']) && $_GET['show_past_due'] == '1';

// Validate month and year
if ($month < 1 || $month > 12) {
    $month = intval(date('m'));
}
if ($year < 2000 || $year > 2100) {
    $year = intval(date('Y'));
}

// Get the first day of the month
$firstDayOfMonth = mktime(0, 0, 0, $month, 1, $year);
$numberDays = date('t', $firstDayOfMonth);
$dateComponents = getdate($firstDayOfMonth);
$monthName = $dateComponents['month'];
$dayOfWeek = $dateComponents['wday'];

// Get previous and next month links
$prevMonth = $month - 1;
$prevYear = $year;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $month + 1;
$nextYear = $year;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}

// Get events for the current month from the database
$events = getMonthEvents($month, $year, $showCompleted, $showPastDue);

// Group events by date
$eventsByDate = [];
foreach ($events as $event) {
    $eventsByDate[$event['date']][] = $event;
}

// Set page title
$page_title = "Calendar";

// Add extra CSS for calendar
$extra_css = '
<style>
    .calendar {
        width: 100%;
        border-collapse: collapse;
    }
    .calendar th {
        background-color: #f1f3f4;
        color: #5f6368;
        font-weight: 500;
        text-align: center;
        padding: 10px;
        border: 1px solid #dadce0;
    }
    .calendar td {
        height: 120px;
        border: 1px solid #dadce0;
        vertical-align: top;
        padding: 5px;
    }
    .calendar-day {
        font-weight: 500;
        margin-bottom: 5px;
    }
    .calendar-event {
        background-color: #e8f0fe;
        border-left: 4px solid #1a73e8;
        padding: 5px;
        margin-bottom: 5px;
        font-size: 12px;
        border-radius: 4px;
        cursor: pointer;
    }
    .calendar-event.assignment {
        background-color: #e8f0fe;
        border-left-color: #1a73e8;
    }
    .calendar-event.quiz {
        background-color: #fce8e6;
        border-left-color: #ea4335;
    }
    .calendar-event.material {
        background-color: #e6f4ea;
        border-left-color: #34a853;
    }
    .calendar-event.system {
        background-color: #fff0e0;
        border-left-color: #ff9800;
    }
    .calendar-event.admin {
        background-color: #e0e0ff;
        border-left-color: #3f51b5;
    }
    .calendar-event.report {
        background-color: #f0e0ff;
        border-left-color: #9c27b0;
    }
    .calendar-event.meeting {
        background-color: #e0f7fa;
        border-left-color: #00bcd4;
    }
    .calendar-event.other {
        background-color: #f5f5f5;
        border-left-color: #9e9e9e;
    }
    .calendar-event.deadline {
        background-color: #ffebee;
        border-left-color: #f44336;
    }
    .calendar-event.personal {
        background-color: #e8f5e9;
        border-left-color: #4caf50;
    }
    .calendar-event.reminder {
        background-color: #fff8e1;
        border-left-color: #ffc107;
    }
    .calendar-event.study {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
    }
    .calendar-event.assignment {
        background-color: #e8eaf6;
        border-left-color: #3f51b5;
    }
    .calendar-event.exam {
        background-color: #f3e5f5;
        border-left-color: #9c27b0;
    }
    .calendar-event-title {
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .calendar-event-course {
        color: #5f6368;
        font-size: 11px;
    }
    .calendar-event-due, .calendar-event-points {
        color: #5f6368;
        font-size: 11px;
    }
    .calendar-event-source {
        font-size: 10px;
        margin-top: 3px;
        padding: 2px 4px;
        border-radius: 3px;
        display: inline-block;
    }
    .calendar-event-source.personal {
        background-color: #e8f5e9;
        color: #1b5e20;
    }
    .calendar-event-source.instructor {
        background-color: #e3f2fd;
        color: #0d47a1;
    }
    .calendar-event-source.system {
        background-color: #f5f5f5;
        color: #616161;
    }
    .calendar-event-creator {
        font-size: 10px;
        color: #5f6368;
        font-style: italic;
        margin-top: 2px;
    }

    /* Activity styling */
    .calendar-event.activity {
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    .calendar-event.assignment {
        background-color: #e8f0fe;
        border-left-color: #4285f4;
    }
    .calendar-event.quiz {
        background-color: #fce8e6;
        border-left-color: #ea4335;
    }
    .calendar-event.material {
        background-color: #e6f4ea;
        border-left-color: #34a853;
    }
    .calendar-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    .calendar-title {
        font-size: 24px;
        font-weight: 500;
    }
    .calendar-nav {
        display: flex;
        align-items: center;
    }
    .calendar-nav-btn {
        background-color: transparent;
        border: none;
        color: #5f6368;
        font-size: 24px;
        cursor: pointer;
        padding: 0 10px;
    }
    .calendar-nav-btn:hover {
        color: #1a73e8;
    }
    .today {
        background-color: #e8f0fe;
    }
    .other-month {
        background-color: #f8f9fa;
        color: #bdc1c6;
    }

    /* Done event styling */
    .calendar-event.done {
        opacity: 0.7;
        text-decoration: line-through;
        background-color: #f8f9fa !important;
        border-left: 3px solid #28a745 !important;
    }

    /* Event actions styling */
    .calendar-event-actions {
        display: none;
        margin-top: 5px;
        text-align: right;
    }

    .calendar-event:hover .calendar-event-actions {
        display: block;
    }

    .btn-sm {
        padding: 0.15rem 0.3rem;
        font-size: 0.75rem;
    }
</style>';

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <?php if (isAdmin()): ?>
        <h1>Admin Calendar</h1>
        <p class="text-muted">System events and administrative schedule</p>
        <?php elseif (isTeacher()): ?>
        <h1>Instructor Calendar</h1>
        <p class="text-muted">Manage course deadlines, events, and important dates</p>
        <?php else: ?>
        <h1>Student Calendar</h1>
        <p class="text-muted">Track your assignments, exams, study sessions, and course events</p>
        <?php endif; ?>
    </div>
    <div>
        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addEventModal">
            <i class="fas fa-plus"></i> Add Event
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="calendar-controls">
            <div class="calendar-title">
                <?php echo $monthName . ' ' . $year; ?>
            </div>
            <div class="calendar-nav">
                <a href="?month=<?php echo $prevMonth; ?>&year=<?php echo $prevYear; ?>&show_completed=<?php echo $showCompleted ? '1' : '0'; ?>&show_past_due=<?php echo $showPastDue ? '1' : '0'; ?>" class="calendar-nav-btn">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <a href="?month=<?php echo date('m'); ?>&year=<?php echo date('Y'); ?>&show_completed=<?php echo $showCompleted ? '1' : '0'; ?>&show_past_due=<?php echo $showPastDue ? '1' : '0'; ?>" class="btn btn-outline-primary mx-2">Today</a>
                <a href="?month=<?php echo $nextMonth; ?>&year=<?php echo $nextYear; ?>&show_completed=<?php echo $showCompleted ? '1' : '0'; ?>&show_past_due=<?php echo $showPastDue ? '1' : '0'; ?>" class="calendar-nav-btn">
                    <i class="fas fa-chevron-right"></i>
                </a>

                <div class="calendar-filters ml-3">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="showCompleted" <?php echo $showCompleted ? 'checked' : ''; ?> onchange="updateFilters()">
                        <label class="form-check-label" for="showCompleted">Show completed</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="showPastDue" <?php echo $showPastDue ? 'checked' : ''; ?> onchange="updateFilters()">
                        <label class="form-check-label" for="showPastDue">Show past due</label>
                    </div>
                </div>
            </div>
        </div>

        <table class="calendar">
            <thead>
                <tr>
                    <th>Sunday</th>
                    <th>Monday</th>
                    <th>Tuesday</th>
                    <th>Wednesday</th>
                    <th>Thursday</th>
                    <th>Friday</th>
                    <th>Saturday</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <?php
                    // Fill in the days of the week
                    // Add empty cells for days before the first day of the month
                    for ($i = 0; $i < $dayOfWeek; $i++) {
                        $prevMonthDay = date('t', mktime(0, 0, 0, $prevMonth, 1, $prevYear)) - $dayOfWeek + $i + 1;
                        echo '<td class="other-month"><div class="calendar-day">' . $prevMonthDay . '</div></td>';
                    }

                    // Fill in the days of the month
                    for ($day = 1; $day <= $numberDays; $day++) {
                        $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
                        $isToday = ($date == date('Y-m-d'));
                        $cellClass = $isToday ? 'today' : '';

                        echo '<td class="' . $cellClass . '">';
                        echo '<div class="calendar-day">' . $day . '</div>';

                        // Display events for this day
                        if (isset($eventsByDate[$date])) {
                            foreach ($eventsByDate[$date] as $event) {
                                // Add data attributes for event details (for editing)
                                $dataAttrs = ' data-id="' . (isset($event['id']) ? $event['id'] : '0') . '"'
                                          . ' data-title="' . htmlspecialchars($event['title']) . '"'
                                          . ' data-date="' . $date . '"'
                                          . ' data-type="' . $event['type'] . '"'
                                          . ' data-description="' . (isset($event['description']) ? htmlspecialchars($event['description']) : '') . '"'
                                          . ' data-status="' . (isset($event['status']) ? $event['status'] : 'pending') . '"';

                                // Add status class for styling
                                $statusClass = isset($event['status']) && $event['status'] === 'done' ? ' done' : '';

                                // Determine if this is an activity or a regular calendar event
                                $source = isset($event['source']) ? $event['source'] : 'calendar';
                                $sourceClass = ' ' . $source;

                                // Add event source indicator
                                $eventSourceIndicator = '';
                                if (isset($event['event_source'])) {
                                    if ($event['event_source'] === 'course') {
                                        $eventSourceIndicator = '<div class="calendar-event-source instructor"><i class="fas fa-chalkboard-teacher"></i> From instructor</div>';
                                    } elseif ($event['event_source'] === 'personal') {
                                        $eventSourceIndicator = '<div class="calendar-event-source personal"><i class="fas fa-user"></i> Personal</div>';
                                    } elseif ($event['event_source'] === 'instructor') {
                                        $eventSourceIndicator = '<div class="calendar-event-source instructor"><i class="fas fa-chalkboard-teacher"></i> Instructor</div>';
                                    } elseif ($event['event_source'] === 'system') {
                                        $eventSourceIndicator = '<div class="calendar-event-source system"><i class="fas fa-cog"></i> System</div>';
                                    }
                                }

                                // Add due date and points for activities
                                $extraInfo = '';
                                if ($source === 'activity' && isset($event['due_date'])) {
                                    $dueTime = date('g:i A', strtotime($event['due_date']));
                                    $extraInfo .= '<div class="calendar-event-due">Due: ' . $dueTime . '</div>';

                                    if (isset($event['points']) && $event['points'] > 0) {
                                        $extraInfo .= '<div class="calendar-event-points">' . $event['points'] . ' points</div>';
                                    }
                                }

                                // Add creator name if available
                                $creatorInfo = '';
                                if (isset($event['creator_name']) && !empty($event['creator_name'])) {
                                    $creatorInfo = '<div class="calendar-event-creator">Added by: ' . htmlspecialchars($event['creator_name']) . '</div>';
                                }

                                echo '<div class="calendar-event ' . $event['type'] . $statusClass . $sourceClass . '"' . $dataAttrs . ' data-source="' . $source . '">';
                                echo '<div class="calendar-event-title">' . htmlspecialchars($event['title']) . '</div>';

                                // Show course title from database if available
                                if (isset($event['course_title']) && !empty($event['course_title'])) {
                                    echo '<div class="calendar-event-course">' . htmlspecialchars($event['course_title']) . '</div>';
                                } else {
                                    echo '<div class="calendar-event-course">' . htmlspecialchars($event['course']) . '</div>';
                                }

                                echo $extraInfo;
                                echo $eventSourceIndicator;
                                echo $creatorInfo;

                                // Add action buttons
                                echo '<div class="calendar-event-actions">';

                                // For activities, add a view button that links to the activity
                                if ($source === 'activity') {
                                    $activityUrl = '';
                                    switch ($event['type']) {
                                        case 'assignment':
                                            $activityUrl = 'assignment_view.php?id=' . $event['id'];
                                            break;
                                        case 'quiz':
                                            $activityUrl = 'quiz_view.php?id=' . $event['id'];
                                            break;
                                        default:
                                            $activityUrl = 'activity_view.php?id=' . $event['id'];
                                            break;
                                    }
                                    echo '<a href="' . $activityUrl . '" class="btn btn-sm btn-outline-info ml-1" title="View Activity"><i class="fas fa-eye"></i></a>';
                                } else {
                                    // Only show edit/delete buttons if:
                                    // 1. The user is an admin, OR
                                    // 2. The user is a teacher, OR
                                    // 3. The user is a student AND the event is their own (not from an instructor)
                                    $canEditDelete = isAdmin() ||
                                                    isTeacher() ||
                                                    (!isset($event['event_source']) || $event['event_source'] === 'personal');

                                    if ($canEditDelete) {
                                        // Edit button for calendar events
                                        echo '<button type="button" class="btn btn-sm btn-outline-primary ml-1 edit-event-btn" title="Edit Event" onclick="editEvent(' . $event['id'] . ', \'' . htmlspecialchars($event['title'], ENT_QUOTES) . '\', \'' . $date . '\', \'' . $event['type'] . '\', \'' . htmlspecialchars(isset($event['description']) ? $event['description'] : '', ENT_QUOTES) . '\')"><i class="fas fa-edit"></i></button>';

                                        // Delete button for calendar events
                                        echo '<form method="post" action="calendar_actions.php" class="d-inline ml-1">';
                                        echo '<input type="hidden" name="event_id" value="' . $event['id'] . '">';
                                        echo '<input type="hidden" name="delete_event" value="1">';
                                        echo '<button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Event" onclick="return confirm(\'Are you sure you want to delete this event?\');"><i class="fas fa-trash"></i></button>';
                                        echo '</form>';
                                    } else {
                                        // For students viewing instructor events, just show a view button
                                        echo '<button type="button" class="btn btn-sm btn-outline-info ml-1" title="View Event Details" onclick="viewEventDetails(' . $event['id'] . ', \'' . htmlspecialchars($event['title'], ENT_QUOTES) . '\', \'' . $date . '\', \'' . $event['type'] . '\', \'' . htmlspecialchars(isset($event['description']) ? $event['description'] : '', ENT_QUOTES) . '\')"><i class="fas fa-eye"></i></button>';
                                    }
                                }

                                echo '</div>'; // End of actions

                                echo '</div>'; // End of event
                            }
                        }

                        echo '</td>';

                        // Start a new row if it's the end of the week
                        if (($day + $dayOfWeek) % 7 == 0) {
                            echo '</tr><tr>';
                        }
                    }

                    // Fill in the days after the last day of the month
                    $remainingCells = 7 - (($numberDays + $dayOfWeek) % 7);
                    if ($remainingCells < 7) {
                        for ($i = 1; $i <= $remainingCells; $i++) {
                            echo '<td class="other-month"><div class="calendar-day">' . $i . '</div></td>';
                        }
                    }
                    ?>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Event Modal -->
<div class="modal fade" id="addEventModal" tabindex="-1" role="dialog" aria-labelledby="addEventModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEventModalLabel">Add New Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addEventForm" action="calendar_actions.php" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="event_title">Event Title</label>
                        <input type="text" class="form-control" id="event_title" name="event_title" required>
                    </div>
                    <div class="form-group">
                        <label for="event_date">Date</label>
                        <input type="date" class="form-control" id="event_date" name="event_date" required>
                    </div>
                    <div class="form-group">
                        <label for="event_type">Event Type</label>
                        <select class="form-control" id="event_type" name="event_type" required>
                            <?php if (isAdmin()): ?>
                                <option value="system">System</option>
                                <option value="admin">Administrative</option>
                                <option value="report">Report</option>
                                <option value="meeting">Meeting</option>
                                <option value="other">Other</option>
                            <?php elseif (isTeacher()): ?>
                                <option value="deadline">Deadline</option>
                                <option value="meeting">Meeting</option>
                                <option value="reminder">Reminder</option>
                                <option value="other">Other</option>
                            <?php else: ?>
                                <option value="study">Study Session</option>
                                <option value="assignment">Assignment Work</option>
                                <option value="exam">Exam Preparation</option>
                                <option value="reminder">Reminder</option>
                                <option value="personal">Personal</option>
                                <option value="other">Other</option>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="event_description">Description</label>
                        <textarea class="form-control" id="event_description" name="event_description" rows="3"></textarea>
                    </div>
                    <?php if (isTeacher()): ?>
                    <div class="form-group">
                        <label for="event_course">Course (Optional)</label>
                        <select class="form-control" id="event_course" name="event_course">
                            <option value="">-- Select Course --</option>
                            <?php
                            // Get courses taught by the instructor
                            $courses = getInstructorCourses($_SESSION['user_id']);
                            foreach ($courses as $course) {
                                echo '<option value="' . $course['course_id'] . '">' . htmlspecialchars($course['title']) . ' (' . htmlspecialchars($course['class_code']) . ')</option>';
                            }
                            ?>
                        </select>
                        <small class="form-text text-muted">If selected, this event will be visible to students enrolled in this course.</small>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_event" class="btn btn-primary">Add Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View/Edit Event Modal -->
<div class="modal fade" id="viewEventModal" tabindex="-1" role="dialog" aria-labelledby="viewEventModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewEventModalLabel">Event Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editEventForm" action="calendar_actions.php" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_event_id" name="event_id">
                    <div class="form-group">
                        <label for="edit_event_title">Event Title</label>
                        <input type="text" class="form-control" id="edit_event_title" name="event_title" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_event_date">Date</label>
                        <input type="date" class="form-control" id="edit_event_date" name="event_date" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_event_type">Event Type</label>
                        <select class="form-control" id="edit_event_type" name="event_type" required>
                            <?php if (isAdmin()): ?>
                                <option value="system">System</option>
                                <option value="admin">Administrative</option>
                                <option value="report">Report</option>
                                <option value="meeting">Meeting</option>
                                <option value="other">Other</option>
                            <?php elseif (isTeacher()): ?>
                                <option value="deadline">Deadline</option>
                                <option value="meeting">Meeting</option>
                                <option value="reminder">Reminder</option>
                                <option value="other">Other</option>
                            <?php else: ?>
                                <option value="study">Study Session</option>
                                <option value="assignment">Assignment Work</option>
                                <option value="exam">Exam Preparation</option>
                                <option value="reminder">Reminder</option>
                                <option value="personal">Personal</option>
                                <option value="other">Other</option>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_event_description">Description</label>
                        <textarea class="form-control" id="edit_event_description" name="event_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger mr-auto" id="deleteEventBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" name="update_event" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Event Confirmation Modal -->
<div class="modal fade" id="deleteEventModal" tabindex="-1" role="dialog" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this event? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <form action="calendar_actions.php" method="post">
                    <input type="hidden" id="delete_event_id" name="event_id">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_event" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to edit an event (called directly from the button)
    function editEvent(eventId, eventTitle, eventDate, eventType, eventDescription) {
        console.log("Edit function called for event ID: " + eventId);

        // Set modal title to Edit Event
        $('#viewEventModalLabel').text('Edit Event');

        // Populate the modal with event data
        $('#edit_event_id').val(eventId);
        $('#edit_event_title').val(eventTitle);
        $('#edit_event_date').val(eventDate);
        $('#edit_event_type').val(eventType);
        $('#edit_event_description').val(eventDescription);

        // Enable form fields for editing
        $('#edit_event_title').prop('readonly', false);
        $('#edit_event_date').prop('readonly', false);
        $('#edit_event_type').prop('disabled', false);
        $('#edit_event_description').prop('readonly', false);

        // Show edit controls
        $('#deleteEventBtn').show();
        $('button[name="update_event"]').show();

        // Show the modal
        $('#viewEventModal').modal('show');
    }

    // Function to view event details without edit capabilities (for instructor events viewed by students)
    function viewEventDetails(eventId, eventTitle, eventDate, eventType, eventDescription) {
        console.log("View function called for event ID: " + eventId);

        // Set modal title to View Event
        $('#viewEventModalLabel').text('View Event');

        // Populate the modal with event data
        $('#edit_event_id').val(eventId);
        $('#edit_event_title').val(eventTitle);
        $('#edit_event_date').val(eventDate);
        $('#edit_event_type').val(eventType);
        $('#edit_event_description').val(eventDescription);

        // Hide edit controls
        $('#deleteEventBtn').hide();
        $('button[name="update_event"]').hide();

        // Make form fields readonly
        $('#edit_event_title').prop('readonly', true);
        $('#edit_event_date').prop('readonly', true);
        $('#edit_event_type').prop('disabled', true);
        $('#edit_event_description').prop('readonly', true);

        // Show the modal
        $('#viewEventModal').modal('show');
    }

    // Function to update filters and reload the page
    function updateFilters() {
        var showCompleted = document.getElementById('showCompleted').checked ? '1' : '0';
        var showPastDue = document.getElementById('showPastDue').checked ? '1' : '0';

        // Get current URL parameters
        var urlParams = new URLSearchParams(window.location.search);

        // Update or add filter parameters
        urlParams.set('show_completed', showCompleted);
        urlParams.set('show_past_due', showPastDue);

        // Redirect to the updated URL
        window.location.href = '?' + urlParams.toString();
    }

    $(document).ready(function() {
        // This is a backup handler in case the onclick doesn't work
        $(document).on('click', '.edit-event-btn', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            var eventContainer = $(this).closest('.calendar-event');
            var eventId = eventContainer.data('id');
            var eventTitle = eventContainer.data('title');
            var eventDate = eventContainer.data('date');
            var eventType = eventContainer.data('type');
            var eventDescription = eventContainer.data('description');

            console.log("Edit button clicked for event ID: " + eventId);

            // Call the edit function
            editEvent(eventId, eventTitle, eventDate, eventType, eventDescription);
        });

        // Handle delete button click
        $('#deleteEventBtn').click(function() {
            var eventId = $('#edit_event_id').val();
            $('#delete_event_id').val(eventId);
            $('#viewEventModal').modal('hide');
            $('#deleteEventModal').modal('show');
        });

        // No longer needed - removed status button functionality

        // Prevent event bubbling for all calendar event actions
        $(document).on('click', '.calendar-event-actions, .calendar-event-actions *, .calendar-event form', function(e) {
            e.stopPropagation();
        });

        // Make the entire calendar event clickable to show details
        $(document).on('click', '.calendar-event', function(e) {
            // Only trigger if the click was directly on the event, not on a button or form
            if ($(e.target).closest('.calendar-event-actions').length === 0) {
                var eventId = $(this).data('id');
                var eventTitle = $(this).data('title');
                var eventDate = $(this).data('date');
                var eventType = $(this).data('type');
                var eventDescription = $(this).data('description');
                var eventSource = $(this).data('source');
                var isInstructorEvent = $(this).find('.calendar-event-source.instructor').length > 0;

                // Determine if this is an event the student can edit
                var canEditDelete = <?php echo isAdmin() ? 'true' : 'false'; ?> ||
                                   <?php echo isTeacher() ? 'true' : 'false'; ?> ||
                                   (!isInstructorEvent && eventSource !== 'course');

                // Use the appropriate function based on edit permissions
                if (canEditDelete) {
                    editEvent(eventId, eventTitle, eventDate, eventType, eventDescription);
                } else {
                    viewEventDetails(eventId, eventTitle, eventDate, eventType, eventDescription);
                }
            }
        });
    });
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
