<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Test File Viewer";

// Include header
require_once 'includes/header.php';
?>

<div class="container">
    <h1 class="mb-4">Test File Viewer</h1>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">File Viewer Test</h5>
        </div>
        <div class="card-body">
            <p>This page tests the file viewer functionality. Click on the links below to test different file types:</p>
            
            <div class="list-group">
                <a href="file_viewer.php?file=uploads/announcements/1/test.txt" class="list-group-item list-group-item-action" target="_blank">
                    <i class="fas fa-file-alt mr-2"></i> Test Text File
                </a>
                <a href="file_viewer.php?file=uploads/announcements/1/test.pdf" class="list-group-item list-group-item-action" target="_blank">
                    <i class="fas fa-file-pdf mr-2"></i> Test PDF File
                </a>
                <a href="file_viewer.php?file=uploads/announcements/1/test.jpg" class="list-group-item list-group-item-action" target="_blank">
                    <i class="fas fa-file-image mr-2"></i> Test Image File
                </a>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Create Test Files</h5>
        </div>
        <div class="card-body">
            <p>Click the button below to create test files in the uploads directory:</p>
            
            <form action="" method="post">
                <button type="submit" name="create_test_files" class="btn btn-primary">Create Test Files</button>
            </form>
            
            <?php
            // Process form submission
            if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['create_test_files'])) {
                // Create uploads directory if it doesn't exist
                $uploadsDir = 'uploads';
                if (!file_exists($uploadsDir)) {
                    mkdir($uploadsDir, 0777, true);
                }
                
                // Create announcements directory if it doesn't exist
                $announcementsDir = 'uploads/announcements';
                if (!file_exists($announcementsDir)) {
                    mkdir($announcementsDir, 0777, true);
                }
                
                // Create test announcement directory if it doesn't exist
                $testAnnouncementDir = 'uploads/announcements/1';
                if (!file_exists($testAnnouncementDir)) {
                    mkdir($testAnnouncementDir, 0777, true);
                }
                
                // Create test text file
                $textFile = $testAnnouncementDir . '/test.txt';
                file_put_contents($textFile, "This is a test text file.\nCreated for testing the file viewer functionality.");
                
                // Create test HTML file
                $htmlFile = $testAnnouncementDir . '/test.html';
                file_put_contents($htmlFile, "<html><body><h1>Test HTML File</h1><p>This is a test HTML file.</p></body></html>");
                
                // Create a simple image file (1x1 pixel transparent GIF)
                $imageFile = $testAnnouncementDir . '/test.gif';
                file_put_contents($imageFile, base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'));
                
                echo '<div class="alert alert-success mt-3">Test files created successfully!</div>';
                
                // List created files
                echo '<div class="mt-3">';
                echo '<h6>Created Files:</h6>';
                echo '<ul>';
                echo '<li>' . $textFile . ' - ' . (file_exists($textFile) ? 'Success' : 'Failed') . '</li>';
                echo '<li>' . $htmlFile . ' - ' . (file_exists($htmlFile) ? 'Success' : 'Failed') . '</li>';
                echo '<li>' . $imageFile . ' - ' . (file_exists($imageFile) ? 'Success' : 'Failed') . '</li>';
                echo '</ul>';
                echo '</div>';
            }
            ?>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
