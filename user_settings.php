<?php
/**
 * User Settings Page
 *
 * This page allows students and instructors to manage their settings.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Process form submissions
$message = '';
$messageType = '';

// Handle settings update
if (isset($_POST['update_settings'])) {
    // Get theme setting
    $theme = isset($_POST['theme']) ? $_POST['theme'] : 'light';

    // Get notification settings
    // General settings
    $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;

    // Student settings
    $assignmentReminders = isset($_POST['assignment_reminders']) ? 1 : 0;
    $courseAnnouncements = isset($_POST['course_announcements']) ? 1 : 0;
    $dueDateReminders = isset($_POST['due_date_reminders']) ? 1 : 0;
    $gradeUpdates = isset($_POST['grade_updates']) ? 1 : 0;

    // Instructor settings
    $studentSubmissions = isset($_POST['student_submissions']) ? 1 : 0;
    $enrollmentRequests = isset($_POST['enrollment_requests']) ? 1 : 0;
    $courseActivity = isset($_POST['course_activity']) ? 1 : 0;

    // Comment settings
    $commentsOnPosts = isset($_POST['comments_on_posts']) ? 1 : 0;
    $commentsMentions = isset($_POST['comments_mentions']) ? 1 : 0;
    $privateComments = isset($_POST['private_comments']) ? 1 : 0;

    // Calendar settings
    $eventCreation = isset($_POST['event_creation']) ? 1 : 0;
    $eventUpdates = isset($_POST['event_updates']) ? 1 : 0;
    $eventReminders = isset($_POST['event_reminders']) ? 1 : 0;
    $statusChanges = isset($_POST['status_changes']) ? 1 : 0;
    $systemAnnouncements = isset($_POST['system_announcements']) ? 1 : 0;

    try {
        // Save theme preference as a cookie
        setcookie('theme', $theme, time() + (86400 * 30), "/"); // 30 days expiration

        // Update notification settings in the database
        global $pdo;

        // Check if user_notification_settings table exists
        $tableExists = false;
        $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
        if ($checkTable && $checkTable->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, create it
        if (!$tableExists) {
            $createTable = "CREATE TABLE user_notification_settings (
                setting_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                email_notifications TINYINT(1) DEFAULT 1,
                assignment_reminders TINYINT(1) DEFAULT 1,
                course_announcements TINYINT(1) DEFAULT 1,
                due_date_reminders TINYINT(1) DEFAULT 1,
                comments_on_posts TINYINT(1) DEFAULT 1,
                comments_mentions TINYINT(1) DEFAULT 1,
                private_comments TINYINT(1) DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )";

            $pdo->query($createTable);
        }

        // Check if user has settings
        $checkSettings = $pdo->prepare("SELECT setting_id FROM user_notification_settings WHERE user_id = :user_id");
        $checkSettings->execute([':user_id' => $_SESSION['user_id']]);

        if ($checkSettings->rowCount() > 0) {
            // Update existing settings
            $updateSettings = $pdo->prepare("UPDATE user_notification_settings SET
                email_notifications = :email_notifications,
                theme = :theme,
                assignment_reminders = :assignment_reminders,
                course_announcements = :course_announcements,
                due_date_reminders = :due_date_reminders,
                grade_updates = :grade_updates,
                student_submissions = :student_submissions,
                enrollment_requests = :enrollment_requests,
                course_activity = :course_activity,
                comments_on_posts = :comments_on_posts,
                comments_mentions = :comments_mentions,
                private_comments = :private_comments,
                event_creation = :event_creation,
                event_updates = :event_updates,
                event_reminders = :event_reminders,
                status_changes = :status_changes,
                system_announcements = :system_announcements
                WHERE user_id = :user_id");

            $updateSettings->execute([
                ':email_notifications' => $emailNotifications,
                ':theme' => $theme,
                ':assignment_reminders' => $assignmentReminders,
                ':course_announcements' => $courseAnnouncements,
                ':due_date_reminders' => $dueDateReminders,
                ':grade_updates' => $gradeUpdates,
                ':student_submissions' => $studentSubmissions,
                ':enrollment_requests' => $enrollmentRequests,
                ':course_activity' => $courseActivity,
                ':comments_on_posts' => $commentsOnPosts,
                ':comments_mentions' => $commentsMentions,
                ':private_comments' => $privateComments,
                ':event_creation' => $eventCreation,
                ':event_updates' => $eventUpdates,
                ':event_reminders' => $eventReminders,
                ':status_changes' => $statusChanges,
                ':system_announcements' => $systemAnnouncements,
                ':user_id' => $_SESSION['user_id']
            ]);
        } else {
            // Insert new settings
            $insertSettings = $pdo->prepare("INSERT INTO user_notification_settings
                (user_id, email_notifications, theme,
                 assignment_reminders, course_announcements, due_date_reminders, grade_updates,
                 student_submissions, enrollment_requests, course_activity,
                 comments_on_posts, comments_mentions, private_comments,
                 event_creation, event_updates, event_reminders, status_changes, system_announcements)
                VALUES (:user_id, :email_notifications, :theme,
                 :assignment_reminders, :course_announcements, :due_date_reminders, :grade_updates,
                 :student_submissions, :enrollment_requests, :course_activity,
                 :comments_on_posts, :comments_mentions, :private_comments,
                 :event_creation, :event_updates, :event_reminders, :status_changes, :system_announcements)");

            $insertSettings->execute([
                ':user_id' => $_SESSION['user_id'],
                ':email_notifications' => $emailNotifications,
                ':theme' => $theme,
                ':assignment_reminders' => $assignmentReminders,
                ':course_announcements' => $courseAnnouncements,
                ':due_date_reminders' => $dueDateReminders,
                ':grade_updates' => $gradeUpdates,
                ':student_submissions' => $studentSubmissions,
                ':enrollment_requests' => $enrollmentRequests,
                ':course_activity' => $courseActivity,
                ':comments_on_posts' => $commentsOnPosts,
                ':comments_mentions' => $commentsMentions,
                ':private_comments' => $privateComments,
                ':event_creation' => $eventCreation,
                ':event_updates' => $eventUpdates,
                ':event_reminders' => $eventReminders,
                ':status_changes' => $statusChanges,
                ':system_announcements' => $systemAnnouncements
            ]);
        }

        $message = 'Settings updated successfully';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Database error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle password change
if (isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];

    // Validate input
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = 'All password fields are required';
        $messageType = 'danger';
    } else if ($newPassword !== $confirmPassword) {
        $message = 'New password and confirm password do not match';
        $messageType = 'danger';
    } else {
        try {
            // Verify current password
            $stmt = $pdo->prepare("SELECT password FROM users WHERE user_id = :user_id");
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->execute();
            $user = $stmt->fetch();

            if (!$user || !password_verify($currentPassword, $user['password'])) {
                $message = 'Current password is incorrect';
                $messageType = 'danger';
            } else {
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

                $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :user_id");
                $stmt->bindParam(':password', $hashedPassword);
                $stmt->bindParam(':user_id', $_SESSION['user_id']);
                $stmt->execute();

                $message = 'Password updated successfully';
                $messageType = 'success';
            }
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get notification settings from the database
$notificationSettings = [
    // General settings
    'email_notifications' => 1,
    'theme' => 'light',

    // Student settings
    'assignment_reminders' => 1,
    'course_announcements' => 1,
    'due_date_reminders' => 1,
    'grade_updates' => 1,

    // Instructor settings
    'student_submissions' => 1,
    'enrollment_requests' => 1,
    'course_activity' => 1,

    // Comment settings
    'comments_on_posts' => 1,
    'comments_mentions' => 1,
    'private_comments' => 1,

    // Calendar settings
    'event_creation' => 1,
    'event_updates' => 1,
    'event_reminders' => 1,
    'status_changes' => 1,
    'system_announcements' => 1
];

try {
    // Check if user_notification_settings table exists
    $tableExists = false;
    $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($checkTable && $checkTable->rowCount() > 0) {
        $tableExists = true;
    }

    if (!$tableExists) {
        // Create the table using the setup script
        include_once 'setup_notification_settings.php';
    }

    // Get user's notification settings
    $getSettings = $pdo->prepare("SELECT * FROM user_notification_settings WHERE user_id = :user_id");
    $getSettings->execute([':user_id' => $_SESSION['user_id']]);
    $userSettings = $getSettings->fetch(PDO::FETCH_ASSOC);

    if ($userSettings) {
        $notificationSettings = [
            // General settings
            'email_notifications' => $userSettings['email_notifications'],
            'theme' => $userSettings['theme'],

            // Student settings
            'assignment_reminders' => $userSettings['assignment_reminders'],
            'course_announcements' => $userSettings['course_announcements'],
            'due_date_reminders' => $userSettings['due_date_reminders'],
            'grade_updates' => $userSettings['grade_updates'],

            // Instructor settings
            'student_submissions' => $userSettings['student_submissions'],
            'enrollment_requests' => $userSettings['enrollment_requests'],
            'course_activity' => $userSettings['course_activity'],

            // Comment settings
            'comments_on_posts' => $userSettings['comments_on_posts'],
            'comments_mentions' => $userSettings['comments_mentions'],
            'private_comments' => $userSettings['private_comments'],

            // Calendar settings
            'event_creation' => $userSettings['event_creation'],
            'event_updates' => $userSettings['event_updates'],
            'event_reminders' => $userSettings['event_reminders'],
            'status_changes' => $userSettings['status_changes'],
            'system_announcements' => $userSettings['system_announcements']
        ];
    } else {
        // Insert default settings for this user
        $insertSql = "INSERT INTO user_notification_settings
                (user_id, email_notifications, theme,
                 assignment_reminders, course_announcements, due_date_reminders, grade_updates,
                 student_submissions, enrollment_requests, course_activity,
                 user_registrations, course_creation, account_changes, error_alerts, weekly_reports,
                 comments_on_posts, comments_mentions, private_comments,
                 event_creation, event_updates, event_reminders, status_changes, system_announcements)
                VALUES (:user_id, 1, 'light', 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)";
        $insertStmt = $pdo->prepare($insertSql);
        $insertStmt->execute([':user_id' => $_SESSION['user_id']]);
    }
} catch (PDOException $e) {
    // Just use default settings if there's an error
    error_log("Error getting notification settings: " . $e->getMessage());
}

// Page title
$page_title = 'User Settings';

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Settings</h1>
        </div>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body">
                    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="appearance-tab" data-toggle="tab" href="#appearance" role="tab">Appearance</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="notifications-tab" data-toggle="tab" href="#notifications" role="tab">Notifications</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="security-tab" data-toggle="tab" href="#security" role="tab">Security</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="settingsTabsContent">
                        <!-- Appearance Tab -->
                        <div class="tab-pane fade show active" id="appearance" role="tabpanel">
                            <form action="user_settings.php" method="post">
                                <h4 class="mb-4">Theme Settings</h4>
                                <div class="form-group row mb-4 pb-3 border-bottom">
                                    <label class="col-sm-3 col-form-label">Theme</label>
                                    <div class="col-sm-9">
                                        <div class="form-group">
                                            <div class="d-flex mt-2">
                                                <div class="custom-control custom-radio mr-4">
                                                    <input type="radio" id="lightTheme" name="theme" value="light" class="custom-control-input theme-switch" <?php echo (!isset($_COOKIE['theme']) || $_COOKIE['theme'] === 'light') ? 'checked' : ''; ?>>
                                                    <label class="custom-control-label" for="lightTheme">Light Mode</label>
                                                </div>
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" id="darkTheme" name="theme" value="dark" class="custom-control-input theme-switch" <?php echo (isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark') ? 'checked' : ''; ?>>
                                                    <label class="custom-control-label" for="darkTheme">Dark Mode</label>
                                                </div>
                                            </div>
                                            <small class="text-muted">Choose between light and dark theme for the application</small>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="update_settings" value="1">
                                <button type="submit" class="btn btn-primary">Save Appearance Settings</button>
                            </form>
                        </div>

                        <!-- Notifications Tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <form action="user_settings.php" method="post">
                                <h4 class="mb-4">Notification Preferences</h4>

                                <div class="mb-4">
                                    <h5>Email</h5>
                                    <p class="text-muted small">These settings apply to the notifications you get by email</p>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Allow email notifications</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="emailNotifications" name="email_notifications" <?php echo $notificationSettings['email_notifications'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="emailNotifications"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if (isStudent()): ?>
                                <div class="mb-4">
                                    <h5>Student Notifications</h5>
                                    <p class="text-muted small">Control which student-specific notifications you receive</p>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Assignment reminders</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="assignmentReminders" name="assignment_reminders" <?php echo $notificationSettings['assignment_reminders'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="assignmentReminders"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Course announcements</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="courseAnnouncements" name="course_announcements" <?php echo $notificationSettings['course_announcements'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="courseAnnouncements"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Due date reminders</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="dueDateReminders" name="due_date_reminders" <?php echo $notificationSettings['due_date_reminders'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="dueDateReminders"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Grade updates</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="gradeUpdates" name="grade_updates" <?php echo $notificationSettings['grade_updates'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="gradeUpdates"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (isTeacher()): ?>
                                <div class="mb-4">
                                    <h5>Instructor Notifications</h5>
                                    <p class="text-muted small">Control which instructor-specific notifications you receive</p>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Student submissions</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="studentSubmissions" name="student_submissions" <?php echo $notificationSettings['student_submissions'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="studentSubmissions"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Enrollment requests</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enrollmentRequests" name="enrollment_requests" <?php echo $notificationSettings['enrollment_requests'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="enrollmentRequests"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Course activity</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="courseActivity" name="course_activity" <?php echo $notificationSettings['course_activity'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="courseActivity"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="mb-4">
                                    <h5>Comments</h5>
                                    <p class="text-muted small">Control which comment notifications you receive</p>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Comments on your posts</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="commentsOnPosts" name="comments_on_posts" <?php echo $notificationSettings['comments_on_posts'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="commentsOnPosts"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Comments that mention you</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="commentsMentions" name="comments_mentions" <?php echo $notificationSettings['comments_mentions'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="commentsMentions"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Private comments on work</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="privateComments" name="private_comments" <?php echo $notificationSettings['private_comments'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="privateComments"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5>Calendar Notifications</h5>
                                    <p class="text-muted small">Control which calendar event notifications you receive</p>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Event creation notifications</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="eventCreation" name="event_creation" <?php echo $notificationSettings['event_creation'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="eventCreation"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Event update notifications</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="eventUpdates" name="event_updates" <?php echo $notificationSettings['event_updates'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="eventUpdates"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Event reminder notifications</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="eventReminders" name="event_reminders" <?php echo $notificationSettings['event_reminders'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="eventReminders"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">Status change notifications</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="statusChanges" name="status_changes" <?php echo $notificationSettings['status_changes'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="statusChanges"></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-3 align-items-center">
                                        <label class="col-sm-9 col-form-label">System announcements</label>
                                        <div class="col-sm-3 text-right">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="systemAnnouncements" name="system_announcements" <?php echo $notificationSettings['system_announcements'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="systemAnnouncements"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="update_settings" value="1">
                                <button type="submit" class="btn btn-primary">Save Notification Settings</button>
                            </form>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <form action="user_settings.php" method="post">
                                <h4 class="mb-4">Change Password</h4>

                                <div class="form-group">
                                    <label for="current_password">Current Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="current_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="new_password">New Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="new_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="confirm_password">Confirm New Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="confirm_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="change_password" value="1">
                                <button type="submit" class="btn btn-primary">Change Password</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.custom-switch .custom-control-label::before {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 1rem;
}

.custom-switch .custom-control-label::after {
    width: calc(1.25rem - 4px);
    height: calc(1.25rem - 4px);
    border-radius: 50%;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1.25rem);
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.text-primary {
    color: #1a73e8 !important;
}

.btn-primary {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.btn-primary:hover {
    background-color: #1765cc;
    border-color: #1765cc;
}

.nav-tabs .nav-link {
    color: #5f6368;
    border: none;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    padding: 12px 16px;
}

.nav-tabs .nav-link.active {
    color: #1a73e8;
    background-color: transparent;
    border-bottom: 3px solid #1a73e8;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-bottom: 3px solid #dadce0;
}
</style>

<script>
// Theme switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const themeRadios = document.querySelectorAll('.theme-switch');

    themeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const theme = this.value;

            // Set cookie immediately
            document.cookie = `theme=${theme}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days

            // Apply theme immediately
            const themeLink = document.querySelector('link[href^="css/"][href$="-theme.css"]');
            if (themeLink) {
                themeLink.href = `css/${theme}-theme.css`;
            }

            // Add or remove dark-mode class from body
            if (theme === 'dark') {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }
        });
    });

    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                passwordInput.type = 'password';
                this.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    });
});
</script>

<?php
require_once 'includes/footer.php';
?>
