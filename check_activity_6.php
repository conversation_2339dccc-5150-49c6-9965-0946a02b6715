<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Set the activity ID
$activityId = 6;

echo "<h1>Checking Activity ID: $activityId</h1>";

try {
    // Check if activity exists
    $stmt = $pdo->prepare("SELECT * FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $activity = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h2>Activity Details</h2>";
        echo "<pre>";
        print_r($activity);
        echo "</pre>";
        
        // Check activity type
        echo "<h2>Activity Type: " . htmlspecialchars($activity['activity_type']) . "</h2>";
        
        // Check questions in activity_questions table
        echo "<h2>Questions in activity_questions table</h2>";
        $stmt = $pdo->prepare("SELECT * FROM activity_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        
        $activityQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>Found " . count($activityQuestions) . " questions in activity_questions table.</p>";
        
        if (count($activityQuestions) > 0) {
            echo "<pre>";
            print_r($activityQuestions);
            echo "</pre>";
            
            // Check options for each question
            foreach ($activityQuestions as $question) {
                echo "<h3>Options for Question ID: " . $question['question_id'] . "</h3>";
                $stmt = $pdo->prepare("SELECT * FROM activity_question_options WHERE question_id = :questionId");
                $stmt->bindParam(':questionId', $question['question_id']);
                $stmt->execute();
                
                $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<p>Found " . count($options) . " options.</p>";
                
                if (count($options) > 0) {
                    echo "<pre>";
                    print_r($options);
                    echo "</pre>";
                }
            }
        }
        
        // Check questions in quiz_questions table
        echo "<h2>Questions in quiz_questions table</h2>";
        $stmt = $pdo->prepare("SELECT * FROM quiz_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        
        $quizQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>Found " . count($quizQuestions) . " questions in quiz_questions table.</p>";
        
        if (count($quizQuestions) > 0) {
            echo "<pre>";
            print_r($quizQuestions);
            echo "</pre>";
            
            // Check options for each question
            foreach ($quizQuestions as $question) {
                echo "<h3>Options for Question ID: " . $question['question_id'] . "</h3>";
                $stmt = $pdo->prepare("SELECT * FROM quiz_options WHERE question_id = :questionId");
                $stmt->bindParam(':questionId', $question['question_id']);
                $stmt->execute();
                
                $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<p>Found " . count($options) . " options.</p>";
                
                if (count($options) > 0) {
                    echo "<pre>";
                    print_r($options);
                    echo "</pre>";
                }
            }
        }
        
        // Check if quiz_answers table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'quiz_answers'");
        $quizAnswersExists = $stmt->rowCount() > 0;
        
        echo "<h2>quiz_answers table exists: " . ($quizAnswersExists ? "Yes" : "No") . "</h2>";
        
        if ($quizAnswersExists) {
            // Check the structure of quiz_answers table
            $stmt = $pdo->query("DESCRIBE quiz_answers");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Structure of quiz_answers table</h3>";
            echo "<pre>";
            print_r($columns);
            echo "</pre>";
        }
        
    } else {
        echo "<p>Activity not found.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
