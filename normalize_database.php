<?php
/**
 * Database Normalization Implementation
 * 
 * This script implements database normalization by:
 * 1. Creating normalized table structure
 * 2. Migrating existing data
 * 3. Creating views for backward compatibility
 * 4. Providing rollback functionality
 */

require_once 'includes/config.php';

// Set execution time limit for large migrations
set_time_limit(300);

echo "<h1>Database Normalization Process</h1>";
echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";

$steps = [
    'backup' => 'Create database backup',
    'create_normalized' => 'Create normalized table structure',
    'migrate_data' => 'Migrate existing data to normalized tables',
    'create_views' => 'Create compatibility views',
    'verify_data' => 'Verify data integrity',
    'update_indexes' => 'Optimize indexes for performance'
];

$errors = [];
$warnings = [];

try {
    // Step 1: Create database backup
    echo "<h3>Step 1: Creating Database Backup</h3>";
    
    $backupFile = 'backup_before_normalization_' . date('Y-m-d_H-i-s') . '.sql';
    $backupPath = __DIR__ . '/database/' . $backupFile;
    
    // Get all tables for backup
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $backupContent = "-- Database backup before normalization\n";
    $backupContent .= "-- Created: " . date('Y-m-d H:i:s') . "\n\n";
    $backupContent .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    foreach ($tables as $table) {
        // Get table structure
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
        $backupContent .= $createTable['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $columns = array_keys($rows[0]);
            $backupContent .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = "(" . implode(', ', $rowValues) . ")";
            }
            $backupContent .= implode(",\n", $values) . ";\n\n";
        }
    }
    
    $backupContent .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    if (file_put_contents($backupPath, $backupContent)) {
        echo "✅ Backup created: $backupFile<br>";
    } else {
        throw new Exception("Failed to create backup file");
    }
    
    // Step 2: Create normalized table structure
    echo "<h3>Step 2: Creating Normalized Table Structure</h3>";
    
    $normalizedSchema = file_get_contents(__DIR__ . '/database/normalized_schema.sql');
    if (!$normalizedSchema) {
        throw new Exception("Could not read normalized schema file");
    }
    
    // Execute schema creation
    $statements = explode(';', $normalizedSchema);
    $createdTables = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            if (stripos($statement, 'CREATE TABLE') !== false) {
                $createdTables++;
                preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "✅ Created table: {$matches[1]}<br>";
                }
            }
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') === false) {
                $warnings[] = "Schema warning: " . $e->getMessage();
                echo "⚠️ Warning: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    echo "✅ Created $createdTables normalized tables<br>";
    
    // Step 3: Migrate existing data
    echo "<h3>Step 3: Migrating Existing Data</h3>";
    
    $migrationScript = file_get_contents(__DIR__ . '/database/migrate_to_normalized.sql');
    if (!$migrationScript) {
        throw new Exception("Could not read migration script");
    }
    
    // Execute migration
    $migrationStatements = explode(';', $migrationScript);
    $migratedTables = 0;
    
    foreach ($migrationStatements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0 || strpos($statement, 'SELECT') === 0) {
            continue;
        }
        
        try {
            $result = $pdo->exec($statement);
            if (stripos($statement, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "✅ Migrated data to: {$matches[1]} ($result records)<br>";
                    $migratedTables++;
                }
            }
        } catch (PDOException $e) {
            $warnings[] = "Migration warning: " . $e->getMessage();
            echo "⚠️ Migration warning: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ Migrated data to $migratedTables tables<br>";
    
    // Step 4: Create compatibility views
    echo "<h3>Step 4: Creating Compatibility Views</h3>";
    
    $compatibilityViews = [
        'users_view' => "
            CREATE OR REPLACE VIEW users_view AS
            SELECT 
                u.user_id,
                u.username,
                u.email,
                u.password_hash as password,
                r.role_name as role,
                p.first_name,
                p.last_name,
                p.gender,
                p.date_of_birth as birthday,
                p.phone_number,
                p.profile_picture_url as profile_picture,
                us.status_name as status,
                u.last_login,
                u.created_at,
                u.updated_at
            FROM users_normalized u
            JOIN user_profiles p ON u.user_id = p.user_id
            JOIN roles r ON u.role_id = r.role_id
            JOIN user_statuses us ON u.status_id = us.status_id
        ",
        'courses_view' => "
            CREATE OR REPLACE VIEW courses_view AS
            SELECT 
                c.course_id,
                c.course_code,
                c.title,
                c.description,
                c.instructor_id as created_by,
                cc.category_name as category,
                s.semester_name as semester,
                c.capacity_limit as capacity,
                c.is_published as is_active,
                c.is_archived,
                c.created_at,
                c.updated_at
            FROM courses_normalized c
            LEFT JOIN course_categories cc ON c.category_id = cc.category_id
            LEFT JOIN semesters s ON c.semester_id = s.semester_id
        ",
        'activities_view' => "
            CREATE OR REPLACE VIEW activities_view AS
            SELECT 
                a.activity_id,
                a.course_id,
                at.type_name as activity_type,
                a.title,
                a.description,
                a.instructions,
                a.points_possible as points,
                a.due_date,
                a.is_published,
                a.is_archived,
                a.created_by,
                a.created_at,
                a.updated_at
            FROM activities_normalized a
            JOIN activity_types at ON a.type_id = at.type_id
        "
    ];
    
    foreach ($compatibilityViews as $viewName => $viewSQL) {
        try {
            $pdo->exec($viewSQL);
            echo "✅ Created view: $viewName<br>";
        } catch (PDOException $e) {
            $warnings[] = "View creation warning: " . $e->getMessage();
            echo "⚠️ View warning: " . $e->getMessage() . "<br>";
        }
    }
    
    // Step 5: Verify data integrity
    echo "<h3>Step 5: Verifying Data Integrity</h3>";
    
    $verificationQueries = [
        'users' => "SELECT COUNT(*) FROM users",
        'users_normalized' => "SELECT COUNT(*) FROM users_normalized",
        'courses' => "SELECT COUNT(*) FROM courses",
        'courses_normalized' => "SELECT COUNT(*) FROM courses_normalized",
        'activities' => "SELECT COUNT(*) FROM activities",
        'activities_normalized' => "SELECT COUNT(*) FROM activities_normalized"
    ];
    
    $counts = [];
    foreach ($verificationQueries as $name => $query) {
        try {
            $stmt = $pdo->query($query);
            $counts[$name] = $stmt->fetchColumn();
        } catch (PDOException $e) {
            $counts[$name] = 'N/A';
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Table</th><th>Original Count</th><th>Normalized Count</th><th>Status</th></tr>";
    
    $verificationPairs = [
        ['users', 'users_normalized'],
        ['courses', 'courses_normalized'],
        ['activities', 'activities_normalized']
    ];
    
    foreach ($verificationPairs as $pair) {
        $original = $counts[$pair[0]] ?? 0;
        $normalized = $counts[$pair[1]] ?? 0;
        $status = ($original == $normalized) ? '✅ Match' : '⚠️ Mismatch';
        
        echo "<tr>";
        echo "<td>{$pair[0]}</td>";
        echo "<td>$original</td>";
        echo "<td>$normalized</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 6: Update indexes for performance
    echo "<h3>Step 6: Optimizing Indexes</h3>";
    
    $indexOptimizations = [
        "ANALYZE TABLE users_normalized",
        "ANALYZE TABLE courses_normalized", 
        "ANALYZE TABLE activities_normalized",
        "ANALYZE TABLE enrollments_normalized",
        "ANALYZE TABLE submissions_normalized"
    ];
    
    foreach ($indexOptimizations as $optimization) {
        try {
            $pdo->exec($optimization);
            echo "✅ Optimized indexes<br>";
        } catch (PDOException $e) {
            $warnings[] = "Index optimization warning: " . $e->getMessage();
        }
    }
    
    echo "<h3>✅ Normalization Process Completed Successfully!</h3>";
    
    if (!empty($warnings)) {
        echo "<h4>⚠️ Warnings:</h4>";
        echo "<ul>";
        foreach ($warnings as $warning) {
            echo "<li>$warning</li>";
        }
        echo "</ul>";
    }
    
    echo "<h4>📋 Summary:</h4>";
    echo "<ul>";
    echo "<li>✅ Database backup created: $backupFile</li>";
    echo "<li>✅ Normalized tables created and populated</li>";
    echo "<li>✅ Compatibility views created for existing code</li>";
    echo "<li>✅ Data integrity verified</li>";
    echo "<li>✅ Indexes optimized for performance</li>";
    echo "</ul>";
    
    echo "<h4>🔄 Next Steps:</h4>";
    echo "<ul>";
    echo "<li>Update application code to use normalized tables</li>";
    echo "<li>Test all functionality with new structure</li>";
    echo "<li>Monitor performance and adjust indexes as needed</li>";
    echo "<li>Remove old tables after thorough testing</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error during normalization:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    if (!empty($warnings)) {
        echo "<h4>⚠️ Warnings before error:</h4>";
        echo "<ul>";
        foreach ($warnings as $warning) {
            echo "<li>$warning</li>";
        }
        echo "</ul>";
    }
}

echo "</div>";
?>
