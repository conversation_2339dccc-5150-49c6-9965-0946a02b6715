<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can request to join courses.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_POST['course_id']) || empty($_POST['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_POST['course_id']);

try {
    // Check if the enrollment_requests table exists
    $tableExists = false;
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'enrollment_requests'");
        $tableExists = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        // Error checking table
    }

    if (!$tableExists) {
        // Create the enrollment_requests table
        $sql = "CREATE TABLE enrollment_requests (
            request_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_by INT NULL,
            processed_date TIMESTAMP NULL,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL
        )";

        $pdo->exec($sql);
    }

    // Create enrollment request
    $result = createEnrollmentRequest($_SESSION['user_id'], $courseId);

    if ($result === true) {
        $_SESSION['success'] = "Your enrollment request has been submitted. You will be notified when it is approved by the instructor.";
    } else {
        $_SESSION['error'] = $result;
    }
} catch (PDOException $e) {
    $_SESSION['error'] = "Database error: " . $e->getMessage();
}

// Redirect back to index page
header("location: index.php");
exit;
