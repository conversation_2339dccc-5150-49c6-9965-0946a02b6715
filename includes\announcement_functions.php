<?php
/**
 * Announcement Functions
 *
 * This file contains functions related to announcements.
 */

require_once 'config.php';

/**
 * Function to create a new announcement
 * <PERSON><PERSON> can create announcements for any course, teachers can only create announcements for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The announcement title
 * @param string $content The announcement content
 * @param int $createdBy The user ID of the creator
 * @param array $files Optional array of uploaded files
 * @param string $announcementDate Optional announcement date (YYYY-MM-DD)
 * @return int|string Announcement ID if creation successful, error message otherwise
 */
function createAnnouncement($courseId, $title, $content, $createdBy, $files = null, $announcementDate = null) {
    global $pdo;

    try {
        // Check if user is authorized to create announcements for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->bindParam(':userId', $createdBy);
            $stmt->bindParam(':instructorId', $createdBy);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to create announcements for this course.";
            }
        }

        // Set announcement date to current date if not provided
        if ($announcementDate === null) {
            $announcementDate = date('Y-m-d');
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create the announcement
        $stmt = $pdo->prepare("
            INSERT INTO announcements (course_id, title, content, created_by, announcement_date)
            VALUES (:courseId, :title, :content, :createdBy, :announcementDate)
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':createdBy', $createdBy);
        $stmt->bindParam(':announcementDate', $announcementDate);

        $stmt->execute();

        $announcementId = $pdo->lastInsertId();

        // Handle file uploads if any
        if ($files && !empty($files['name'][0])) {
            // Create uploads directory if it doesn't exist
            $uploadDir = '../uploads/announcements/' . $announcementId . '/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Process each uploaded file
            $fileCount = count($files['name']);
            for ($i = 0; $i < $fileCount; $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $fileName = basename($files['name'][$i]);
                    $fileType = $files['type'][$i];
                    $fileSize = $files['size'][$i];
                    $fileTmpName = $files['tmp_name'][$i];

                    // Generate a unique filename to prevent overwriting
                    $uniqueFileName = uniqid() . '_' . $fileName;
                    $targetFilePath = $uploadDir . $uniqueFileName;

                    // Move the uploaded file to the target directory
                    if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                        // Save file information to the database
                        $stmt = $pdo->prepare("
                            INSERT INTO announcement_files (announcement_id, file_name, file_path, file_type, file_size)
                            VALUES (:announcementId, :fileName, :filePath, :fileType, :fileSize)
                        ");

                        $relativePath = 'uploads/announcements/' . $announcementId . '/' . $uniqueFileName;

                        $stmt->bindParam(':announcementId', $announcementId);
                        $stmt->bindParam(':fileName', $fileName);
                        $stmt->bindParam(':filePath', $relativePath);
                        $stmt->bindParam(':fileType', $fileType);
                        $stmt->bindParam(':fileSize', $fileSize);

                        $stmt->execute();
                    } else {
                        // Rollback transaction if file upload fails
                        $pdo->rollBack();
                        return "Failed to upload file: " . $fileName;
                    }
                } elseif ($files['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                    // Rollback transaction if there's an error with the file
                    $pdo->rollBack();
                    return "Error uploading file: " . getAnnouncementFileUploadError($files['error'][$i]);
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        // Create notification for course students
        createAnnouncementNotifications($courseId, $announcementId, $title);

        return $announcementId;
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to create announcement: " . $e->getMessage();
    }
}

/**
 * Function to get announcement by ID
 *
 * @param int $announcementId The announcement ID
 * @return array|string Announcement details if successful, error message otherwise
 */
function getAnnouncementById($announcementId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.first_name, u.last_name, u.username
            FROM announcements a
            JOIN users u ON a.created_by = u.user_id
            WHERE a.announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Announcement not found.";
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return "Failed to retrieve announcement: " . $e->getMessage();
    }
}

/**
 * Function to get files attached to an announcement
 *
 * @param int $announcementId The announcement ID
 * @return array|string Array of files if successful, error message otherwise
 */
function getAnnouncementFiles($announcementId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT *
            FROM announcement_files
            WHERE announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve announcement files: " . $e->getMessage();
    }
}

/**
 * Function to create notifications for an announcement
 *
 * @param int $courseId The course ID
 * @param int $announcementId The announcement ID
 * @param string $title The announcement title
 * @return bool True if successful
 */
function createAnnouncementNotifications($courseId, $announcementId, $title) {
    global $pdo;

    try {
        // Get all students enrolled in the course
        $stmt = $pdo->prepare("
            SELECT user_id
            FROM course_enrollments
            WHERE course_id = :courseId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        $students = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Create a notification for each student
        foreach ($students as $studentId) {
            $notificationTitle = "New Announcement";
            $notificationMessage = "A new announcement has been posted: " . $title;

            // Check if createNotification function exists
            if (function_exists('createNotification')) {
                createNotification($studentId, $notificationTitle, $notificationMessage, 'announcement', $announcementId);
            }
        }

        return true;
    } catch (PDOException $e) {
        // Log the error but don't fail the announcement creation
        error_log("Failed to create announcement notifications: " . $e->getMessage());
        return false;
    }
}

/**
 * Helper function to get file upload error message for announcements
 *
 * @param int $errorCode The error code from $_FILES['error']
 * @return string Error message
 */
function getAnnouncementFileUploadError($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return "The uploaded file exceeds the upload_max_filesize directive in php.ini";
        case UPLOAD_ERR_FORM_SIZE:
            return "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form";
        case UPLOAD_ERR_PARTIAL:
            return "The uploaded file was only partially uploaded";
        case UPLOAD_ERR_NO_FILE:
            return "No file was uploaded";
        case UPLOAD_ERR_NO_TMP_DIR:
            return "Missing a temporary folder";
        case UPLOAD_ERR_CANT_WRITE:
            return "Failed to write file to disk";
        case UPLOAD_ERR_EXTENSION:
            return "File upload stopped by extension";
        default:
            return "Unknown upload error";
    }
}

/**
 * Function to add a comment to an announcement
 *
 * @param int $announcementId The announcement ID
 * @param int $userId The user ID
 * @param string $content The comment content
 * @return int|string Comment ID if successful, error message otherwise
 */
function addAnnouncementComment($announcementId, $userId, $content) {
    global $pdo;

    try {
        // Check if announcement exists
        $stmt = $pdo->prepare("SELECT announcement_id FROM announcements WHERE announcement_id = :announcementId");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Announcement not found.";
        }

        // Add the comment
        $stmt = $pdo->prepare("
            INSERT INTO announcement_comments (announcement_id, user_id, content)
            VALUES (:announcementId, :userId, :content)
        ");

        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':content', $content);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add comment: " . $e->getMessage();
    }
}

/**
 * Function to get all comments for an announcement
 *
 * @param int $announcementId The announcement ID
 * @return array|string Array of comments if successful, error message otherwise
 */
function getAnnouncementComments($announcementId) {
    global $pdo;

    try {
        // Get all comments for the announcement
        $stmt = $pdo->prepare("
            SELECT c.*, u.first_name, u.last_name
            FROM announcement_comments c
            JOIN users u ON c.user_id = u.user_id
            WHERE c.announcement_id = :announcementId
            ORDER BY c.created_at ASC
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        $comments = $stmt->fetchAll();

        // Get replies for each comment
        foreach ($comments as &$comment) {
            $comment['replies'] = getCommentReplies($comment['comment_id']);
        }

        return $comments;
    } catch (PDOException $e) {
        return "Failed to retrieve comments: " . $e->getMessage();
    }
}

/**
 * Function to add a reply to a comment
 *
 * @param int $commentId The comment ID
 * @param int $userId The user ID
 * @param string $content The reply content
 * @return int|string Reply ID if successful, error message otherwise
 */
function addCommentReply($commentId, $userId, $content) {
    global $pdo;

    try {
        // Check if comment exists
        $stmt = $pdo->prepare("SELECT comment_id FROM announcement_comments WHERE comment_id = :commentId");
        $stmt->bindParam(':commentId', $commentId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Comment not found.";
        }

        // Add the reply
        $stmt = $pdo->prepare("
            INSERT INTO announcement_comment_replies (comment_id, user_id, content)
            VALUES (:commentId, :userId, :content)
        ");

        $stmt->bindParam(':commentId', $commentId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':content', $content);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add reply: " . $e->getMessage();
    }
}

/**
 * Function to get all replies for a comment
 *
 * @param int $commentId The comment ID
 * @return array|string Array of replies if successful, error message otherwise
 */
function getCommentReplies($commentId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT r.*, u.first_name, u.last_name
            FROM announcement_comment_replies r
            JOIN users u ON r.user_id = u.user_id
            WHERE r.comment_id = :commentId
            ORDER BY r.created_at ASC
        ");
        $stmt->bindParam(':commentId', $commentId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve replies: " . $e->getMessage();
    }
}

/**
 * Function to edit a comment
 *
 * @param int $commentId The comment ID
 * @param int $userId The user ID
 * @param string $content The new content
 * @return bool|string True if successful, error message otherwise
 */
function editAnnouncementComment($commentId, $userId, $content) {
    global $pdo;

    try {
        // Check if comment exists and belongs to the user
        $stmt = $pdo->prepare("
            SELECT comment_id
            FROM announcement_comments
            WHERE comment_id = :commentId AND user_id = :userId
        ");
        $stmt->bindParam(':commentId', $commentId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Check if user is admin
            if (isAdmin()) {
                $stmt = $pdo->prepare("SELECT comment_id FROM announcement_comments WHERE comment_id = :commentId");
                $stmt->bindParam(':commentId', $commentId);
                $stmt->execute();

                if ($stmt->rowCount() == 0) {
                    return "Comment not found.";
                }
            } else {
                return "You are not authorized to edit this comment.";
            }
        }

        // Update the comment
        $stmt = $pdo->prepare("
            UPDATE announcement_comments
            SET content = :content
            WHERE comment_id = :commentId
        ");

        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':commentId', $commentId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to edit comment: " . $e->getMessage();
    }
}

/**
 * Function to edit a reply
 *
 * @param int $replyId The reply ID
 * @param int $userId The user ID
 * @param string $content The new content
 * @return bool|string True if successful, error message otherwise
 */
function editCommentReply($replyId, $userId, $content) {
    global $pdo;

    try {
        // Check if reply exists and belongs to the user
        $stmt = $pdo->prepare("
            SELECT reply_id
            FROM announcement_comment_replies
            WHERE reply_id = :replyId AND user_id = :userId
        ");
        $stmt->bindParam(':replyId', $replyId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Check if user is admin
            if (isAdmin()) {
                $stmt = $pdo->prepare("SELECT reply_id FROM announcement_comment_replies WHERE reply_id = :replyId");
                $stmt->bindParam(':replyId', $replyId);
                $stmt->execute();

                if ($stmt->rowCount() == 0) {
                    return "Reply not found.";
                }
            } else {
                return "You are not authorized to edit this reply.";
            }
        }

        // Update the reply
        $stmt = $pdo->prepare("
            UPDATE announcement_comment_replies
            SET content = :content
            WHERE reply_id = :replyId
        ");

        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':replyId', $replyId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to edit reply: " . $e->getMessage();
    }
}

/**
 * Function to delete a comment
 *
 * @param int $commentId The comment ID
 * @param int $userId The user ID
 * @return bool|string True if successful, error message otherwise
 */
function deleteAnnouncementComment($commentId, $userId) {
    global $pdo;

    try {
        // Check if comment exists and belongs to the user
        $stmt = $pdo->prepare("
            SELECT comment_id
            FROM announcement_comments
            WHERE comment_id = :commentId AND user_id = :userId
        ");
        $stmt->bindParam(':commentId', $commentId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Check if user is admin
            if (isAdmin()) {
                $stmt = $pdo->prepare("SELECT comment_id FROM announcement_comments WHERE comment_id = :commentId");
                $stmt->bindParam(':commentId', $commentId);
                $stmt->execute();

                if ($stmt->rowCount() == 0) {
                    return "Comment not found.";
                }
            } else {
                return "You are not authorized to delete this comment.";
            }
        }

        // Delete the comment (replies will be deleted automatically due to foreign key constraints)
        $stmt = $pdo->prepare("
            DELETE FROM announcement_comments
            WHERE comment_id = :commentId
        ");

        $stmt->bindParam(':commentId', $commentId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to delete comment: " . $e->getMessage();
    }
}

/**
 * Function to update an announcement
 *
 * @param int $announcementId The announcement ID
 * @param string $title The new title
 * @param string $content The new content
 * @param string $announcementDate The new announcement date
 * @return bool|string True if successful, error message otherwise
 */
function updateAnnouncement($announcementId, $title, $content, $announcementDate) {
    global $pdo;

    try {
        // Check if announcement exists
        $stmt = $pdo->prepare("
            SELECT a.*, c.created_by as course_creator
            FROM announcements a
            JOIN courses c ON a.course_id = c.course_id
            WHERE a.announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Announcement not found.";
        }

        $announcement = $stmt->fetch();

        // Check if user is authorized to update this announcement
        if (!isAdmin()) {
            if ($announcement['created_by'] != $_SESSION['user_id'] && $announcement['course_creator'] != $_SESSION['user_id']) {
                return "You are not authorized to update this announcement.";
            }
        }

        // Update the announcement
        $stmt = $pdo->prepare("
            UPDATE announcements
            SET title = :title, content = :content, announcement_date = :announcementDate, updated_at = NOW()
            WHERE announcement_id = :announcementId
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':announcementDate', $announcementDate);
        $stmt->bindParam(':announcementId', $announcementId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update announcement: " . $e->getMessage();
    }
}

/**
 * Function to delete an announcement
 *
 * @param int $announcementId The announcement ID
 * @return bool|string True if successful, error message otherwise
 */
function deleteAnnouncement($announcementId) {
    global $pdo;

    try {
        // Begin transaction
        $pdo->beginTransaction();

        // Delete announcement files from database
        $stmt = $pdo->prepare("
            DELETE FROM announcement_files
            WHERE announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        // Delete announcement comments and replies
        $stmt = $pdo->prepare("
            SELECT comment_id FROM announcement_comments
            WHERE announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        $commentIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!empty($commentIds)) {
            // Delete comment replies
            $placeholders = implode(',', array_fill(0, count($commentIds), '?'));
            $stmt = $pdo->prepare("
                DELETE FROM announcement_comment_replies
                WHERE comment_id IN ($placeholders)
            ");

            foreach ($commentIds as $index => $commentId) {
                $stmt->bindValue($index + 1, $commentId);
            }

            $stmt->execute();

            // Delete comments
            $stmt = $pdo->prepare("
                DELETE FROM announcement_comments
                WHERE announcement_id = :announcementId
            ");
            $stmt->bindParam(':announcementId', $announcementId);
            $stmt->execute();
        }

        // Delete the announcement
        $stmt = $pdo->prepare("
            DELETE FROM announcements
            WHERE announcement_id = :announcementId
        ");
        $stmt->bindParam(':announcementId', $announcementId);
        $stmt->execute();

        // Commit transaction
        $pdo->commit();

        // Delete files from filesystem
        $uploadDir = 'uploads/announcements/' . $announcementId . '/';
        if (file_exists($uploadDir)) {
            // Get all files in the directory
            $files = glob($uploadDir . '*');

            // Delete each file
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }

            // Remove the directory
            rmdir($uploadDir);
        }

        return true;
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to delete announcement: " . $e->getMessage();
    }
}

/**
 * Function to delete a reply
 *
 * @param int $replyId The reply ID
 * @param int $userId The user ID
 * @return bool|string True if successful, error message otherwise
 */
function deleteCommentReply($replyId, $userId) {
    global $pdo;

    try {
        // Check if reply exists and belongs to the user
        $stmt = $pdo->prepare("
            SELECT reply_id
            FROM announcement_comment_replies
            WHERE reply_id = :replyId AND user_id = :userId
        ");
        $stmt->bindParam(':replyId', $replyId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Check if user is admin
            if (isAdmin()) {
                $stmt = $pdo->prepare("SELECT reply_id FROM announcement_comment_replies WHERE reply_id = :replyId");
                $stmt->bindParam(':replyId', $replyId);
                $stmt->execute();

                if ($stmt->rowCount() == 0) {
                    return "Reply not found.";
                }
            } else {
                return "You are not authorized to delete this reply.";
            }
        }

        // Delete the reply
        $stmt = $pdo->prepare("
            DELETE FROM announcement_comment_replies
            WHERE reply_id = :replyId
        ");

        $stmt->bindParam(':replyId', $replyId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to delete reply: " . $e->getMessage();
    }
}
