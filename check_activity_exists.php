<?php
// Include configuration file
require_once 'includes/config.php';

// Set the activity ID
$activityId = 6;

echo "<h1>Checking Activity ID: $activityId</h1>";

try {
    // Check if activity exists
    $stmt = $pdo->prepare("SELECT * FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $activity = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h2>Activity Details</h2>";
        echo "<pre>";
        print_r($activity);
        echo "</pre>";
    } else {
        echo "<p>Activity not found in the database.</p>";
        
        // Check if there are any activities in the database
        $stmt = $pdo->query("SELECT activity_id, title, activity_type FROM activities ORDER BY activity_id");
        $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($activities) > 0) {
            echo "<h2>Available Activities</h2>";
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Title</th><th>Type</th></tr>";
            
            foreach ($activities as $activity) {
                echo "<tr>";
                echo "<td>" . $activity['activity_id'] . "</td>";
                echo "<td>" . htmlspecialchars($activity['title']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['activity_type']) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No activities found in the database.</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
