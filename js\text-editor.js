/**
 * Text Editor Functionality
 * Provides rich text editing capabilities for textareas and input fields
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize text editor functionality
    initTextEditor();
});

/**
 * Initialize text editor functionality
 */
function initTextEditor() {
    // Find all text formatting toolbars
    const toolbars = document.querySelectorAll('.text-formatting-toolbar');

    if (toolbars.length === 0) {
        console.log('No text formatting toolbars found');
        return;
    }

    // For each toolbar, add event listeners to its buttons
    toolbars.forEach(toolbar => {
        // Find the associated textarea or input field
        const editorId = toolbar.getAttribute('data-editor');
        const editor = document.getElementById(editorId);

        if (!editor) {
            console.error(`Editor with ID ${editorId} not found`);
            return;
        }

        // Add event listeners to each button
        const boldBtn = toolbar.querySelector('.btn-bold');
        if (boldBtn) {
            boldBtn.addEventListener('click', function() {
                applyFormatting(editor, 'bold');
            });
        }

        const italicBtn = toolbar.querySelector('.btn-italic');
        if (italicBtn) {
            italicBtn.addEventListener('click', function() {
                applyFormatting(editor, 'italic');
            });
        }

        const underlineBtn = toolbar.querySelector('.btn-underline');
        if (underlineBtn) {
            underlineBtn.addEventListener('click', function() {
                applyFormatting(editor, 'underline');
            });
        }

        const listBtn = toolbar.querySelector('.btn-list');
        if (listBtn) {
            listBtn.addEventListener('click', function() {
                applyFormatting(editor, 'list');
            });
        }

        const strikeBtn = toolbar.querySelector('.btn-strike');
        if (strikeBtn) {
            strikeBtn.addEventListener('click', function() {
                applyFormatting(editor, 'strike');
            });
        }

        // Add input event listener to update formatted output if it exists
        const formattedOutput = document.getElementById('formatted-output');
        if (formattedOutput && editor.id === 'editor-demo') {
            editor.addEventListener('input', function() {
                updateFormattedOutput(editor, formattedOutput);
            });

            // Initial update
            updateFormattedOutput(editor, formattedOutput);
        }
    });

    // Also handle standalone formatting buttons (not in a toolbar)
    const boldBtns = document.querySelectorAll('.btn-bold:not(.text-formatting-toolbar *)');
    boldBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const editorId = btn.getAttribute('data-editor');
            const editor = document.getElementById(editorId);
            if (editor) {
                applyFormatting(editor, 'bold');
            }
        });
    });

    const italicBtns = document.querySelectorAll('.btn-italic:not(.text-formatting-toolbar *)');
    italicBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const editorId = btn.getAttribute('data-editor');
            const editor = document.getElementById(editorId);
            if (editor) {
                applyFormatting(editor, 'italic');
            }
        });
    });

    const underlineBtns = document.querySelectorAll('.btn-underline:not(.text-formatting-toolbar *)');
    underlineBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const editorId = btn.getAttribute('data-editor');
            const editor = document.getElementById(editorId);
            if (editor) {
                applyFormatting(editor, 'underline');
            }
        });
    });

    const listBtns = document.querySelectorAll('.btn-list:not(.text-formatting-toolbar *)');
    listBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const editorId = btn.getAttribute('data-editor');
            const editor = document.getElementById(editorId);
            if (editor) {
                applyFormatting(editor, 'list');
            }
        });
    });

    const strikeBtns = document.querySelectorAll('.btn-strike:not(.text-formatting-toolbar *)');
    strikeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const editorId = btn.getAttribute('data-editor');
            const editor = document.getElementById(editorId);
            if (editor) {
                applyFormatting(editor, 'strike');
            }
        });
    });
}

/**
 * Update the formatted output preview
 *
 * @param {HTMLElement} editor - The textarea or input element
 * @param {HTMLElement} outputElement - The element to display formatted output
 */
function updateFormattedOutput(editor, outputElement) {
    const text = editor.value;

    if (!text) {
        outputElement.innerHTML = '<p class="text-muted mb-0">Formatted text will appear here...</p>';
        return;
    }

    // Format the text
    let formattedText = formatText(text);

    // Update the output element
    outputElement.innerHTML = formattedText || '<p class="text-muted mb-0">No formatted content yet...</p>';

    // Add a class to indicate content has been formatted
    outputElement.classList.add('has-content');

    // Add event to show the original markdown when hovering over formatted elements
    const formattedElements = outputElement.querySelectorAll('strong, em, u, del, li');
    formattedElements.forEach(el => {
        // Add a subtle highlight effect on hover
        el.style.transition = 'background-color 0.2s';
        el.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(66, 133, 244, 0.1)';
        });
        el.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
        });
    });
}

/**
 * Format text with Markdown-like syntax
 *
 * @param {string} text - The text to format
 * @return {string} The formatted HTML
 */
function formatText(text) {
    if (!text) return '';

    // Escape HTML
    text = text.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&#039;');

    // Process formatting in the correct order to handle nested formatting

    // Bold: **text**
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic: *text*
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Underline: __text__
    text = text.replace(/__(.*?)__/g, '<u>$1</u>');

    // Strikethrough: ~~text~~
    text = text.replace(/~~(.*?)~~/g, '<del>$1</del>');

    // Bullet points: • text
    let hasList = text.includes('• ');
    if (hasList) {
        let lines = text.split('\n');
        let inList = false;
        let result = [];

        for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('• ')) {
                if (!inList) {
                    result.push('<ul class="formatted-list">');
                    inList = true;
                }
                result.push('<li>' + lines[i].substring(2) + '</li>');
            } else {
                if (inList) {
                    result.push('</ul>');
                    inList = false;
                }
                result.push(lines[i]);
            }
        }

        if (inList) {
            result.push('</ul>');
        }

        text = result.join('\n');
    }

    // Convert newlines to <br>
    text = text.replace(/\n/g, '<br>');

    return text;
}

/**
 * Apply formatting to the selected text in the editor
 *
 * @param {HTMLElement} editor - The textarea or input element
 * @param {string} command - The formatting command (bold, italic, underline, list, strike)
 */
function applyFormatting(editor, command) {
    // Focus the editor
    editor.focus();

    // Get the current selection
    const selectionStart = editor.selectionStart;
    const selectionEnd = editor.selectionEnd;
    const selectedText = editor.value.substring(selectionStart, selectionEnd);

    // Apply the formatting based on the command
    let formattedText = '';
    let cursorPosition = 0;

    switch (command) {
        case 'bold':
            formattedText = `**${selectedText}**`;
            cursorPosition = selectionStart + 2;
            break;
        case 'italic':
            formattedText = `*${selectedText}*`;
            cursorPosition = selectionStart + 1;
            break;
        case 'underline':
            formattedText = `__${selectedText}__`;
            cursorPosition = selectionStart + 2;
            break;
        case 'strike':
            formattedText = `~~${selectedText}~~`;
            cursorPosition = selectionStart + 2;
            break;
        case 'list':
            // Split the selected text by lines and add bullet points
            if (selectedText) {
                const lines = selectedText.split('\n');
                formattedText = lines.map(line => `• ${line}`).join('\n');
            } else {
                formattedText = '• ';
            }
            cursorPosition = selectionStart + 2;
            break;
    }

    // Replace the selected text with the formatted text
    if (selectedText) {
        editor.value =
            editor.value.substring(0, selectionStart) +
            formattedText +
            editor.value.substring(selectionEnd);

        // Set the cursor position after the formatting
        editor.setSelectionRange(
            selectionStart + formattedText.length,
            selectionStart + formattedText.length
        );
    } else {
        // If no text is selected, insert the formatting at the cursor position
        editor.value =
            editor.value.substring(0, selectionStart) +
            formattedText +
            editor.value.substring(selectionStart);

        // Set the cursor position inside the formatting
        editor.setSelectionRange(cursorPosition, cursorPosition);
    }

    // Add visual feedback to the button
    const button = document.querySelector(`.btn-${command}`);
    if (button) {
        button.classList.add('active');
        setTimeout(() => {
            button.classList.remove('active');
        }, 200);
    }

    // Update formatted output if it exists
    const formattedOutput = document.getElementById('formatted-output');
    if (formattedOutput && editor.id === 'editor-demo') {
        updateFormattedOutput(editor, formattedOutput);
    }

    // Trigger input event to notify any listeners
    const inputEvent = new Event('input', { bubbles: true });
    editor.dispatchEvent(inputEvent);
}
