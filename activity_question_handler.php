<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to manage activity questions.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_POST['activity_id']) || empty($_POST['activity_id'])) {
    $_SESSION['error'] = "Activity ID is required.";
    header("location: index.php");
    exit;
}

$activityId = intval($_POST['activity_id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to manage questions for this activity.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Process form data
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received in activity_question_handler.php: " . print_r($_POST, true));

    // Add a new question
    if (isset($_POST['add_question'])) {
        $questionText = trim($_POST['question_text']);
        $questionType = $_POST['question_type'];
        $questionPoints = intval($_POST['question_points']);

        if (!empty($questionText) && !empty($questionType) && $questionPoints > 0) {
            // Check activity type to determine which function to use
            if ($activity['activity_type'] == 'quiz') {
                $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
            } else {
                $result = addActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
            }

            if (is_numeric($result)) {
                $questionId = $result;
                $_SESSION['success'] = "Question added successfully!";

                // Handle different question types
                if ($questionType == 'multiple_choice') {
                    // Add multiple choice options
                    $options = array();
                    $correctAnswer = trim($_POST['mc_correct_answer']);

                    // Add options A, B, C, D if they exist
                    if (!empty($_POST['option_a'])) {
                        $optionA = trim($_POST['option_a']);
                        $isCorrect = ($correctAnswer == 'A');
                        // Add option based on activity type
                        if ($activity['activity_type'] == 'quiz') {
                            $optionResult = addQuizQuestionOption($questionId, $optionA, $isCorrect);
                        } else {
                            $optionResult = addActivityQuestionOption($questionId, $optionA, $isCorrect);
                        }
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option A: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_b'])) {
                        $optionB = trim($_POST['option_b']);
                        $isCorrect = ($correctAnswer == 'B');
                        // Add option based on activity type
                        if ($activity['activity_type'] == 'quiz') {
                            $optionResult = addQuizQuestionOption($questionId, $optionB, $isCorrect);
                        } else {
                            $optionResult = addActivityQuestionOption($questionId, $optionB, $isCorrect);
                        }
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option B: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_c'])) {
                        $optionC = trim($_POST['option_c']);
                        $isCorrect = ($correctAnswer == 'C');
                        // Add option based on activity type
                        if ($activity['activity_type'] == 'quiz') {
                            $optionResult = addQuizQuestionOption($questionId, $optionC, $isCorrect);
                        } else {
                            $optionResult = addActivityQuestionOption($questionId, $optionC, $isCorrect);
                        }
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option C: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_d'])) {
                        $optionD = trim($_POST['option_d']);
                        $isCorrect = ($correctAnswer == 'D');
                        // Add option based on activity type
                        if ($activity['activity_type'] == 'quiz') {
                            $optionResult = addQuizQuestionOption($questionId, $optionD, $isCorrect);
                        } else {
                            $optionResult = addActivityQuestionOption($questionId, $optionD, $isCorrect);
                        }
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option D: " . $optionResult;
                        }
                    }
                }
                elseif ($questionType == 'true_false') {
                    // Add true/false options
                    $correctAnswer = $_POST['tf_correct_answer'];

                    // Add True option
                    $isCorrect = ($correctAnswer == 'true');
                    // Add option based on activity type
                    if ($activity['activity_type'] == 'quiz') {
                        $optionResult = addQuizQuestionOption($questionId, 'True', $isCorrect);
                    } else {
                        $optionResult = addActivityQuestionOption($questionId, 'True', $isCorrect);
                    }
                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to add True option: " . $optionResult;
                    }

                    // Add False option
                    $isCorrect = ($correctAnswer == 'false');
                    // Add option based on activity type
                    if ($activity['activity_type'] == 'quiz') {
                        $optionResult = addQuizQuestionOption($questionId, 'False', $isCorrect);
                    } else {
                        $optionResult = addActivityQuestionOption($questionId, 'False', $isCorrect);
                    }
                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to add False option: " . $optionResult;
                    }
                }
                elseif ($questionType == 'short_answer' && isset($_POST['sa_correct_answer'])) {
                    // Add short answer correct answer
                    $correctAnswer = trim($_POST['sa_correct_answer']);
                    // Add option based on activity type
                    if ($activity['activity_type'] == 'quiz') {
                        $optionResult = addQuizQuestionOption($questionId, $correctAnswer, true);
                    } else {
                        $optionResult = addActivityQuestionOption($questionId, $correctAnswer, true);
                    }

                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to set correct answer: " . $optionResult;
                    }
                }
            } else {
                $_SESSION['error'] = $result;
            }
        } else {
            $_SESSION['error'] = "Please fill in all question fields.";
        }
    }

    // Redirect back to the activity edit page with a timestamp to force refresh
    header("location: activity_edit.php?id=$activityId&refresh=" . time() . "#questions");
    exit;
} else {
    // If not a POST request, redirect to the activity edit page
    header("location: activity_edit.php?id=$activityId");
    exit;
}
?>
