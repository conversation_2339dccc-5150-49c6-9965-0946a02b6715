<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Updating Users Table</h1>";

try {
    // Check if the columns already exist
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'gender'");
    $genderExists = $stmt->rowCount() > 0;
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'birthday'");
    $birthdayExists = $stmt->rowCount() > 0;
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'phone_number'");
    $phoneNumberExists = $stmt->rowCount() > 0;
    
    // Add gender column if it doesn't exist
    if (!$genderExists) {
        $pdo->exec("ALTER TABLE users ADD COLUMN gender ENUM('male', 'female', 'other') AFTER last_name");
        echo "<p>Added gender column to users table.</p>";
    } else {
        echo "<p>Gender column already exists.</p>";
    }
    
    // Add birthday column if it doesn't exist
    if (!$birthdayExists) {
        $pdo->exec("ALTER TABLE users ADD COLUMN birthday DATE AFTER gender");
        echo "<p>Added birthday column to users table.</p>";
    } else {
        echo "<p>Birthday column already exists.</p>";
    }
    
    // Add phone_number column if it doesn't exist
    if (!$phoneNumberExists) {
        $pdo->exec("ALTER TABLE users ADD COLUMN phone_number VARCHAR(20) AFTER birthday");
        echo "<p>Added phone_number column to users table.</p>";
    } else {
        echo "<p>Phone number column already exists.</p>";
    }
    
    echo "<p>Users table update completed successfully.</p>";
    echo "<p><a href='register.php'>Go to Registration Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error updating users table: " . $e->getMessage() . "</p>";
}
?>
