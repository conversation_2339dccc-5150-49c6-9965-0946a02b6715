<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Activity ID is required.";
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit activities for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get modules for this course
$modules = getCourseModules($courseId);
if (is_string($modules)) {
    $modules = [];
}

// Get activity questions based on activity type
if ($activity['activity_type'] == 'quiz') {
    // For quiz activities, use getQuizQuestions
    $questions = getQuizQuestions($activityId);
    error_log("Quiz questions for ID $activityId: " . print_r($questions, true));
} else {
    // For regular activities, use getActivityQuestions
    $questions = getActivityQuestions($activityId);
    error_log("Activity questions for ID $activityId: " . print_r($questions, true));
}

// Handle error or empty results
if (is_string($questions)) {
    error_log("Error retrieving questions: $questions");
    $questions = [];
}

// Initialize variables
$title = $activity['title'];
$description = $activity['description'];
$activityType = $activity['activity_type'];
$points = $activity['points'];
$dueDate = $activity['due_date'];
$isPublished = $activity['is_published'];
$allowLateSubmissions = isset($activity['allow_late_submissions']) ? $activity['allow_late_submissions'] : false;
$title_err = $description_err = $points_err = $dueDate_err = $file_err = "";
$success = "";

// Get existing files
$activityFiles = getActivityFiles($activityId);

// Set page title
$page_title = "Edit Activity - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($activity['title']); ?></a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Activity</h1>
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <a href="#questions" class="btn btn-primary" id="manageQuestionsBtn">
                    <i class="fas fa-question-circle"></i> Manage Questions
                </a>
                <?php endif; ?>
            </div>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <ul class="nav nav-tabs mb-4" id="activityTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="true">Activity Details</a>
                </li>
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <li class="nav-item">
                    <a class="nav-link" id="questions-tab" data-toggle="tab" href="#questions" role="tab" aria-controls="questions" aria-selected="false">Questions</a>
                </li>
                <?php endif; ?>
            </ul>

            <div class="tab-content" id="activityTabsContent">
                <!-- Activity Details Tab -->
                <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                    <div class="card">
                        <div class="card-body">
                            <form action="activity_update_handler.php" method="post" enctype="multipart/form-data" id="update-activity-form">
                                <!-- Form content for activity details -->
                                <div class="form-group text-right">
                                    <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                                    <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary" id="update-activity-btn">Update Activity</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Questions Tab -->
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <div class="tab-pane fade" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                    <div class="text-right mb-3">
                        <button type="button" class="btn btn-primary btn-lg" id="showAddQuestionForm">
                            <i class="fas fa-plus"></i> Add New Question
                        </button>
                    </div>

                    <div id="addQuestionFormContainer" style="display: none;">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Add New Question</h5>
                            </div>
                            <div class="card-body">
                                <form action="activity_question_handler.php" method="post" id="questionAddForm" enctype="multipart/form-data">
                                    <div class="form-group">
                                        <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                        <textarea name="question_text" id="question_text" class="form-control" rows="3" required></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_type">Type:</label>
                                        <select name="question_type" id="question_type" class="form-control" onchange="toggleQuestionOptions()">
                                            <option value="multiple_choice">Multiple Choice</option>
                                            <option value="true_false">True/False</option>
                                            <option value="short_answer">Short Answer</option>
                                        </select>
                                    </div>

                                    <!-- Multiple Choice Options -->
                                    <div id="multiple_choice_options" class="question-options">
                                        <div class="form-group">
                                            <label for="option_a">Option A:</label>
                                            <input type="text" name="option_a" id="option_a" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_b">Option B:</label>
                                            <input type="text" name="option_b" id="option_b" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_c">Option C:</label>
                                            <input type="text" name="option_c" id="option_c" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_d">Option D:</label>
                                            <input type="text" name="option_d" id="option_d" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="mc_correct_answer">Correct Answer:</label>
                                            <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="A">Option A</option>
                                                <option value="B">Option B</option>
                                                <option value="C">Option C</option>
                                                <option value="D">Option D</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- True/False Options -->
                                    <div id="true_false_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="tf_correct_answer">Correct Answer:</label>
                                            <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="true">True</option>
                                                <option value="false">False</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Short Answer Options -->
                                    <div id="short_answer_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="sa_correct_answer">Correct Answer:</label>
                                            <input type="text" name="sa_correct_answer" id="sa_correct_answer" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_points">Points <span class="text-danger">*</span></label>
                                        <input type="number" name="question_points" id="question_points" class="form-control" value="1" min="1" required>
                                    </div>

                                    <div class="form-group text-right">
                                        <button type="button" class="btn btn-secondary mr-2" id="cancelAddQuestion">Cancel</button>
                                        <input type="hidden" name="add_question" value="1">
                                        <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                                        <button type="submit" class="btn btn-success" id="add-question-btn">Add Question</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Questions</h5>
                        </div>
                        <div class="list-group list-group-flush" id="questionsList">
                            <!-- Questions will be loaded here -->
                            <?php if (count($questions) == 0): ?>
                                <div class="list-group-item text-center py-4">
                                    <p class="text-muted mb-0">No questions added yet. Click "Add New Question" to get started.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
// Function to toggle question options based on question type
function toggleQuestionOptions() {
    var questionType = document.getElementById('question_type').value;
    var multipleChoiceOptions = document.getElementById('multiple_choice_options');
    var trueFalseOptions = document.getElementById('true_false_options');
    var shortAnswerOptions = document.getElementById('short_answer_options');

    // Hide all options first
    multipleChoiceOptions.style.display = 'none';
    trueFalseOptions.style.display = 'none';
    shortAnswerOptions.style.display = 'none';

    // Show the appropriate options based on question type
    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
        // Make option A and B required for multiple choice
        document.getElementById('option_a').setAttribute('required', 'required');
        document.getElementById('option_b').setAttribute('required', 'required');
        document.getElementById('mc_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'true_false') {
        trueFalseOptions.style.display = 'block';
        document.getElementById('tf_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'short_answer') {
        shortAnswerOptions.style.display = 'block';
        document.getElementById('sa_correct_answer').setAttribute('required', 'required');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize question options
    toggleQuestionOptions();

    // Initialize tabs
    $('#activityTabs a[data-toggle="tab"]').tab();

    // Check if there's a hash in the URL to activate the correct tab
    var hash = window.location.hash;
    if (hash) {
        $('#activityTabs a[href="' + hash + '"]').tab('show');
    }

    // Add hash to URL when tab is clicked
    $('#activityTabs a').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.hash;
    });

    // Show/hide add question form
    $('#showAddQuestionForm').on('click', function() {
        $('#addQuestionFormContainer').slideDown();
        $(this).hide();
    });

    $('#cancelAddQuestion').on('click', function() {
        $('#addQuestionFormContainer').slideUp();
        $('#showAddQuestionForm').show();
    });

    // Manage Questions button
    $('#manageQuestionsBtn').on('click', function() {
        $('#questions-tab').tab('show');

        // After a short delay, show the questions list
        setTimeout(function() {
            loadQuestions();
        }, 200);
    });

    // Form validation for adding questions
    $('#questionAddForm').on('submit', function(e) {
        var questionText = $('#question_text').val().trim();
        var questionType = $('#question_type').val();
        var questionPoints = $('#question_points').val();

        if (!questionText) {
            alert('Please enter question text');
            e.preventDefault();
            return false;
        }

        if (!questionPoints || questionPoints < 1) {
            alert('Please enter valid points (minimum 1)');
            e.preventDefault();
            return false;
        }

        if (questionType === 'multiple_choice') {
            var optionA = $('#option_a').val().trim();
            var optionB = $('#option_b').val().trim();
            var correctAnswer = $('#mc_correct_answer').val();

            if (!optionA || !optionB) {
                alert('Please fill in at least options A and B');
                e.preventDefault();
                return false;
            }

            if (!correctAnswer) {
                alert('Please select the correct answer');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'true_false') {
            var tfCorrectAnswer = $('#tf_correct_answer').val();

            if (!tfCorrectAnswer) {
                alert('Please select the correct answer (True or False)');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'short_answer') {
            var saCorrectAnswer = $('#sa_correct_answer').val().trim();

            if (!saCorrectAnswer) {
                alert('Please enter the correct answer for the short answer question');
                e.preventDefault();
                return false;
            }
        }

        return true;
    });

    // Load questions via AJAX
    function loadQuestions() {
        $.ajax({
            url: 'get_activity_questions.php',
            type: 'GET',
            data: { id: <?php echo $activityId; ?> },
            success: function(response) {
                $('#questionsList').html(response);
            },
            error: function() {
                $('#questionsList').html('<div class="list-group-item text-center py-4"><p class="text-danger">Error loading questions. Please refresh the page.</p></div>');
            }
        });
    }

    // Load questions when the questions tab is shown
    $('#questions-tab').on('shown.bs.tab', function() {
        loadQuestions();
    });

    // If hash is #questions, load questions immediately
    if (hash === '#questions') {
        loadQuestions();
    }
});
</script>
