<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin
if (!isAdmin()) {
    echo "You must be an admin to run this script.";
    exit;
}

// Get activity ID from query parameter
$activityId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($activityId <= 0) {
    echo "<p>Please provide a valid activity ID using the 'id' query parameter.</p>";
    echo "<p>Example: <a href='debug_activity.php?id=8'>debug_activity.php?id=8</a></p>";
    exit;
}

echo "<h1>Debug Activity ID: $activityId</h1>";

try {
    // Get activity details
    $activity = getActivityById($activityId);
    
    if (is_string($activity)) {
        echo "<div style='color: red; font-weight: bold;'>Error: $activity</div>";
        exit;
    }
    
    echo "<h2>Activity Details</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($activity as $key => $value) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($key) . "</td>";
        echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Check activity type
    echo "<h2>Activity Type: " . htmlspecialchars($activity['activity_type']) . "</h2>";
    
    // Get questions based on activity type
    $questions = [];
    
    if ($activity['activity_type'] == 'quiz') {
        echo "<p>This is a quiz activity. Checking quiz_questions table...</p>";
        $questions = getQuizQuestions($activityId);
    } else {
        echo "<p>This is a regular activity. Checking activity_questions table...</p>";
        $questions = getActivityQuestions($activityId);
    }
    
    if (is_string($questions)) {
        echo "<div style='color: red; font-weight: bold;'>Error retrieving questions: $questions</div>";
    } else {
        echo "<p>Found " . count($questions) . " questions.</p>";
        
        if (count($questions) > 0) {
            echo "<h2>Questions</h2>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Text</th><th>Type</th><th>Points</th><th>Options</th></tr>";
            
            foreach ($questions as $question) {
                echo "<tr>";
                echo "<td>" . $question['question_id'] . "</td>";
                echo "<td>" . htmlspecialchars($question['question_text']) . "</td>";
                echo "<td>" . $question['question_type'] . "</td>";
                echo "<td>" . $question['points'] . "</td>";
                
                // Check for options
                echo "<td>";
                if (isset($question['options']) && is_array($question['options'])) {
                    echo "<ul>";
                    foreach ($question['options'] as $option) {
                        echo "<li>" . htmlspecialchars($option['option_text']) . 
                             " (" . ($option['is_correct'] ? "Correct" : "Incorrect") . ")</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "No options found";
                }
                echo "</td>";
                
                echo "</tr>";
            }
            
            echo "</table>";
        }
    }
    
    // Check database tables directly
    echo "<h2>Database Check</h2>";
    
    // Check activity_questions table
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_questions WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    $activityQuestionsCount = $stmt->fetchColumn();
    
    echo "<p>Records in activity_questions for this activity: $activityQuestionsCount</p>";
    
    if ($activityQuestionsCount > 0) {
        $stmt = $pdo->prepare("SELECT * FROM activity_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $rawQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Raw Questions Data</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($rawQuestions[0]) as $header) {
            echo "<th>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($rawQuestions as $question) {
            echo "<tr>";
            foreach ($question as $value) {
                echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Check options for each question
        echo "<h3>Question Options</h3>";
        
        foreach ($rawQuestions as $question) {
            $questionId = $question['question_id'];
            
            $stmt = $pdo->prepare("SELECT * FROM activity_question_options WHERE question_id = :questionId");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();
            $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Options for Question ID: $questionId</h4>";
            
            if (count($options) > 0) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr>";
                foreach (array_keys($options[0]) as $header) {
                    echo "<th>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                
                foreach ($options as $option) {
                    echo "<tr>";
                    foreach ($option as $value) {
                        echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No options found for this question.</p>";
            }
        }
    }
    
    // Check quiz_questions table
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quiz_questions WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    $quizQuestionsCount = $stmt->fetchColumn();
    
    echo "<p>Records in quiz_questions for this activity: $quizQuestionsCount</p>";
    
    if ($quizQuestionsCount > 0) {
        $stmt = $pdo->prepare("SELECT * FROM quiz_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $rawQuizQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Raw Quiz Questions Data</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($rawQuizQuestions[0]) as $header) {
            echo "<th>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($rawQuizQuestions as $question) {
            echo "<tr>";
            foreach ($question as $value) {
                echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Check options for each quiz question
        echo "<h3>Quiz Question Options</h3>";
        
        foreach ($rawQuizQuestions as $question) {
            $questionId = $question['question_id'];
            
            $stmt = $pdo->prepare("SELECT * FROM quiz_options WHERE question_id = :questionId");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();
            $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Options for Quiz Question ID: $questionId</h4>";
            
            if (count($options) > 0) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr>";
                foreach (array_keys($options[0]) as $header) {
                    echo "<th>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                
                foreach ($options as $option) {
                    echo "<tr>";
                    foreach ($option as $value) {
                        echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No options found for this quiz question.</p>";
            }
        }
    }
    
    // Add links to other pages
    echo "<h2>Links</h2>";
    echo "<ul>";
    echo "<li><a href='activity_view.php?id=$activityId'>View Activity</a></li>";
    echo "<li><a href='activity_edit.php?id=$activityId'>Edit Activity</a></li>";
    echo "<li><a href='activity_edit.php?id=$activityId#questions'>Edit Questions</a></li>";
    echo "<li><a href='debug_questions.php?id=$activityId'>Debug Questions</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
