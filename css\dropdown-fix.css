/* Dropdown menu fixes */
.dropdown-menu {
    z-index: 9999 !important; /* Very high z-index to ensure it appears above all elements */
    position: absolute !important;
    transform: none !important;
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    will-change: transform !important;
    margin-top: 0.5rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    display: none;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    border-radius: 0.25rem !important;
    background-color: #fff !important;
    padding: 0.5rem 0 !important;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure dropdown items are visible and properly styled */
.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.5rem 1.5rem !important;
    clear: both !important;
    text-align: inherit !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    color: #212529 !important;
    text-decoration: none !important;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #f8f9fa !important;
    color: #16181b !important;
}

/* Fix for the Create dropdown in classwork tab */
#createClassworkDropdown + .dropdown-menu {
    z-index: 9999 !important; /* Very high z-index */
    min-width: 200px !important;
    display: none;
}

#createClassworkDropdown + .dropdown-menu.show {
    display: block !important;
}

/* Fix for any other dropdowns that might be problematic */
.btn-primary.dropdown-toggle + .dropdown-menu,
.btn.dropdown-toggle + .dropdown-menu {
    z-index: 9999 !important;
}

/* Specific fix for the Create button in blue */
.btn-primary.dropdown-toggle {
    position: relative !important;
}

.btn-primary.dropdown-toggle + .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    margin-top: 0.125rem !important;
    min-width: 200px !important;
}

/* Ensure proper positioning for all dropdowns */
.dropdown {
    position: relative !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    .dropdown-menu {
        position: absolute !important;
        left: auto !important;
        right: 0 !important;
        width: auto !important;
        min-width: 200px !important;
    }
}
