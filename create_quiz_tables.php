<?php
// Include configuration file
require_once 'includes/config.php';

// Function to check if a table exists
function tableExists($pdo, $tableName) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$tableName'");
        return $result->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Create quiz_questions table if it doesn't exist
if (!tableExists($pdo, 'quiz_questions')) {
    try {
        $sql = "CREATE TABLE quiz_questions (
            question_id INT(11) NOT NULL AUTO_INCREMENT,
            activity_id INT(11) NOT NULL,
            question_text TEXT NOT NULL,
            question_type VARCHAR(50) NOT NULL,
            points INT(11) NOT NULL DEFAULT 1,
            position INT(11) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (question_id),
            KEY activity_id (activity_id),
            CONSTRAINT fk_quiz_questions_activity FOREIGN KEY (activity_id) REFERENCES activities (activity_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        echo "<p style='color:green;'>quiz_questions table created successfully!</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red;'>Error creating quiz_questions table: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>quiz_questions table already exists.</p>";
}

// Create quiz_options table if it doesn't exist
if (!tableExists($pdo, 'quiz_options')) {
    try {
        $sql = "CREATE TABLE quiz_options (
            option_id INT(11) NOT NULL AUTO_INCREMENT,
            question_id INT(11) NOT NULL,
            option_text TEXT NOT NULL,
            is_correct TINYINT(1) NOT NULL DEFAULT 0,
            position INT(11) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (option_id),
            KEY question_id (question_id),
            CONSTRAINT fk_quiz_options_question FOREIGN KEY (question_id) REFERENCES quiz_questions (question_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        echo "<p style='color:green;'>quiz_options table created successfully!</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red;'>Error creating quiz_options table: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>quiz_options table already exists.</p>";
}

// Create quiz_settings table if it doesn't exist
if (!tableExists($pdo, 'quiz_settings')) {
    try {
        $sql = "CREATE TABLE quiz_settings (
            setting_id INT(11) NOT NULL AUTO_INCREMENT,
            activity_id INT(11) NOT NULL,
            time_limit INT(11) NOT NULL DEFAULT 60,
            passing_score DECIMAL(5,2) NOT NULL DEFAULT 70.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (setting_id),
            UNIQUE KEY activity_id (activity_id),
            CONSTRAINT fk_quiz_settings_activity FOREIGN KEY (activity_id) REFERENCES activities (activity_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        echo "<p style='color:green;'>quiz_settings table created successfully!</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red;'>Error creating quiz_settings table: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>quiz_settings table already exists.</p>";
}

// Create quiz_answers table if it doesn't exist
if (!tableExists($pdo, 'quiz_answers')) {
    try {
        $sql = "CREATE TABLE quiz_answers (
            answer_id INT(11) NOT NULL AUTO_INCREMENT,
            submission_id INT(11) NOT NULL,
            question_id INT(11) NOT NULL,
            answer_text TEXT,
            is_correct TINYINT(1) DEFAULT NULL,
            points_earned DECIMAL(8,2) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (answer_id),
            KEY submission_id (submission_id),
            KEY question_id (question_id),
            CONSTRAINT fk_quiz_answers_submission FOREIGN KEY (submission_id) REFERENCES activity_submissions (submission_id) ON DELETE CASCADE,
            CONSTRAINT fk_quiz_answers_question FOREIGN KEY (question_id) REFERENCES quiz_questions (question_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        echo "<p style='color:green;'>quiz_answers table created successfully!</p>";
    } catch (PDOException $e) {
        echo "<p style='color:red;'>Error creating quiz_answers table: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>quiz_answers table already exists.</p>";
}

echo "<p>All necessary tables have been checked and created if needed.</p>";
?>
