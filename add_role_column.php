<?php
// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if role column exists
$result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // Add role column
    $sql = "ALTER TABLE users ADD COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student' AFTER password";
    
    if ($conn->query($sql) === TRUE) {
        echo "Role column added successfully<br>";
    } else {
        echo "Error adding role column: " . $conn->error . "<br>";
    }
} else {
    echo "Role column already exists<br>";
}

// Update existing users to have a role (if they don't already)
$sql = "UPDATE users SET role = 'student' WHERE role IS NULL OR role = ''";
if ($conn->query($sql) === TRUE) {
    echo "Updated users with default role<br>";
} else {
    echo "Error updating users: " . $conn->error . "<br>";
}

// Show the current table structure
$result = $conn->query("SHOW COLUMNS FROM users");

if ($result) {
    echo "<h2>Users Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

$conn->close();
?>
