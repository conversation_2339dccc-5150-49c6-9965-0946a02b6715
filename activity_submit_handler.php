<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'You must be logged in to perform this action.']);
    exit;
}

// Set the response header to JSON
header('Content-Type: application/json');

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Check if the action is specified
if (!isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'No action specified.']);
    exit;
}

// Check if the activity ID is specified
if (!isset($_POST['activity_id'])) {
    echo json_encode(['success' => false, 'message' => 'No activity ID specified.']);
    exit;
}

$activityId = intval($_POST['activity_id']);
$action = $_POST['action'];
$userId = $_SESSION['user_id'];
$isLate = false; // Initialize isLate flag

// Get activity details to check due date
$activityFile = 'data/activities/' . $activityId . '.json';
if (file_exists($activityFile)) {
    $activityData = json_decode(file_get_contents($activityFile), true);

    // Check if due date has passed and late submissions are not allowed
    if (!empty($activityData['due_date'])) {
        $dueDate = new DateTime($activityData['due_date']);
        $now = new DateTime();

        if ($now > $dueDate && $action !== 'unsubmit') {
            if (!($activityData['allow_late_submissions'] ?? false)) {
                echo json_encode(['success' => false, 'message' => 'The due date for this activity has passed and late submissions are not allowed.']);
                exit;
            } else {
                // Late submission is allowed, but we'll mark it as late
                $isLate = true;
            }
        }
    }
}

// Create the uploads directory if it doesn't exist
$uploadsDir = 'uploads';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Create the submissions directory if it doesn't exist
$submissionsDir = 'data/submissions';
if (!is_dir($submissionsDir)) {
    mkdir($submissionsDir, 0755, true);
}

// Define the submissions file path
$submissionsFile = $submissionsDir . '/activity_' . $activityId . '.json';

// Load existing submissions
$submissions = [];
if (file_exists($submissionsFile)) {
    $submissionsJson = file_get_contents($submissionsFile);
    $submissions = json_decode($submissionsJson, true);

    if ($submissions === null) {
        $submissions = [];
    }
}

// Handle the action
switch ($action) {
    case 'mark_as_done':
        // Get activity type
        $activityType = 'activity';
        if (isset($activityData['activity_type'])) {
            $activityType = $activityData['activity_type'];
        }

        // Create a new submission
        $submission = [
            'user_id' => $userId,
            'activity_id' => $activityId,
            'submission_date' => date('Y-m-d H:i:s'),
            'status' => 'submitted',
            'marked_as_done' => true,
            'activity_type' => $activityType,
            'is_late' => $isLate
        ];

        // Add or update the submission
        $submissions[$userId] = $submission;

        // Save the submissions to the file
        if (file_put_contents($submissionsFile, json_encode($submissions)) === false) {
            echo json_encode(['success' => false, 'message' => 'Failed to save submission.']);
            exit;
        }

        // Return success
        echo json_encode(['success' => true]);
        break;

    case 'unsubmit':
        // Remove the submission
        if (isset($submissions[$userId])) {
            unset($submissions[$userId]);

            // Save the submissions to the file
            if (file_put_contents($submissionsFile, json_encode($submissions)) === false) {
                echo json_encode(['success' => false, 'message' => 'Failed to remove submission.']);
                exit;
            }

            // Return success
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'No submission found.']);
        }
        break;

    case 'submit_file':
        // Check if a file was uploaded
        if (!isset($_FILES['submission_file']) || $_FILES['submission_file']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error.']);
            exit;
        }

        // Get the file details
        $file = $_FILES['submission_file'];
        $fileName = $file['name'];
        $fileTmpPath = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileError = $file['error'];

        // Check for upload errors
        if ($fileError !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => 'File upload error: ' . $fileError]);
            exit;
        }

        // Generate a unique file name
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $newFileName = 'submission_' . $userId . '_' . $activityId . '_' . time() . '.' . $fileExtension;
        $uploadFilePath = $uploadsDir . '/' . $newFileName;

        // Move the uploaded file
        if (!move_uploaded_file($fileTmpPath, $uploadFilePath)) {
            echo json_encode(['success' => false, 'message' => 'Failed to move uploaded file.']);
            exit;
        }

        // Get activity type
        $activityType = 'activity';
        if (isset($activityData['activity_type'])) {
            $activityType = $activityData['activity_type'];
        }

        // Create a new submission
        $submission = [
            'user_id' => $userId,
            'activity_id' => $activityId,
            'submission_date' => date('Y-m-d H:i:s'),
            'status' => 'submitted',
            'file_path' => $uploadFilePath,
            'file_name' => $fileName,
            'activity_type' => $activityType,
            'is_late' => $isLate
        ];

        // Add or update the submission
        $submissions[$userId] = $submission;

        // Save the submissions to the file
        if (file_put_contents($submissionsFile, json_encode($submissions)) === false) {
            echo json_encode(['success' => false, 'message' => 'Failed to save submission.']);
            exit;
        }

        // Return success
        echo json_encode(['success' => true]);
        break;

    case 'submit_link':
        // Check if a link was provided
        if (!isset($_POST['submission_link']) || empty($_POST['submission_link'])) {
            echo json_encode(['success' => false, 'message' => 'No link provided.']);
            exit;
        }

        $link = $_POST['submission_link'];

        // Get activity type
        $activityType = 'activity';
        if (isset($activityData['activity_type'])) {
            $activityType = $activityData['activity_type'];
        }

        // Create a new submission
        $submission = [
            'user_id' => $userId,
            'activity_id' => $activityId,
            'submission_date' => date('Y-m-d H:i:s'),
            'status' => 'submitted',
            'submission_link' => $link,
            'activity_type' => $activityType,
            'is_late' => $isLate
        ];

        // Add or update the submission
        $submissions[$userId] = $submission;

        // Save the submissions to the file
        if (file_put_contents($submissionsFile, json_encode($submissions)) === false) {
            echo json_encode(['success' => false, 'message' => 'Failed to save submission.']);
            exit;
        }

        // Return success
        echo json_encode(['success' => true]);
        break;

    case 'submit_answers':
        // Process the answers
        $answers = [];

        // Loop through POST data to find answers
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'answer_') === 0) {
                $questionId = substr($key, 7); // Remove 'answer_' prefix
                $answers[$questionId] = $value;
            }
        }

        // Check if any answers were submitted
        if (empty($answers)) {
            echo json_encode(['success' => false, 'message' => 'No answers submitted.']);
            exit;
        }

        // Get activity details from database
        try {
            $stmt = $pdo->prepare("SELECT * FROM activities WHERE activity_id = :activityId");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                echo json_encode(['success' => false, 'message' => 'Activity not found in database.']);
                exit;
            }

            $activity = $stmt->fetch();

            // Submit the activity to the database
            $result = submitActivity($activityId, $userId, null, $answers, null, null, null, null, $isLate);

            if (is_numeric($result)) {
                // For backward compatibility, still update the file-based storage
                // Get activity type
                $activityType = $activity['activity_type'];

                // Create a new submission for file storage
                $submission = [
                    'user_id' => $userId,
                    'activity_id' => $activityId,
                    'submission_date' => date('Y-m-d H:i:s'),
                    'status' => 'submitted',
                    'answers' => $answers,
                    'activity_type' => $activityType,
                    'is_late' => $isLate
                ];

                // Add or update the submission in file storage
                $submissions[$userId] = $submission;
                file_put_contents($submissionsFile, json_encode($submissions));

                // Return success with redirect URL
                echo json_encode(['success' => true, 'redirect' => 'activity_view.php?id=' . $activityId . '&success=1']);
            } else {
                // Error submitting to database
                echo json_encode(['success' => false, 'message' => $result]);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
        }
        exit;

    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action.']);
        break;
}
?>
