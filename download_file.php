<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found. Please contact the administrator.";
    exit;
}

// Get file information
$fileInfo = pathinfo($filePath);
$fileName = $fileInfo['basename'];
$fileExtension = strtolower($fileInfo['extension']);
$fileSize = filesize($filePath);

// Get MIME type
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $filePath);
finfo_close($finfo);

// Determine MIME type from extension if needed
if ($mimeType == 'application/octet-stream' || $mimeType == 'text/plain') {
    // Try to determine MIME type from extension
    switch ($fileExtension) {
        case 'pdf':
            $mimeType = 'application/pdf';
            break;
        case 'doc':
            $mimeType = 'application/msword';
            break;
        case 'docx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            break;
        case 'xls':
            $mimeType = 'application/vnd.ms-excel';
            break;
        case 'xlsx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            break;
        case 'ppt':
            $mimeType = 'application/vnd.ms-powerpoint';
            break;
        case 'pptx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            break;
        case 'txt':
            $mimeType = 'text/plain';
            break;
        case 'jpg':
        case 'jpeg':
            $mimeType = 'image/jpeg';
            break;
        case 'png':
            $mimeType = 'image/png';
            break;
        case 'gif':
            $mimeType = 'image/gif';
            break;
        case 'svg':
            $mimeType = 'image/svg+xml';
            break;
    }
}

// Set appropriate headers for download
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . $fileSize);
header('Content-Disposition: attachment; filename="' . $fileName . '"');

// Output file content
readfile($filePath);
exit;
?>
