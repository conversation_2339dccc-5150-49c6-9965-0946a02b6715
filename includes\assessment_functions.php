<?php
/**
 * Assessment Functions
 *
 * This file contains functions related to assignments and quizzes.
 */

require_once 'config.php';
require_once 'content_functions.php';

/**
 * Function to create a new assignment
 * Ad<PERSON> can create assignments for any course, teachers can only create assignments for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The assignment title
 * @param string $description The assignment description
 * @param int $points The maximum points
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS) or null if no due date
 * @param int $moduleId The module ID (optional)
 * @param bool $allowLateSubmissions Whether to allow late submissions
 * @param int $createdBy The user ID of the creator
 * @param array $files Optional array of uploaded files
 * @return int|string Assignment ID if creation successful, error message otherwise
 */
function createAssignment($courseId, $title, $description, $points, $dueDate = null, $moduleId = null, $allowLateSubmissions = false, $createdBy, $files = null) {
    global $pdo;

    try {
        // Check if user is authorized to create assignments for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->bindParam(':userId', $createdBy);
            $stmt->bindParam(':instructorId', $createdBy);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to create assignments for this course.";
            }
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create the activity with type 'assignment'
        $stmt = $pdo->prepare("
            INSERT INTO activities (course_id, title, description, activity_type, points, due_date, allow_late_submissions, is_published, created_by)
            VALUES (:courseId, :title, :description, 'assignment', :points, :dueDate, :allowLateSubmissions, 1, :createdBy)
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':dueDate', $dueDate);
        $allowLateSubmissionsInt = $allowLateSubmissions ? 1 : 0;
        $stmt->bindParam(':allowLateSubmissions', $allowLateSubmissionsInt);
        $stmt->bindParam(':createdBy', $createdBy);

        $stmt->execute();

        $activityId = $pdo->lastInsertId();

        // If module is selected, associate the activity with the module
        if ($moduleId) {
            $stmt = $pdo->prepare("
                INSERT INTO module_activities (module_id, activity_id)
                VALUES (:moduleId, :activityId)
            ");

            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->bindParam(':activityId', $activityId);

            $stmt->execute();
        }

        // Handle file uploads if any
        if ($files && !empty($files['name'][0])) {
            // Create uploads directory if it doesn't exist
            $uploadDir = '../uploads/assignments/' . $activityId . '/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Process each uploaded file
            $fileCount = count($files['name']);
            for ($i = 0; $i < $fileCount; $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $fileName = basename($files['name'][$i]);
                    $fileType = $files['type'][$i];
                    $fileSize = $files['size'][$i];
                    $fileTmpName = $files['tmp_name'][$i];

                    // Generate a unique filename to prevent overwriting
                    $uniqueFileName = uniqid() . '_' . $fileName;
                    $targetFilePath = $uploadDir . $uniqueFileName;

                    // Move the uploaded file to the target directory
                    if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                        // Save file information to the database
                        $stmt = $pdo->prepare("
                            INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size)
                            VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize)
                        ");

                        $relativePath = 'uploads/assignments/' . $activityId . '/' . $uniqueFileName;

                        $stmt->bindParam(':activityId', $activityId);
                        $stmt->bindParam(':fileName', $fileName);
                        $stmt->bindParam(':filePath', $relativePath);
                        $stmt->bindParam(':fileType', $fileType);
                        $stmt->bindParam(':fileSize', $fileSize);

                        $stmt->execute();
                    } else {
                        // Rollback transaction if file upload fails
                        $pdo->rollBack();
                        return "Failed to upload file: " . $fileName;
                    }
                } elseif ($files['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                    // Rollback transaction if there's an error with the file
                    $pdo->rollBack();
                    return "Error uploading file: " . getFileUploadError($files['error'][$i]);
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        return $activityId;
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to create assignment: " . $e->getMessage();
    }
}

/**
 * Function to update an assignment
 * Admin can update any assignment, teachers can only update assignments in their own courses
 *
 * @param int $assignmentId The assignment ID
 * @param string $title The assignment title
 * @param string $description The assignment description
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS)
 * @param int $points The maximum points
 * @return bool|string True if update successful, error message otherwise
 */
function updateAssignment($assignmentId, $title, $description, $dueDate, $points) {
    global $pdo;

    try {
        // Check if user is authorized to update this assignment
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM assignments a
                JOIN lessons l ON a.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE a.assignment_id = :assignmentId
            ");
            $stmt->bindParam(':assignmentId', $assignmentId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $assignment = $stmt->fetch();

                if (!isTeacher() || $assignment['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this assignment.";
                }
            } else {
                return "Assignment not found.";
            }
        }

        // Update the assignment
        $stmt = $pdo->prepare("
            UPDATE assignments
            SET title = :title, description = :description, due_date = :dueDate, points = :points
            WHERE assignment_id = :assignmentId
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':dueDate', $dueDate);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':assignmentId', $assignmentId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update assignment: " . $e->getMessage();
    }
}

/**
 * Function to delete an assignment
 * Admin can delete any assignment, teachers can only delete assignments in their own courses
 *
 * @param int $assignmentId The assignment ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteAssignment($assignmentId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this assignment
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM assignments a
                JOIN lessons l ON a.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE a.assignment_id = :assignmentId
            ");
            $stmt->bindParam(':assignmentId', $assignmentId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $assignment = $stmt->fetch();

                if (!isTeacher() || $assignment['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this assignment.";
                }
            } else {
                return "Assignment not found.";
            }
        }

        // Delete the assignment
        $stmt = $pdo->prepare("DELETE FROM assignments WHERE assignment_id = :assignmentId");
        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Assignment not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete assignment: " . $e->getMessage();
    }
}

/**
 * Function to get all assignments for a lesson
 *
 * @param int $lessonId The lesson ID
 * @return array|string Array of assignments if successful, error message otherwise
 */
function getAssignmentsByLesson($lessonId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT assignment_id, title, description, due_date, points, created_at, updated_at
            FROM assignments
            WHERE lesson_id = :lessonId
            ORDER BY due_date
        ");
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve assignments: " . $e->getMessage();
    }
}

/**
 * Function to get an assignment by ID
 *
 * @param int $assignmentId The assignment ID
 * @return array|string Assignment data if successful, error message otherwise
 */
function getAssignmentById($assignmentId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT assignment_id, lesson_id, title, description, due_date, points, created_at, updated_at
            FROM assignments
            WHERE assignment_id = :assignmentId
        ");
        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Assignment not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve assignment: " . $e->getMessage();
    }
}

/**
 * Function to submit an assignment
 *
 * @param int $assignmentId The assignment ID
 * @param int $userId The user ID
 * @param string $content The submission content
 * @return bool|string True if submission successful, error message otherwise
 */
function submitAssignment($assignmentId, $userId, $content) {
    global $pdo;

    try {
        // Check if the assignment exists
        $stmt = $pdo->prepare("SELECT assignment_id, due_date FROM assignments WHERE assignment_id = :assignmentId");
        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Assignment not found.";
        }

        $assignment = $stmt->fetch();

        // Check if the due date has passed
        if (strtotime($assignment['due_date']) < time()) {
            return "The due date for this assignment has passed.";
        }

        // Check if a submission already exists
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM submissions
            WHERE assignment_id = :assignmentId AND user_id = :userId
        ");
        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Update the existing submission
            $stmt = $pdo->prepare("
                UPDATE submissions
                SET submission_content = :content, submission_date = CURRENT_TIMESTAMP
                WHERE assignment_id = :assignmentId AND user_id = :userId
            ");
        } else {
            // Create a new submission
            $stmt = $pdo->prepare("
                INSERT INTO submissions (assignment_id, user_id, submission_content)
                VALUES (:assignmentId, :userId, :content)
            ");
        }

        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':content', $content);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to submit assignment: " . $e->getMessage();
    }
}

/**
 * Function to grade an assignment submission (legacy version)
 * Admin can grade any submission, teachers can only grade submissions for their own courses
 *
 * @param int $submissionId The submission ID
 * @param float $grade The grade
 * @param string $feedback The feedback
 * @return bool|string True if grading successful, error message otherwise
 */
function gradeAssignmentSubmission($submissionId, $grade, $feedback) {
    global $pdo;

    try {
        // Check if user is authorized to grade this submission
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM submissions s
                JOIN assignments a ON s.assignment_id = a.assignment_id
                JOIN lessons l ON a.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE s.submission_id = :submissionId
            ");
            $stmt->bindParam(':submissionId', $submissionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $submission = $stmt->fetch();

                if (!isTeacher() || $submission['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to grade this submission.";
                }
            } else {
                return "Submission not found.";
            }
        }

        // Grade the submission
        $stmt = $pdo->prepare("
            UPDATE submissions
            SET grade = :grade, feedback = :feedback
            WHERE submission_id = :submissionId
        ");

        $stmt->bindParam(':grade', $grade);
        $stmt->bindParam(':feedback', $feedback);
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Submission not found.";
        }
    } catch (PDOException $e) {
        return "Failed to grade submission: " . $e->getMessage();
    }
}

/**
 * Function to get all submissions for an assignment
 * Admin can view any submissions, teachers can only view submissions for their own courses
 *
 * @param int $assignmentId The assignment ID
 * @return array|string Array of submissions if successful, error message otherwise
 */
function getSubmissionsByAssignment($assignmentId) {
    global $pdo;

    try {
        // Check if user is authorized to view these submissions
        if (!isAdmin() && !isTeacher()) {
            return "You are not authorized to view these submissions.";
        }

        if (!isAdmin()) {
            // Check if the assignment belongs to a course created by this teacher
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM assignments a
                JOIN lessons l ON a.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE a.assignment_id = :assignmentId
            ");
            $stmt->bindParam(':assignmentId', $assignmentId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $assignment = $stmt->fetch();

                if ($assignment['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to view these submissions.";
                }
            } else {
                return "Assignment not found.";
            }
        }

        // Get the submissions
        $stmt = $pdo->prepare("
            SELECT s.submission_id, s.user_id, u.username, u.first_name, u.last_name,
                   s.submission_content, s.submission_date, s.grade, s.feedback
            FROM submissions s
            JOIN users u ON s.user_id = u.user_id
            WHERE s.assignment_id = :assignmentId
            ORDER BY s.submission_date
        ");
        $stmt->bindParam(':assignmentId', $assignmentId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve submissions: " . $e->getMessage();
    }
}

/**
 * Function to get a submission by ID for assessments (legacy version)
 * Admin can view any submission, teachers can only view submissions for their own courses, students can only view their own submissions
 *
 * @param int $submissionId The submission ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getAssessmentSubmissionById($submissionId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT s.submission_id, s.assignment_id, s.user_id,
                   s.submission_content, s.submission_date, s.grade, s.feedback
            FROM submissions s
            WHERE s.submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $submission = $stmt->fetch();

            // Check if user is authorized to view this submission
            if (!isAdmin()) {
                if (isTeacher()) {
                    // Check if the submission belongs to a course created by this teacher
                    $stmt = $pdo->prepare("
                        SELECT c.created_by
                        FROM submissions s
                        JOIN assignments a ON s.assignment_id = a.assignment_id
                        JOIN lessons l ON a.lesson_id = l.lesson_id
                        JOIN modules m ON l.module_id = m.module_id
                        JOIN courses c ON m.course_id = c.course_id
                        WHERE s.submission_id = :submissionId
                    ");
                    $stmt->bindParam(':submissionId', $submissionId);
                    $stmt->execute();

                    if ($stmt->rowCount() == 1) {
                        $course = $stmt->fetch();

                        if ($course['created_by'] != $_SESSION['user_id']) {
                            return "You are not authorized to view this submission.";
                        }
                    }
                } else {
                    // Students can only view their own submissions
                    if ($submission['user_id'] != $_SESSION['user_id']) {
                        return "You are not authorized to view this submission.";
                    }
                }
            }

            return $submission;
        } else {
            return "Submission not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to get all assignments for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of assignments if successful, error message otherwise
 */
function getCourseAssignments($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.activity_id, a.title, a.description, a.due_date, a.points, a.allow_late_submissions,
                   a.created_at, a.created_by, a.is_published, m.module_id, m.title as module_title
            FROM activities a
            LEFT JOIN module_activities ma ON a.activity_id = ma.activity_id
            LEFT JOIN modules m ON ma.module_id = m.module_id
            WHERE a.course_id = :courseId AND a.activity_type = 'assignment'
            ORDER BY a.due_date
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve course assignments: " . $e->getMessage();
    }
}

/**
 * Helper function to get file upload error message
 *
 * @param int $errorCode The error code from $_FILES['error']
 * @return string Error message
 */
function getFileUploadError($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return "The uploaded file exceeds the upload_max_filesize directive in php.ini";
        case UPLOAD_ERR_FORM_SIZE:
            return "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form";
        case UPLOAD_ERR_PARTIAL:
            return "The uploaded file was only partially uploaded";
        case UPLOAD_ERR_NO_FILE:
            return "No file was uploaded";
        case UPLOAD_ERR_NO_TMP_DIR:
            return "Missing a temporary folder";
        case UPLOAD_ERR_CANT_WRITE:
            return "Failed to write file to disk";
        case UPLOAD_ERR_EXTENSION:
            return "File upload stopped by extension";
        default:
            return "Unknown upload error";
    }
}

/**
 * Function to create a new material
 * Admin can create materials for any course, teachers can only create materials for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The material title
 * @param string $content The material content
 * @param int $moduleId The module ID (optional)
 * @param int $createdBy The user ID of the creator
 * @return int|string Material ID if creation successful, error message otherwise
 */
function createMaterial($courseId, $title, $content, $moduleId = null, $createdBy) {
    global $pdo;

    try {
        // Check if user is authorized to create materials for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->bindParam(':userId', $createdBy);
            $stmt->bindParam(':instructorId', $createdBy);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to create materials for this course.";
            }
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create the activity with type 'material'
        $stmt = $pdo->prepare("
            INSERT INTO activities (course_id, title, description, activity_type, points, is_published, created_by)
            VALUES (:courseId, :title, :content, 'material', 0, 1, :createdBy)
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':createdBy', $createdBy);

        $stmt->execute();

        $activityId = $pdo->lastInsertId();

        // If module is selected, associate the activity with the module
        if ($moduleId) {
            $stmt = $pdo->prepare("
                INSERT INTO module_activities (module_id, activity_id)
                VALUES (:moduleId, :activityId)
            ");

            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->bindParam(':activityId', $activityId);

            $stmt->execute();
        }

        // Commit transaction
        $pdo->commit();

        return $activityId;
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to create material: " . $e->getMessage();
    }
}
?>
