<?php
// Include configuration file
require_once 'includes/config.php';

// Get the database connection
global $pdo;

// Add class_code column to courses table if it doesn't exist
try {
    // Check if the column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM courses LIKE 'class_code'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $stmt = $pdo->prepare("ALTER TABLE courses ADD COLUMN class_code VARCHAR(10) UNIQUE AFTER description");
        $stmt->execute();
        echo "Added class_code column to courses table.<br>";
        
        // Update existing courses with random class codes
        $stmt = $pdo->prepare("SELECT course_id FROM courses");
        $stmt->execute();
        $courses = $stmt->fetchAll();
        
        foreach ($courses as $course) {
            $classCode = generateRandomCode(6);
            $updateStmt = $pdo->prepare("UPDATE courses SET class_code = :classCode WHERE course_id = :courseId");
            $updateStmt->bindParam(':classCode', $classCode);
            $updateStmt->bindParam(':courseId', $course['course_id']);
            $updateStmt->execute();
        }
        
        echo "Updated existing courses with random class codes.<br>";
    } else {
        echo "class_code column already exists in courses table.<br>";
    }
    
    echo "Database update completed successfully.";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}

/**
 * Function to generate a random alphanumeric code
 * 
 * @param int $length The length of the code
 * @return string The generated code
 */
function generateRandomCode($length = 6) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $code;
}
?>
