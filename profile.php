<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/user_functions.php';

// Check if the user is logged in
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Get user data
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

// Check if user is an error message
if (is_string($user)) {
    $error = $user;
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['update_profile'])) {
        // Update profile
        $email = trim($_POST["email"]);
        $firstName = trim($_POST["first_name"]);
        $lastName = trim($_POST["last_name"]);
        $gender = isset($_POST["gender"]) ? trim($_POST["gender"]) : null;
        $birthday = isset($_POST["birthday"]) ? trim($_POST["birthday"]) : null;
        $phoneNumber = isset($_POST["phone_number"]) ? trim($_POST["phone_number"]) : null;
        $profilePicture = isset($_FILES['profile_picture']) ? $_FILES['profile_picture'] : null;

        // Validate input
        $input_error = false;

        if (empty($email)) {
            $email_err = "Please enter an email.";
            $input_error = true;
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email_err = "Please enter a valid email.";
            $input_error = true;
        }

        if (empty($firstName)) {
            $firstName_err = "Please enter a first name.";
            $input_error = true;
        }

        if (empty($lastName)) {
            $lastName_err = "Please enter a last name.";
            $input_error = true;
        }

        // Validate gender if provided
        if ($gender !== null && !in_array($gender, ['male', 'female', 'other'])) {
            $gender_err = "Please select a valid gender.";
            $input_error = true;
        }

        // Validate birthday if provided
        if (!empty($_POST["birth_month"]) && !empty($_POST["birth_day"]) && !empty($_POST["birth_year"])) {
            $birth_month = trim($_POST["birth_month"]);
            $birth_day = trim($_POST["birth_day"]);
            $birth_year = trim($_POST["birth_year"]);

            // Format as YYYY-MM-DD for database storage
            $birthday = sprintf('%04d-%02d-%02d', $birth_year, $birth_month, $birth_day);

            // Check if the date is valid
            $date = date_create_from_format('Y-m-d', $birthday);
            if (!$date || date_format($date, 'Y-m-d') !== $birthday) {
                $birthday_err = "Please enter a valid date.";
                $input_error = true;
            }
        } else {
            // If any part is missing, set birthday to null
            $birthday = null;
        }

        // Validate phone number if provided
        if ($phoneNumber !== null && !empty($phoneNumber) && !preg_match('/^[0-9+\-\s()]{7,20}$/', $phoneNumber)) {
            $phoneNumber_err = "Please enter a valid phone number.";
            $input_error = true;
        }

        // Validate profile picture if provided
        if ($profilePicture !== null && $profilePicture['size'] > 0) {
            // Check file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!in_array($profilePicture['type'], $allowedTypes)) {
                $profilePicture_err = "Please upload a valid image file (JPEG, PNG, or GIF).";
                $input_error = true;
            }

            // Check file size (max 2MB)
            if ($profilePicture['size'] > 2 * 1024 * 1024) {
                $profilePicture_err = "Profile picture must be less than 2MB.";
                $input_error = true;
            }
        }

        // If no input errors, update the profile
        if (!$input_error) {
            $result = updateUser($userId, $email, $firstName, $lastName, $gender, $birthday, $phoneNumber, $profilePicture);

            if ($result === true) {
                $success = "Profile updated successfully.";
                // Update session variables
                $_SESSION['email'] = $email;
                $_SESSION['first_name'] = $firstName;
                $_SESSION['last_name'] = $lastName;
                // Refresh user data
                $user = getUserById($userId);
            } else {
                $error = $result;
            }
        }
    } elseif (isset($_POST['change_password'])) {
        // Change password
        $currentPassword = trim($_POST["current_password"]);
        $newPassword = trim($_POST["new_password"]);
        $confirmPassword = trim($_POST["confirm_password"]);

        // Validate input
        $input_error = false;

        if (empty($currentPassword)) {
            $currentPassword_err = "Please enter your current password.";
            $input_error = true;
        }

        if (empty($newPassword)) {
            $newPassword_err = "Please enter a new password.";
            $input_error = true;
        } elseif (strlen($newPassword) < 6) {
            $newPassword_err = "Password must have at least 6 characters.";
            $input_error = true;
        }

        if (empty($confirmPassword)) {
            $confirmPassword_err = "Please confirm the new password.";
            $input_error = true;
        } elseif ($newPassword != $confirmPassword) {
            $confirmPassword_err = "Passwords do not match.";
            $input_error = true;
        }

        // If no input errors, change the password
        if (!$input_error) {
            $result = changePassword($userId, $currentPassword, $newPassword);

            if ($result === true) {
                $success = "Password changed successfully.";
            } else {
                $error = $result;
            }
        }
    }
}

// Set page title
$page_title = "My Profile";

// Include header
require_once 'includes/header.php';
?>

<style>
    /* Date input styling */
    input[type="date"] {
        padding-right: 10px;
    }

    /* Select styling for consistency */
    select.form-control {
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0.375rem 0.75rem;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>My Profile</h1>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
<div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<?php if (!isset($error) && isset($user) && !is_string($user)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">System Administrator</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 text-center mb-4">
                <?php if (isset($user['profile_picture']) && !empty($user['profile_picture'])): ?>
                    <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile Picture" class="img-fluid rounded-circle profile-image" style="width: 150px; height: 150px; object-fit: cover;">
                <?php else: ?>
                    <div class="profile-image-placeholder rounded-circle d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; background-color: #e9ecef; margin: 0 auto;">
                        <span style="font-size: 3rem; color: #6c757d;"><?php echo strtoupper(substr($user['first_name'], 0, 1)); ?></span>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Username:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo htmlspecialchars($user['username']); ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Email:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo htmlspecialchars($user['email']); ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Role:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo htmlspecialchars(ucfirst($user['role_name'])); ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Gender:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo isset($user['gender']) ? htmlspecialchars(ucfirst($user['gender'])) : 'Not specified'; ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Birthday:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo isset($user['birthday']) && !empty($user['birthday']) ? date('F j, Y', strtotime($user['birthday'])) : 'Not specified'; ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Phone Number:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo isset($user['phone_number']) && !empty($user['phone_number']) ? htmlspecialchars($user['phone_number']) : 'Not specified'; ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <p class="text-muted mb-1">Joined:</p>
                    </div>
                    <div class="col-md-9">
                        <p class="mb-1"><?php echo date('F j, Y', strtotime($user['created_at'])); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-edit"></i> Update Profile</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label>Profile Picture</label>
                        <div class="custom-file">
                            <input type="file" name="profile_picture" class="custom-file-input <?php echo isset($profilePicture_err) ? 'is-invalid' : ''; ?>" id="profile-picture">
                            <label class="custom-file-label" for="profile-picture">Choose file</label>
                            <span class="invalid-feedback"><?php echo isset($profilePicture_err) ? $profilePicture_err : ''; ?></span>
                        </div>
                        <small class="form-text text-muted">Upload a profile picture (JPEG, PNG, or GIF, max 2MB)</small>
                        <?php if (isset($user['profile_picture']) && !empty($user['profile_picture'])): ?>
                            <div class="mt-2">
                                <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Current Profile Picture" class="img-thumbnail" style="max-width: 100px;">
                                <small class="form-text text-muted">Current profile picture</small>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" name="email" class="form-control <?php echo isset($email_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <span class="invalid-feedback"><?php echo isset($email_err) ? $email_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>First Name</label>
                        <input type="text" name="first_name" class="form-control <?php echo isset($firstName_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($user['first_name']); ?>">
                        <span class="invalid-feedback"><?php echo isset($firstName_err) ? $firstName_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Last Name</label>
                        <input type="text" name="last_name" class="form-control <?php echo isset($lastName_err) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($user['last_name']); ?>">
                        <span class="invalid-feedback"><?php echo isset($lastName_err) ? $lastName_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Gender</label>
                        <select name="gender" class="form-control <?php echo isset($gender_err) ? 'is-invalid' : ''; ?>">
                            <option value="">Select Gender</option>
                            <option value="male" <?php echo (isset($user['gender']) && $user['gender'] == 'male') ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo (isset($user['gender']) && $user['gender'] == 'female') ? 'selected' : ''; ?>>Female</option>
                            <option value="other" <?php echo (isset($user['gender']) && $user['gender'] == 'other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                        <span class="invalid-feedback"><?php echo isset($gender_err) ? $gender_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label for="birthday">Birthday</label>
                        <div class="row">
                            <div class="col-md-4">
                                <select id="birth_month" name="birth_month" class="form-control <?php echo isset($birthday_err) ? 'is-invalid' : ''; ?>">
                                    <option value="">Month</option>
                                    <?php
                                    $months = [
                                        1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                                        5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                                        9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                                    ];

                                    $selected_month = isset($_POST['birth_month']) ? $_POST['birth_month'] : '';
                                    if (empty($selected_month) && isset($user['birthday']) && !empty($user['birthday'])) {
                                        $date_parts = explode('-', $user['birthday']);
                                        if (count($date_parts) == 3) {
                                            $selected_month = (int)$date_parts[1];
                                        }
                                    }

                                    foreach ($months as $num => $name) {
                                        echo '<option value="' . $num . '"' . ($selected_month == $num ? ' selected' : '') . '>' . $name . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select id="birth_day" name="birth_day" class="form-control <?php echo isset($birthday_err) ? 'is-invalid' : ''; ?>">
                                    <option value="">Day</option>
                                    <?php
                                    $selected_day = isset($_POST['birth_day']) ? $_POST['birth_day'] : '';
                                    if (empty($selected_day) && isset($user['birthday']) && !empty($user['birthday'])) {
                                        $date_parts = explode('-', $user['birthday']);
                                        if (count($date_parts) == 3) {
                                            $selected_day = (int)$date_parts[2];
                                        }
                                    }

                                    for ($i = 1; $i <= 31; $i++) {
                                        echo '<option value="' . $i . '"' . ($selected_day == $i ? ' selected' : '') . '>' . $i . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select id="birth_year" name="birth_year" class="form-control <?php echo isset($birthday_err) ? 'is-invalid' : ''; ?>">
                                    <option value="">Year</option>
                                    <?php
                                    $current_year = (int)date('Y');
                                    $selected_year = isset($_POST['birth_year']) ? $_POST['birth_year'] : '';
                                    if (empty($selected_year) && isset($user['birthday']) && !empty($user['birthday'])) {
                                        $date_parts = explode('-', $user['birthday']);
                                        if (count($date_parts) == 3) {
                                            $selected_year = (int)$date_parts[0];
                                        }
                                    }

                                    for ($i = $current_year; $i >= $current_year - 100; $i--) {
                                        echo '<option value="' . $i . '"' . ($selected_year == $i ? ' selected' : '') . '>' . $i . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <span class="invalid-feedback"><?php echo isset($birthday_err) ? $birthday_err : ''; ?></span>
                        <small class="form-text text-muted">Your date of birth</small>
                    </div>
                    <div class="form-group">
                        <label>Phone Number</label>
                        <input type="text" name="phone_number" class="form-control <?php echo isset($phoneNumber_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($user['phone_number']) ? htmlspecialchars($user['phone_number']) : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($phoneNumber_err) ? $phoneNumber_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <input type="submit" name="update_profile" class="btn btn-primary" value="Update Profile">
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key"></i> Change Password</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div class="form-group">
                        <label>Current Password</label>
                        <input type="password" name="current_password" class="form-control <?php echo isset($currentPassword_err) ? 'is-invalid' : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($currentPassword_err) ? $currentPassword_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>New Password</label>
                        <input type="password" name="new_password" class="form-control <?php echo isset($newPassword_err) ? 'is-invalid' : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($newPassword_err) ? $newPassword_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Confirm New Password</label>
                        <input type="password" name="confirm_password" class="form-control <?php echo isset($confirmPassword_err) ? 'is-invalid' : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($confirmPassword_err) ? $confirmPassword_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <input type="submit" name="change_password" class="btn btn-primary" value="Change Password">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Add JavaScript for file input
?>
<script>
// Show file name in custom file input
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('profile-picture');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'Choose file';
            const label = this.nextElementSibling;
            label.textContent = fileName;
        });
    }


});
</script>
<?php
// Include footer
require_once 'includes/footer.php';
?>
