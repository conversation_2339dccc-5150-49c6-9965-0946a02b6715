<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher, admin, or student (for complete, archive, restore actions)
if (!isTeacher() && !isAdmin() && !(isStudent() && in_array($_GET['action'], ['complete', 'archive', 'restore']))) {
    $_SESSION['error'] = "You do not have permission to perform this action.";
    header("location: index.php");
    exit;
}

// Check if action and course ID are provided
if (!isset($_GET['action']) || !isset($_GET['id'])) {
    $_SESSION['error'] = "Invalid request.";
    header("location: index.php");
    exit;
}

$action = $_GET['action'];
$courseId = intval($_GET['id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has permission to modify this course
// Students can only complete, archive, or restore courses they're enrolled in
if (isStudent() && in_array($action, ['complete', 'archive', 'restore'])) {
    if (!isEnrolled($_SESSION['user_id'], $courseId)) {
        $_SESSION['error'] = "You are not enrolled in this course.";
        header("location: index.php");
        exit;
    }
} else if (!isAdmin() && $course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You do not have permission to modify this course.";
    header("location: index.php");
    exit;
}

// Process the action
switch ($action) {
    case 'activate':
        // Only course creator or admin can activate a course
        if (!isAdmin() && $course['created_by'] != $_SESSION['user_id']) {
            $_SESSION['error'] = "Only the course creator or an administrator can activate this course.";
            header("location: course_view_full.php?id=$courseId");
            exit;
        }

        $result = updateCourse($courseId, $course['title'], $course['description'], $course['class_code'], 1);

        if ($result === true) {
            $_SESSION['success'] = "Course activated successfully. Students can now access this course.";
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    case 'deactivate':
        // Course creator, instructor, or admin can deactivate a course
        $result = updateCourse($courseId, $course['title'], $course['description'], $course['class_code'], 0);

        if ($result === true) {
            $_SESSION['success'] = "Course deactivated successfully. Students will no longer be able to access this course.";
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    case 'delete':
        // Only course creator or admin can delete a course
        if (!isAdmin() && $course['created_by'] != $_SESSION['user_id']) {
            $_SESSION['error'] = "Only the course creator or an administrator can delete this course.";
            header("location: course_view_full.php?id=$courseId");
            exit;
        }

        $result = deleteCourse($courseId);

        if ($result === true) {
            $_SESSION['success'] = "Course deleted successfully.";
            header("location: index.php");
            exit;
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    case 'complete':
        // Students can mark their own enrollment as complete
        if (isStudent()) {
            $result = updateCourseCompletionStatus($_SESSION['user_id'], $courseId, 'completed');

            if ($result === true) {
                // Also archive the course for the student
                archiveCourse($_SESSION['user_id'], $courseId);
                $_SESSION['success'] = "Course marked as completed successfully and moved to archive.";
                header("location: index.php");
                exit;
            } else {
                $_SESSION['error'] = $result;
            }
        }
        // Instructors can mark the course as complete for all enrolled students
        else if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))) {
            $result = markCourseCompleteForAllStudents($courseId);

            if ($result === true) {
                // markCourseCompleteForAllStudents already archives the course, no need to call archiveCourse again
                $_SESSION['success'] = "Course marked as completed for all enrolled students and moved to archive.";
                header("location: index.php");
                exit;
            } else {
                $_SESSION['error'] = $result;
            }
        } else {
            $_SESSION['error'] = "You do not have permission to perform this action.";
        }
        break;

    case 'archive':
        // Only instructors can archive courses
        if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))) {
            $result = archiveCourse($_SESSION['user_id'], $courseId);

            if ($result === true) {
                $_SESSION['success'] = "Course archived successfully.";
                header("location: index.php");
                exit;
            } else {
                $_SESSION['error'] = $result;
            }
        } else {
            $_SESSION['error'] = "Only instructors can archive courses.";
        }
        break;

    case 'restore':
        // Only instructors can restore archived courses
        if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))) {
            $result = restoreCourse($_SESSION['user_id'], $courseId);

            if ($result === true) {
                $_SESSION['success'] = "Course restored successfully.";
                header("location: archive.php");
                exit;
            } else {
                $_SESSION['error'] = $result;
            }
        } else {
            $_SESSION['error'] = "Only instructors can restore archived courses.";
        }
        break;

    default:
        $_SESSION['error'] = "Invalid action.";
        break;
}

// Redirect back to the course page
header("location: course_view_full.php?id=$courseId");
exit;
