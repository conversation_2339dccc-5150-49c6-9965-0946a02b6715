-- Calendar Events Table
CREATE TABLE IF NOT EXISTS calendar_events (
    event_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    course VARCHAR(100),
    course_id INT,
    item_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Insert some sample events for testing
INSERT INTO calendar_events (user_id, title, description, event_date, event_type, course, course_id, item_id)
VALUES 
(1, 'System Maintenance', 'Scheduled maintenance of the e-learning platform. System may be down for 1-2 hours.', 
 DATE_ADD(CURDATE(), INTERVAL 3 DAY), 'system', 'System', NULL, NULL),
 
(1, 'New User Registrations', 'Process new user registration requests and set up accounts.', 
 DATE_ADD(CURDATE(), INTERVAL 5 DAY), 'admin', 'Admin', NULL, NULL),
 
(1, 'Monthly Report Generation', 'Generate and review monthly activity reports for all courses.', 
 DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'report', 'Admin', NULL, NULL),
 
(1, 'Teacher Meeting', 'Monthly meeting with all teachers to discuss platform updates and improvements.', 
 DATE_ADD(CURDATE(), INTERVAL 10 DAY), 'meeting', 'Admin', NULL, NULL);
