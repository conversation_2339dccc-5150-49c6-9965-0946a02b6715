<?php
// Include configuration file
require_once 'includes/config.php';

// Set page title
$page_title = "Update Quiz Tables";

// Include header
require_once 'includes/header.php';

// Check if the user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Function to check if a column exists in a table
function columnExists($tableName, $columnName) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM $tableName LIKE :columnName");
        $stmt->bindParam(':columnName', $columnName);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to execute SQL with error handling
function executeSQL($sql, $description) {
    global $pdo;
    
    try {
        $pdo->exec($sql);
        echo "<div class='alert alert-success'>$description: Success</div>";
        return true;
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>$description: Error - " . $e->getMessage() . "</div>";
        return false;
    }
}

// Function to display table structure
function displayTableStructure($tableName) {
    global $pdo;
    
    try {
        // Get table structure
        $stmt = $pdo->prepare("DESCRIBE $tableName");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table: $tableName</h3>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

// Update the quiz_questions table
$quizQuestionsUpdated = false;
$quizOptionsUpdated = false;

// Check if quiz_questions table has quiz_id column
$hasQuizId = columnExists('quiz_questions', 'quiz_id');
$hasActivityId = columnExists('quiz_questions', 'activity_id');

// Check if quiz_questions table has position column
$hasPosition = columnExists('quiz_questions', 'position');

// Check if quiz_options table has position column
$hasOptionPosition = columnExists('quiz_options', 'position');

?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Update Quiz Tables</h1>
            <p>This page updates the database structure for quiz tables.</p>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Table Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display current structure
                    displayTableStructure('quiz_questions');
                    displayTableStructure('quiz_options');
                    ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Update Database Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Update quiz_questions table
                    if ($hasQuizId && !$hasActivityId) {
                        // Rename quiz_id to activity_id
                        $sql = "ALTER TABLE quiz_questions CHANGE quiz_id activity_id INT(11) NOT NULL;";
                        $quizQuestionsUpdated = executeSQL($sql, "Rename quiz_id to activity_id in quiz_questions table");
                    } elseif (!$hasQuizId && !$hasActivityId) {
                        // Add activity_id column
                        $sql = "ALTER TABLE quiz_questions ADD COLUMN activity_id INT(11) NOT NULL AFTER question_id;";
                        $quizQuestionsUpdated = executeSQL($sql, "Add activity_id column to quiz_questions table");
                    } elseif ($hasQuizId && $hasActivityId) {
                        // Both columns exist, drop quiz_id
                        $sql = "ALTER TABLE quiz_questions DROP COLUMN quiz_id;";
                        $quizQuestionsUpdated = executeSQL($sql, "Drop quiz_id column from quiz_questions table");
                    } else {
                        echo "<div class='alert alert-info'>quiz_questions table already has activity_id column.</div>";
                        $quizQuestionsUpdated = true;
                    }
                    
                    // Add position column to quiz_questions if it doesn't exist
                    if (!$hasPosition) {
                        $sql = "ALTER TABLE quiz_questions ADD COLUMN position INT(11) NOT NULL DEFAULT 0 AFTER points;";
                        $positionAdded = executeSQL($sql, "Add position column to quiz_questions table");
                        $quizQuestionsUpdated = $quizQuestionsUpdated && $positionAdded;
                    } else {
                        echo "<div class='alert alert-info'>quiz_questions table already has position column.</div>";
                    }
                    
                    // Add position column to quiz_options if it doesn't exist
                    if (!$hasOptionPosition) {
                        $sql = "ALTER TABLE quiz_options ADD COLUMN position INT(11) NOT NULL DEFAULT 0 AFTER is_correct;";
                        $quizOptionsUpdated = executeSQL($sql, "Add position column to quiz_options table");
                    } else {
                        echo "<div class='alert alert-info'>quiz_options table already has position column.</div>";
                        $quizOptionsUpdated = true;
                    }
                    
                    // Display overall status
                    if ($quizQuestionsUpdated && $quizOptionsUpdated) {
                        echo "<div class='alert alert-success mt-3'><strong>Database structure updated successfully!</strong></div>";
                    } else {
                        echo "<div class='alert alert-warning mt-3'><strong>Some updates failed. Please check the messages above.</strong></div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Updated Table Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display updated structure
                    displayTableStructure('quiz_questions');
                    displayTableStructure('quiz_options');
                    ?>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <a href="quiz_edit.php?id=7&manage_questions=1#questions" class="btn btn-primary">Return to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
