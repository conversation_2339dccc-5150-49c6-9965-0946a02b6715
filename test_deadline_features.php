<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Test Deadline Features";
$current_page = "test";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Testing Deadline Features</h1>
            
            <div class="alert alert-info">
                <h4>Changes Made:</h4>
                <p>The following changes have been implemented:</p>
                <ul>
                    <li>Assignments, quizzes, and activities are now automatically closed once the deadline passes</li>
                    <li>Students cannot take quizzes after the deadline has passed (unless late submissions are allowed)</li>
                    <li>Activities with passed deadlines are marked as "Missing" in the classwork list</li>
                    <li>Late submissions are properly tracked and marked as late</li>
                </ul>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Test Cases</h5>
                </div>
                <div class="card-body">
                    <h6>1. Create a test activity with a past due date</h6>
                    <p>Create an activity with a due date in the past. Verify that:</p>
                    <ul>
                        <li>The activity shows as "Missing" in the classwork list for students</li>
                        <li>Students cannot submit the activity unless late submissions are allowed</li>
                        <li>The activity shows a "Missing" status in the student's view</li>
                    </ul>
                    
                    <h6>2. Create a test activity with a future due date</h6>
                    <p>Create an activity with a due date in the future. Verify that:</p>
                    <ul>
                        <li>The activity shows as "Assigned" in the classwork list for students</li>
                        <li>Students can submit the activity</li>
                        <li>The activity shows an "Assigned" status in the student's view</li>
                    </ul>
                    
                    <h6>3. Create a test activity with a past due date but allow late submissions</h6>
                    <p>Create an activity with a due date in the past, but check "Allow Late Submissions". Verify that:</p>
                    <ul>
                        <li>Students can still submit the activity</li>
                        <li>The submission is marked as "Late"</li>
                        <li>The activity shows a warning about late submission</li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>Test Results</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="test-results">Enter your test results here:</label>
                        <textarea id="test-results" class="form-control" rows="6" placeholder="Document your test results..."></textarea>
                    </div>
                    <button id="save-results" class="btn btn-primary">Save Results</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('save-results').addEventListener('click', function() {
    const results = document.getElementById('test-results').value;
    if (results.trim() === '') {
        alert('Please enter test results before saving.');
        return;
    }
    
    // In a real application, you would save this to the server
    alert('Test results saved successfully!');
    
    // Clear the textarea
    document.getElementById('test-results').value = '';
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
