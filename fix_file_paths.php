<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Fix File Paths";

// Include header
require_once 'includes/header.php';

$messages = [];
$errors = [];

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['fix_paths'])) {
    try {
        global $pdo;
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Fix activity_files table
        $stmt = $pdo->prepare("SELECT * FROM activity_files");
        $stmt->execute();
        $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $fixedActivityFiles = 0;
        
        foreach ($files as $file) {
            $filePath = $file['file_path'];
            $fileId = $file['file_id'];
            
            // Check if the file path starts with C:\ or other absolute path
            if (preg_match('/^[A-Z]:\\\\/', $filePath) || strpos($filePath, $_SERVER['DOCUMENT_ROOT']) === 0) {
                // Extract the relative path
                $relativePath = '';
                
                if (strpos($filePath, 'uploads/') !== false) {
                    $relativePath = substr($filePath, strpos($filePath, 'uploads/'));
                } else if (strpos($filePath, 'uploads\\') !== false) {
                    $relativePath = str_replace('\\', '/', substr($filePath, strpos($filePath, 'uploads\\')));
                }
                
                if (!empty($relativePath)) {
                    // Update the file path in the database
                    $updateStmt = $pdo->prepare("UPDATE activity_files SET file_path = :filePath WHERE file_id = :fileId");
                    $updateStmt->bindParam(':filePath', $relativePath);
                    $updateStmt->bindParam(':fileId', $fileId);
                    $updateStmt->execute();
                    
                    $fixedActivityFiles++;
                }
            }
        }
        
        // Fix assignment_submissions table
        $stmt = $pdo->prepare("SELECT * FROM assignment_submissions WHERE file_path IS NOT NULL");
        $stmt->execute();
        $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $fixedSubmissions = 0;
        
        foreach ($submissions as $submission) {
            $filePath = $submission['file_path'];
            $submissionId = $submission['submission_id'];
            
            // Check if the file path starts with C:\ or other absolute path
            if (preg_match('/^[A-Z]:\\\\/', $filePath) || strpos($filePath, $_SERVER['DOCUMENT_ROOT']) === 0) {
                // Extract the relative path
                $relativePath = '';
                
                if (strpos($filePath, 'uploads/') !== false) {
                    $relativePath = substr($filePath, strpos($filePath, 'uploads/'));
                } else if (strpos($filePath, 'uploads\\') !== false) {
                    $relativePath = str_replace('\\', '/', substr($filePath, strpos($filePath, 'uploads\\')));
                }
                
                if (!empty($relativePath)) {
                    // Update the file path in the database
                    $updateStmt = $pdo->prepare("UPDATE assignment_submissions SET file_path = :filePath WHERE submission_id = :submissionId");
                    $updateStmt->bindParam(':filePath', $relativePath);
                    $updateStmt->bindParam(':submissionId', $submissionId);
                    $updateStmt->execute();
                    
                    $fixedSubmissions++;
                }
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        $messages[] = "Fixed $fixedActivityFiles file paths in activity_files table.";
        $messages[] = "Fixed $fixedSubmissions file paths in assignment_submissions table.";
        
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        $errors[] = "Database error: " . $e->getMessage();
    }
}

// Get file path statistics
$stats = [];

try {
    global $pdo;
    
    // Activity files stats
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM activity_files");
    $stats['activity_files_total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM activity_files WHERE file_path LIKE 'uploads/%'");
    $stats['activity_files_relative'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM activity_files WHERE file_path LIKE 'C:%' OR file_path LIKE '/var/%'");
    $stats['activity_files_absolute'] = $stmt->fetchColumn();
    
    // Submission files stats
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assignment_submissions WHERE file_path IS NOT NULL");
    $stats['submissions_total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM assignment_submissions WHERE file_path LIKE 'uploads/%'");
    $stats['submissions_relative'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM assignment_submissions WHERE file_path LIKE 'C:%' OR file_path LIKE '/var/%'");
    $stats['submissions_absolute'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $errors[] = "Database error: " . $e->getMessage();
}
?>

<div class="container mt-4">
    <h1>Fix File Paths</h1>
    
    <?php if (!empty($messages)): ?>
    <div class="alert alert-success">
        <?php foreach ($messages as $message): ?>
        <p><?php echo $message; ?></p>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <?php foreach ($errors as $error): ?>
        <p><?php echo $error; ?></p>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">File Path Statistics</h5>
        </div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>Table</th>
                        <th>Total Files</th>
                        <th>Relative Paths</th>
                        <th>Absolute Paths</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>activity_files</td>
                        <td><?php echo isset($stats['activity_files_total']) ? $stats['activity_files_total'] : 'N/A'; ?></td>
                        <td><?php echo isset($stats['activity_files_relative']) ? $stats['activity_files_relative'] : 'N/A'; ?></td>
                        <td><?php echo isset($stats['activity_files_absolute']) ? $stats['activity_files_absolute'] : 'N/A'; ?></td>
                    </tr>
                    <tr>
                        <td>assignment_submissions</td>
                        <td><?php echo isset($stats['submissions_total']) ? $stats['submissions_total'] : 'N/A'; ?></td>
                        <td><?php echo isset($stats['submissions_relative']) ? $stats['submissions_relative'] : 'N/A'; ?></td>
                        <td><?php echo isset($stats['submissions_absolute']) ? $stats['submissions_absolute'] : 'N/A'; ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Fix File Paths</h5>
        </div>
        <div class="card-body">
            <p>This tool will convert absolute file paths to relative paths in the database. This is useful if you've moved your site to a different server or directory.</p>
            <p><strong>Warning:</strong> This operation cannot be undone. Please backup your database before proceeding.</p>
            
            <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                <button type="submit" name="fix_paths" class="btn btn-primary" onclick="return confirm('Are you sure you want to fix file paths? This cannot be undone.');">Fix File Paths</button>
                <a href="file_debug.php" class="btn btn-secondary">View File Debug</a>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
