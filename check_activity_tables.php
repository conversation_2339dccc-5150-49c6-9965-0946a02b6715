<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Checking Activity-Related Tables</h1>";

try {
    // Check if activity_submissions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_submissions'");
    $activitySubmissionsExists = $stmt->rowCount() > 0;
    
    if (!$activitySubmissionsExists) {
        echo "<p>Creating activity_submissions table...</p>";
        
        // Create activity_submissions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `activity_submissions` (
                `submission_id` int(11) NOT NULL AUTO_INCREMENT,
                `activity_id` int(11) NOT NULL,
                `user_id` int(11) NOT NULL,
                `content` text DEFAULT NULL,
                `file_path` varchar(255) DEFAULT NULL,
                `file_name` varchar(255) DEFAULT NULL,
                `file_type` varchar(100) DEFAULT NULL,
                `file_size` int(11) DEFAULT NULL,
                `submission_date` datetime NOT NULL,
                `is_late` tinyint(1) DEFAULT 0,
                `grade` decimal(5,2) DEFAULT NULL,
                `feedback` text DEFAULT NULL,
                `graded_by` int(11) DEFAULT NULL,
                `graded_at` datetime DEFAULT NULL,
                PRIMARY KEY (`submission_id`),
                KEY `activity_id` (`activity_id`),
                KEY `user_id` (`user_id`),
                KEY `graded_by` (`graded_by`),
                CONSTRAINT `activity_submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_3` FOREIGN KEY (`graded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");
        
        echo "<p>✓ Activity Submissions table created successfully.</p>";
    } else {
        echo "<p>✓ Activity Submissions table already exists.</p>";
    }
    
    // Check if activity_questions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
    $activityQuestionsExists = $stmt->rowCount() > 0;
    
    if (!$activityQuestionsExists) {
        echo "<p>Activity Questions table does not exist. Please run create_activity_questions_tables.php</p>";
    } else {
        echo "<p>✓ Activity Questions table exists.</p>";
    }
    
    // Check if activity_question_options table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_question_options'");
    $activityQuestionOptionsExists = $stmt->rowCount() > 0;
    
    if (!$activityQuestionOptionsExists) {
        echo "<p>Activity Question Options table does not exist. Please run create_activity_questions_tables.php</p>";
    } else {
        echo "<p>✓ Activity Question Options table exists.</p>";
    }
    
    // Check if activity_answers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
    $activityAnswersExists = $stmt->rowCount() > 0;
    
    if (!$activityAnswersExists) {
        echo "<p>Activity Answers table does not exist. Please run create_activity_questions_tables.php</p>";
    } else {
        echo "<p>✓ Activity Answers table exists.</p>";
    }
    
    echo "<p><a href='assignment_edit.php?id=5' class='btn btn-primary'>Return to Assignment Edit</a></p>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error checking tables: " . $e->getMessage() . "</div>";
}
?>
