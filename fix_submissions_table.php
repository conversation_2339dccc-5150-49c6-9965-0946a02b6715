<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Fixing Submissions Tables</h1>";

try {
    // Check if activity_submissions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_submissions'");
    $activitySubmissionsExists = $stmt->rowCount() > 0;

    if (!$activitySubmissionsExists) {
        echo "<p>Activity Submissions table does not exist. Creating it now...</p>";

        // Create activity_submissions table with all required columns
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `activity_submissions` (
                `submission_id` int(11) NOT NULL AUTO_INCREMENT,
                `activity_id` int(11) NOT NULL,
                `user_id` int(11) NOT NULL,
                `submission_content` text DEFAULT NULL,
                `file_path` varchar(255) DEFAULT NULL,
                `file_name` varchar(255) DEFAULT NULL,
                `file_type` varchar(100) DEFAULT NULL,
                `file_size` int(11) DEFAULT NULL,
                `submission_date` datetime NOT NULL,
                `is_late` tinyint(1) DEFAULT 0,
                `grade` decimal(5,2) DEFAULT NULL,
                `feedback` text DEFAULT NULL,
                `graded_by` int(11) DEFAULT NULL,
                `graded_at` datetime DEFAULT NULL,
                PRIMARY KEY (`submission_id`),
                KEY `activity_id` (`activity_id`),
                KEY `user_id` (`user_id`),
                KEY `graded_by` (`graded_by`),
                CONSTRAINT `activity_submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_3` FOREIGN KEY (`graded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        echo "<p>✓ Activity Submissions table created successfully.</p>";
    } else {
        echo "<p>✓ Activity Submissions table exists.</p>";
    }

    // Check if the code is looking for a 'submissions' table instead of 'activity_submissions'
    // Create a view to make 'submissions' point to 'activity_submissions'
    $stmt = $pdo->query("SHOW TABLES LIKE 'submissions'");
    $submissionsExists = $stmt->rowCount() > 0;

    // Check if it's a view or a table
    $isView = false;
    if ($submissionsExists) {
        $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_name = 'submissions' AND Table_type = 'VIEW'");
        $isView = $stmt->rowCount() > 0;
    }

    if (!$submissionsExists || !$isView) {
        echo "<p>Creating 'submissions' view to point to 'activity_submissions' table...</p>";

        // Drop the table or view if it exists
        $pdo->exec("DROP TABLE IF EXISTS `submissions`");
        $pdo->exec("DROP VIEW IF EXISTS `submissions`");

        // Create the view with explicit column mapping
        $pdo->exec("
            CREATE VIEW `submissions` AS
            SELECT
                `submission_id`,
                `activity_id` AS `assignment_id`,
                `user_id`,
                `submission_content` AS `content`,
                `file_path`,
                `file_name`,
                `file_type`,
                `file_size`,
                `submission_date`,
                `is_late`,
                `grade`,
                `feedback`,
                `graded_by`,
                `graded_at`
            FROM `activity_submissions`
        ");

        echo "<p>✓ 'submissions' view created successfully.</p>";
    } else {
        echo "<p>✓ 'submissions' view already exists.</p>";
    }

    // Create activity_answers table if it doesn't exist
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
    $activityAnswersExists = $stmt->rowCount() > 0;

    if (!$activityAnswersExists) {
        echo "<p>Activity Answers table does not exist. Creating it now...</p>";

        // Create activity_answers table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `activity_answers` (
                `answer_id` int(11) NOT NULL AUTO_INCREMENT,
                `submission_id` int(11) NOT NULL,
                `question_id` int(11) NOT NULL,
                `answer_text` text DEFAULT NULL,
                `selected_option_id` int(11) DEFAULT NULL,
                `is_correct` tinyint(1) DEFAULT NULL,
                `points_earned` decimal(10,2) DEFAULT NULL,
                PRIMARY KEY (`answer_id`),
                KEY `submission_id` (`submission_id`),
                KEY `question_id` (`question_id`),
                KEY `selected_option_id` (`selected_option_id`),
                CONSTRAINT `activity_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        echo "<p>✓ Activity Answers table created successfully.</p>";
    } else {
        echo "<p>✓ Activity Answers table exists.</p>";
    }

    echo "<p>Database tables have been fixed. You can now <a href='index.php'>return to the home page</a>.</p>";

} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error fixing tables: " . $e->getMessage() . "</div>";
}
