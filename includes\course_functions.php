<?php
/**
 * Course Management Functions
 *
 * This file contains functions related to course management.
 */

require_once 'config.php';
require_once 'user_functions.php';

/**
 * Function to create a new course
 * Only admin and teachers can create courses
 *
 * @param string $title The course title
 * @param string $description The course description
 * @param int $createdBy The user ID of the creator
 * @param string $classCode Optional class code (will be generated if not provided)
 * @param int $capacity Optional capacity limit for the course
 * @param string $semester Optional semester (first or second)
 * @return bool|string True if creation successful, error message otherwise
 */
function createCourse($title, $description, $createdBy, $classCode = null, $capacity = null, $semester = null) {
    global $pdo;

    // Only admin and teachers can create courses
    if (!isAdmin() && !isTeacher()) {
        return "Only administrators and teachers can create courses.";
    }

    // Generate a random class code if not provided
    if ($classCode === null) {
        $classCode = generateClassCode();
    }

    try {
        $stmt = $pdo->prepare("
            INSERT INTO courses (title, description, class_code, capacity, semester, created_by)
            VALUES (:title, :description, :classCode, :capacity, :semester, :createdBy)
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':classCode', $classCode);
        $stmt->bindParam(':capacity', $capacity);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':createdBy', $createdBy);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        // Check if it's a duplicate class code error
        if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'class_code') !== false) {
            // Try again with a new class code
            return createCourse($title, $description, $createdBy);
        }
        return "Failed to create course: " . $e->getMessage();
    }
}

/**
 * Function to generate a unique class code
 *
 * @return string The generated class code
 */
function generateClassCode() {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';

    for ($i = 0; $i < 6; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $code;
}

/**
 * Function to update a course
 * Admin can update any course, teachers can only update their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The course title
 * @param string $description The course description
 * @param string $classCode The class code (optional)
 * @param bool $isActive The active status (optional)
 * @param int $capacity The capacity limit for the course (optional)
 * @param string $semester The semester (first or second) (optional)
 * @return bool|string True if update successful, error message otherwise
 */
function updateCourse($courseId, $title, $description, $classCode = null, $isActive = null, $capacity = null, $semester = null) {
    global $pdo;

    try {
        // Check if user is authorized to update this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $course = $stmt->fetch();

                if (!isTeacher() || $course['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this course.";
                }
            } else {
                return "Course not found.";
            }
        }

        // Start building the query
        $query = "UPDATE courses SET title = :title, description = :description";
        $params = [
            ':title' => $title,
            ':description' => $description,
            ':courseId' => $courseId
        ];

        // Update class code if provided
        if ($classCode !== null) {
            $query .= ", class_code = :classCode";
            $params[':classCode'] = $classCode;
        }

        // Update capacity if provided
        if ($capacity !== null) {
            $query .= ", capacity = :capacity";
            $params[':capacity'] = $capacity;
        }

        // Update semester if provided
        if ($semester !== null) {
            $query .= ", semester = :semester";
            $params[':semester'] = $semester;
        }

        // Admin can update active status for any course
        if (isAdmin() && $isActive !== null) {
            $query .= ", is_active = :isActive";
            $params[':isActive'] = $isActive;
        }

        // Teachers can activate or deactivate their own courses
        if (isTeacher() && $isActive !== null) {
            $query .= ", is_active = :isActive";
            $params[':isActive'] = $isActive;
        }

        $query .= " WHERE course_id = :courseId";

        // Execute the query
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return true;
    } catch (PDOException $e) {
        // Check if it's a duplicate class code error
        if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'class_code') !== false) {
            return "This class code is already in use. Please choose a different one.";
        }
        return "Failed to update course: " . $e->getMessage();
    }
}

/**
 * Function to delete a course
 * Admin can delete any course, teachers can only delete their own courses
 *
 * @param int $courseId The course ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteCourse($courseId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $course = $stmt->fetch();

                if (!isTeacher() || $course['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this course.";
                }
            } else {
                return "Course not found.";
            }
        }

        // Delete the course
        $stmt = $pdo->prepare("DELETE FROM courses WHERE course_id = :courseId");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Course not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete course: " . $e->getMessage();
    }
}

/**
 * Function to get all courses
 *
 * @param bool $activeOnly Whether to get only active courses
 * @return array|string Array of courses if successful, error message otherwise
 */
function getAllCourses($activeOnly = true) {
    global $pdo;

    try {
        $query = "
            SELECT c.course_id, c.title, c.description, c.is_active,
                   c.created_at, c.updated_at, u.username as creator_name
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
        ";

        if ($activeOnly) {
            $query .= " WHERE c.is_active = 1";
        }

        $query .= " ORDER BY c.course_id";

        $stmt = $pdo->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve courses: " . $e->getMessage();
    }
}

/**
 * Function to get a course by ID
 *
 * @param int $courseId The course ID
 * @return array|string Course data if successful, error message otherwise
 */
function getCourseById($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT c.course_id, c.title, c.description, c.class_code, c.is_active,
                   c.created_by, c.created_at, c.updated_at, c.is_archived,
                   u.username as creator_name
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            WHERE c.course_id = :courseId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            // Check if the course exists at all
            $stmt = $pdo->prepare("SELECT course_id FROM courses WHERE course_id = :courseId");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "Course not found.";
            } else {
                return "Course information is incomplete.";
            }
        }
    } catch (PDOException $e) {
        return "Failed to retrieve course: " . $e->getMessage();
    }
}

/**
 * Function to archive a course
 *
 * @param int $userId The user ID (instructor)
 * @param int $courseId The course ID
 * @return bool|string True if successful, error message otherwise
 */
function archiveCourse($userId, $courseId) {
    global $pdo;

    try {
        // Only teachers can archive courses now
        if (isTeacher()) {
            // Check if the user is the course creator or an instructor
            $course = getCourseById($courseId);

            if (!is_string($course) && ($course['created_by'] == $userId || isInstructor($userId, $courseId))) {
                // Archive the entire course
                $stmt = $pdo->prepare("
                    UPDATE courses
                    SET is_archived = 1
                    WHERE course_id = :courseId
                ");
                $stmt->bindParam(':courseId', $courseId);
                $stmt->execute();

                // Also archive all student enrollments for this course
                $stmt = $pdo->prepare("
                    UPDATE enrollments
                    SET is_archived = 1
                    WHERE course_id = :courseId
                ");
                $stmt->bindParam(':courseId', $courseId);
                $stmt->execute();

                // If the user is an instructor, also archive their relationship
                if (isInstructor($userId, $courseId)) {
                    $stmt = $pdo->prepare("
                        UPDATE course_instructors
                        SET is_archived = 1
                        WHERE instructor_id = :userId AND course_id = :courseId
                    ");
                    $stmt->bindParam(':userId', $userId);
                    $stmt->bindParam(':courseId', $courseId);
                    $stmt->execute();
                }

                return true;
            } else {
                return "You don't have permission to archive this course.";
            }
        } else {
            return "Only instructors can archive courses.";
        }
    } catch (PDOException $e) {
        return "Failed to archive course: " . $e->getMessage();
    }
}

/**
 * Function to restore an archived course
 *
 * @param int $userId The user ID (instructor)
 * @param int $courseId The course ID
 * @return bool|string True if successful, error message otherwise
 */
function restoreCourse($userId, $courseId) {
    global $pdo;

    try {
        // Only teachers can restore courses now
        if (isTeacher()) {
            // Check if the user is the course creator or an instructor
            $course = getCourseById($courseId);

            if (!is_string($course) && ($course['created_by'] == $userId || isInstructor($userId, $courseId))) {
                // Restore the entire course
                $stmt = $pdo->prepare("
                    UPDATE courses
                    SET is_archived = 0
                    WHERE course_id = :courseId
                ");
                $stmt->bindParam(':courseId', $courseId);
                $stmt->execute();

                // Also restore all student enrollments for this course
                $stmt = $pdo->prepare("
                    UPDATE enrollments
                    SET is_archived = 0
                    WHERE course_id = :courseId
                ");
                $stmt->bindParam(':courseId', $courseId);
                $stmt->execute();

                // If the user is an instructor, also restore their relationship
                if (isInstructor($userId, $courseId)) {
                    $stmt = $pdo->prepare("
                        UPDATE course_instructors
                        SET is_archived = 0
                        WHERE instructor_id = :userId AND course_id = :courseId
                    ");
                    $stmt->bindParam(':userId', $userId);
                    $stmt->bindParam(':courseId', $courseId);
                    $stmt->execute();
                }

                return true;
            } else {
                return "You don't have permission to restore this course.";
            }
        } else {
            return "Only instructors can restore courses.";
        }
    } catch (PDOException $e) {
        return "Failed to restore course: " . $e->getMessage();
    }
}

/**
 * Function to get archived courses for a student
 *
 * @param int $userId The user ID
 * @return array|string Array of archived courses if successful, error message otherwise
 */
function getArchivedCoursesByStudent($userId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT c.course_id, c.title, c.description, c.created_by, c.is_active,
                   c.capacity, c.semester, c.created_at, c.is_archived as course_archived,
                   u.username as creator_name, e.enrollment_date, e.completion_status,
                   e.is_archived
            FROM enrollments e
            JOIN courses c ON e.course_id = c.course_id
            JOIN users u ON c.created_by = u.user_id
            WHERE e.user_id = :userId AND c.is_active = 1
            AND (e.is_archived = 1 OR c.is_archived = 1)
            ORDER BY e.enrollment_date DESC
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve archived courses: " . $e->getMessage();
    }
}

/**
 * Function to get archived courses for a teacher
 *
 * @param int $teacherId The teacher's user ID
 * @return array|string Array of archived courses if successful, error message otherwise
 */
function getArchivedCoursesByTeacher($teacherId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT c.course_id, c.title, c.description, c.is_active, c.created_by,
                   c.created_at, c.updated_at, u.username as creator_name,
                   CASE WHEN ci.is_archived = 1 OR c.is_archived = 1 THEN 1 ELSE 0 END as is_archived
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            LEFT JOIN course_instructors ci ON c.course_id = ci.course_id AND (ci.instructor_id = :instructorId)
            WHERE (c.created_by = :teacherId OR ci.instructor_id = :instructorId2)
            AND c.is_active = 1
            AND (c.is_archived = 1 OR ci.is_archived = 1)
            ORDER BY c.created_at DESC
        ");
        $stmt->bindParam(':teacherId', $teacherId);
        $stmt->bindParam(':instructorId', $teacherId);
        $stmt->bindParam(':instructorId2', $teacherId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve archived courses: " . $e->getMessage();
    }
}

/**
 * Function to update the completion status of a course for a student
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @param string $status The completion status ('completed', 'in_progress', etc.)
 * @return bool|string True if successful, error message otherwise
 */
function updateCourseCompletionStatus($userId, $courseId, $status) {
    global $pdo;

    try {
        // Update the enrollment completion status
        $stmt = $pdo->prepare("
            UPDATE enrollments
            SET completion_status = :status
            WHERE user_id = :userId AND course_id = :courseId
        ");
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update course completion status: " . $e->getMessage();
    }
}

/**
 * Function to get all courses taught by an instructor
 *
 * @param int $instructorId The instructor's user ID
 * @return array Array of courses
 */
function getInstructorCourses($instructorId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT c.course_id, c.title, c.class_code
            FROM courses c
            LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
            WHERE (c.created_by = :instructorId OR ci.instructor_id = :instructorId2)
            AND c.is_active = 1
            AND c.is_archived = 0
            ORDER BY c.title ASC
        ");
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->bindParam(':instructorId2', $instructorId);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting instructor courses: " . $e->getMessage());
        return [];
    }
}

/**
 * Function to mark a course as complete for all students and archive it
 *
 * @param int $courseId The course ID
 * @return bool|string True if successful, error message otherwise
 */
function markCourseCompleteForAllStudents($courseId) {
    global $pdo;

    try {
        // Mark all enrollments as completed
        $stmt = $pdo->prepare("
            UPDATE enrollments
            SET completion_status = 'completed', is_archived = 1
            WHERE course_id = :courseId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        // Mark the course as archived
        $stmt = $pdo->prepare("
            UPDATE courses
            SET is_archived = 1
            WHERE course_id = :courseId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to mark course as complete: " . $e->getMessage();
    }
}

/**
 * Function to get courses by teacher (created by teacher or assigned as instructor)
 *
 * @param int $teacherId The teacher's user ID
 * @param bool $activeOnly Whether to get only active courses
 * @param bool $includeArchived Whether to include archived courses
 * @return array|string Array of courses if successful, error message otherwise
 */
function getCoursesByTeacher($teacherId, $activeOnly = true, $includeArchived = false) {
    global $pdo;

    try {
        $query = "
            SELECT DISTINCT c.course_id, c.title, c.description, c.is_active, c.created_by,
                   c.created_at, c.updated_at, u.username as creator_name,
                   CASE WHEN ci.is_archived = 1 OR c.is_archived = 1 THEN 1 ELSE 0 END as is_archived,
                   c.is_archived as course_archived, c.capacity, c.semester
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            LEFT JOIN course_instructors ci ON c.course_id = ci.course_id AND (ci.instructor_id = :instructorId)
            WHERE c.created_by = :teacherId OR ci.instructor_id = :instructorId2
        ";

        if ($activeOnly) {
            $query .= " AND c.is_active = 1";
        }

        if (!$includeArchived) {
            $query .= " AND c.is_archived = 0";
            $query .= " AND (ci.is_archived = 0 OR ci.is_archived IS NULL)";
        }

        $query .= " ORDER BY c.created_at DESC";

        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':teacherId', $teacherId);
        $stmt->bindParam(':instructorId', $teacherId);
        $stmt->bindParam(':instructorId2', $teacherId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve courses: " . $e->getMessage();
    }
}

/**
 * Function to enroll a student in a course
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @return bool|string True if enrollment successful, error message otherwise
 */
function enrollStudent($userId, $courseId) {
    global $pdo;

    try {
        // Check if the course exists and is active
        $stmt = $pdo->prepare("SELECT course_id FROM courses WHERE course_id = :courseId AND is_active = 1");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Course not found or inactive.";
        }

        // Check if the user exists and is a student
        $stmt = $pdo->prepare("
            SELECT u.user_id FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :userId AND r.role_name = 'student' AND u.is_active = 1
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "User not found, inactive, or not a student.";
        }

        // Check if already enrolled
        $stmt = $pdo->prepare("SELECT enrollment_id FROM enrollments WHERE user_id = :userId AND course_id = :courseId");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "Student already enrolled in this course.";
        }

        // Enroll the student
        $stmt = $pdo->prepare("INSERT INTO enrollments (user_id, course_id) VALUES (:userId, :courseId)");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to enroll student: " . $e->getMessage();
    }
}

/**
 * Function to unenroll a student from a course
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @return bool|string True if unenrollment successful, error message otherwise
 */
function unenrollStudent($userId, $courseId) {
    global $pdo;

    try {
        // Check if the enrollment exists
        $stmt = $pdo->prepare("SELECT enrollment_id FROM enrollments WHERE user_id = :userId AND course_id = :courseId");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Student not enrolled in this course.";
        }

        // Unenroll the student
        $stmt = $pdo->prepare("DELETE FROM enrollments WHERE user_id = :userId AND course_id = :courseId");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to unenroll student: " . $e->getMessage();
    }
}

/**
 * Function to get enrolled students for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of enrolled students if successful, error message otherwise
 */
function getEnrolledStudents($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name,
                   e.enrollment_date, e.completion_status
            FROM enrollments e
            JOIN users u ON e.user_id = u.user_id
            WHERE e.course_id = :courseId
            ORDER BY u.last_name, u.first_name
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve enrolled students: " . $e->getMessage();
    }
}

/**
 * Function to get courses a student is enrolled in
 *
 * @param int $userId The user ID
 * @param bool $includeArchived Whether to include archived courses
 * @return array|string Array of enrolled courses if successful, error message otherwise
 */
function getEnrolledCourses($userId, $includeArchived = false) {
    global $pdo;

    try {
        $query = "
            SELECT c.course_id, c.title, c.description, c.created_by, c.is_active,
                   c.capacity, c.semester, c.created_at,
                   u.username as creator_name, e.enrollment_date, e.completion_status,
                   e.is_archived, c.is_archived as course_archived
            FROM enrollments e
            JOIN courses c ON e.course_id = c.course_id
            JOIN users u ON c.created_by = u.user_id
            WHERE e.user_id = :userId AND c.is_active = 1
        ";

        if (!$includeArchived) {
            $query .= " AND e.is_archived = 0";
            $query .= " AND c.is_archived = 0";
        }

        $query .= " ORDER BY e.enrollment_date DESC";

        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve enrolled courses: " . $e->getMessage();
    }
}

/**
 * Function to check if a user is enrolled in a course
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @return bool True if user is enrolled, false otherwise
 */
function isEnrolled($userId, $courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT enrollment_id
            FROM enrollments
            WHERE user_id = :userId AND course_id = :courseId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Function to create an enrollment request
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @return bool|string True if request created successfully, error message otherwise
 */
function createEnrollmentRequest($userId, $courseId) {
    global $pdo;

    try {
        // Check if the course exists and is active
        $stmt = $pdo->prepare("SELECT course_id FROM courses WHERE course_id = :courseId AND is_active = 1");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Course not found or inactive.";
        }

        // Check if the user exists and is a student
        $stmt = $pdo->prepare("
            SELECT u.user_id FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :userId AND r.role_name = 'student' AND u.is_active = 1
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "User not found, inactive, or not a student.";
        }

        // Check if already enrolled
        $stmt = $pdo->prepare("SELECT enrollment_id FROM enrollments WHERE user_id = :userId AND course_id = :courseId");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "Student already enrolled in this course.";
        }

        // Check if there's already a pending request
        $stmt = $pdo->prepare("
            SELECT request_id FROM enrollment_requests
            WHERE user_id = :userId AND course_id = :courseId AND status = 'pending'
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "You already have a pending enrollment request for this course.";
        }

        // Create the enrollment request
        $stmt = $pdo->prepare("
            INSERT INTO enrollment_requests (user_id, course_id)
            VALUES (:userId, :courseId)
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to create enrollment request in database '" . DB_NAME . "': " . $e->getMessage();
    }
}

/**
 * Function to get pending enrollment requests for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of pending requests if successful, error message otherwise
 */
function getPendingEnrollmentRequests($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT er.request_id, er.user_id, er.course_id, er.request_date,
                   u.username, u.first_name, u.last_name, u.email
            FROM enrollment_requests er
            JOIN users u ON er.user_id = u.user_id
            WHERE er.course_id = :courseId AND er.status = 'pending'
            ORDER BY er.request_date ASC
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve enrollment requests: " . $e->getMessage();
    }
}

/**
 * Function to count pending enrollment requests for a course
 *
 * @param int $courseId The course ID
 * @return int|string Number of pending requests if successful, error message otherwise
 */
function countPendingEnrollmentRequests($courseId) {
    global $pdo;

    try {
        // Check if the table exists first
        $stmt = $pdo->query("SHOW TABLES LIKE 'enrollment_requests'");
        if ($stmt->rowCount() == 0) {
            return 0; // Table doesn't exist yet
        }

        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM enrollment_requests
            WHERE course_id = :courseId AND status = 'pending'
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        $result = $stmt->fetch();
        return $result['count'];
    } catch (PDOException $e) {
        // Just return 0 instead of an error message
        return 0;
    }
}

/**
 * Function to process an enrollment request
 *
 * @param int $requestId The request ID
 * @param string $status The new status ('approved' or 'rejected')
 * @param int $processedBy The user ID of the processor
 * @return bool|string True if processing successful, error message otherwise
 */
function processEnrollmentRequest($requestId, $status, $processedBy) {
    global $pdo;

    try {
        // Check if the request exists and is pending
        $stmt = $pdo->prepare("
            SELECT er.*, c.created_by
            FROM enrollment_requests er
            JOIN courses c ON er.course_id = c.course_id
            WHERE er.request_id = :requestId AND er.status = 'pending'
        ");
        $stmt->bindParam(':requestId', $requestId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Request not found or already processed.";
        }

        $request = $stmt->fetch();

        // Check if user is authorized to process this request
        if (!isAdmin() && $request['created_by'] != $processedBy && !isInstructor($processedBy, $request['course_id'])) {
            return "You are not authorized to process this request.";
        }

        // Update the request status
        $stmt = $pdo->prepare("
            UPDATE enrollment_requests
            SET status = :status, processed_by = :processedBy, processed_date = NOW()
            WHERE request_id = :requestId
        ");
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':processedBy', $processedBy);
        $stmt->bindParam(':requestId', $requestId);
        $stmt->execute();

        // If approved, enroll the student
        if ($status === 'approved') {
            $result = enrollStudent($request['user_id'], $request['course_id']);
            if ($result !== true) {
                return $result;
            }

            // Get course details for notification
            $courseStmt = $pdo->prepare("SELECT title FROM courses WHERE course_id = :courseId");
            $courseStmt->bindParam(':courseId', $request['course_id']);
            $courseStmt->execute();
            $courseTitle = $courseStmt->fetchColumn();

            // Get processor name (instructor who approved)
            $processorStmt = $pdo->prepare("SELECT username FROM users WHERE user_id = :userId");
            $processorStmt->bindParam(':userId', $processedBy);
            $processorStmt->execute();
            $processorName = $processorStmt->fetchColumn();

            // Send notification to student
            if (function_exists('createNotification')) {
                $notificationTitle = "Enrollment Request Approved";
                $notificationMessage = "Your request to enroll in the course '$courseTitle' has been approved by $processorName. You are now enrolled in this course.";
                createNotification($request['user_id'], $notificationTitle, $notificationMessage, 'enrollment_approved', $request['course_id']);
            }
        } else if ($status === 'rejected') {
            // Get course details for notification
            $courseStmt = $pdo->prepare("SELECT title FROM courses WHERE course_id = :courseId");
            $courseStmt->bindParam(':courseId', $request['course_id']);
            $courseStmt->execute();
            $courseTitle = $courseStmt->fetchColumn();

            // Get processor name (instructor who rejected)
            $processorStmt = $pdo->prepare("SELECT username FROM users WHERE user_id = :userId");
            $processorStmt->bindParam(':userId', $processedBy);
            $processorStmt->execute();
            $processorName = $processorStmt->fetchColumn();

            // Send notification to student
            if (function_exists('createNotification')) {
                $notificationTitle = "Enrollment Request Rejected";
                $notificationMessage = "Your request to enroll in the course '$courseTitle' has been rejected by $processorName. Please contact the instructor for more information.";
                createNotification($request['user_id'], $notificationTitle, $notificationMessage, 'enrollment_rejected', $request['course_id']);
            }
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to process enrollment request: " . $e->getMessage();
    }
}

/**
 * Function to create an enrollment request using a class code
 *
 * @param int $userId The user ID
 * @param string $classCode The class code
 * @return bool|string True if request created successfully, error message otherwise
 */
function enrollStudentByClassCode($userId, $classCode) {
    global $pdo;

    try {
        // Check if the class code exists and course is active
        $stmt = $pdo->prepare("SELECT course_id FROM courses WHERE class_code = :classCode AND is_active = 1");
        $stmt->bindParam(':classCode', $classCode);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Invalid class code or course is inactive.";
        }

        $course = $stmt->fetch();
        $courseId = $course['course_id'];

        // Check if the user exists and is a student
        $stmt = $pdo->prepare("
            SELECT u.user_id FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :userId AND r.role_name = 'student' AND u.is_active = 1
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "User not found, inactive, or not a student.";
        }

        // Check if already enrolled
        $stmt = $pdo->prepare("SELECT enrollment_id FROM enrollments WHERE user_id = :userId AND course_id = :courseId");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "You are already enrolled in this course.";
        }

        // Check if there's already a pending request
        $stmt = $pdo->prepare("
            SELECT request_id FROM enrollment_requests
            WHERE user_id = :userId AND course_id = :courseId AND status = 'pending'
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "You already have a pending enrollment request for this course.";
        }

        // Create the enrollment request
        $stmt = $pdo->prepare("
            INSERT INTO enrollment_requests (user_id, course_id)
            VALUES (:userId, :courseId)
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        // Get course details for notifications
        $courseStmt = $pdo->prepare("
            SELECT c.title, c.created_by, u.username as creator_name
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            WHERE c.course_id = :courseId
        ");
        $courseStmt->bindParam(':courseId', $courseId);
        $courseStmt->execute();
        $course = $courseStmt->fetch();

        // Get student name
        $studentStmt = $pdo->prepare("SELECT username FROM users WHERE user_id = :userId");
        $studentStmt->bindParam(':userId', $userId);
        $studentStmt->execute();
        $studentName = $studentStmt->fetchColumn();

        // Notify course creator (instructor)
        if (function_exists('createNotification')) {
            $notificationTitle = "New Enrollment Request";
            $notificationMessage = "$studentName has requested to enroll in your course '$course[title]'. Please review and approve or reject this request.";
            createNotification($course['created_by'], $notificationTitle, $notificationMessage, 'enrollment_request', $courseId);

            // Also notify other instructors assigned to this course
            $instructorsStmt = $pdo->prepare("
                SELECT instructor_id
                FROM course_instructors
                WHERE course_id = :courseId AND instructor_id != :creatorId
            ");
            $instructorsStmt->bindParam(':courseId', $courseId);
            $instructorsStmt->bindParam(':creatorId', $course['created_by']);
            $instructorsStmt->execute();

            while ($instructor = $instructorsStmt->fetch()) {
                createNotification($instructor['instructor_id'], $notificationTitle, $notificationMessage, 'enrollment_request', $courseId);
            }
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to create enrollment request in database '" . DB_NAME . "': " . $e->getMessage();
    }
}

/**
 * Function to get a course by class code
 *
 * @param string $classCode The class code
 * @return array|string Course data if successful, error message otherwise
 */
function getCourseByClassCode($classCode) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT c.course_id, c.title, c.description, c.class_code, c.is_active,
                   c.created_by, c.created_at, c.updated_at,
                   u.username as creator_name
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            WHERE c.class_code = :classCode
        ");
        $stmt->bindParam(':classCode', $classCode);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Course not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve course: " . $e->getMessage();
    }
}

/**
 * Function to get a course ID by class code
 *
 * @param string $classCode The class code
 * @return int|string Course ID if successful, error message otherwise
 */
function getCourseIdByClassCode($classCode) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT course_id
            FROM courses
            WHERE class_code = :classCode AND is_active = 1
        ");
        $stmt->bindParam(':classCode', $classCode);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $course = $stmt->fetch();
            return $course['course_id'];
        } else {
            return "Invalid class code or course is inactive.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve course: " . $e->getMessage();
    }
}

/**
 * Function to assign an instructor to a course
 * Admin or course creator can assign instructors
 *
 * @param int $courseId The course ID
 * @param int $instructorId The instructor's user ID
 * @return bool|string True if assignment successful, error message otherwise
 */
function assignInstructor($courseId, $instructorId) {
    global $pdo;

    // Check if user is authorized to assign instructors
    if (!isAdmin()) {
        // Check if the user is the course creator
        $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $course = $stmt->fetch();
            if ($course['created_by'] != $_SESSION['user_id']) {
                return "Only administrators and course creators can assign instructors to courses.";
            }
        } else {
            return "Course not found.";
        }
    }

    try {
        // Check if the course exists
        $stmt = $pdo->prepare("SELECT course_id FROM courses WHERE course_id = :courseId");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Course not found.";
        }

        // Check if the user exists and is a teacher
        $stmt = $pdo->prepare("
            SELECT u.user_id FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :instructorId AND r.role_name = 'teacher' AND u.is_active = 1
        ");
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "User not found, inactive, or not a teacher.";
        }

        // Check if already assigned
        $stmt = $pdo->prepare("
            SELECT * FROM course_instructors
            WHERE course_id = :courseId AND instructor_id = :instructorId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "Instructor already assigned to this course.";
        }

        // Assign the instructor
        $stmt = $pdo->prepare("
            INSERT INTO course_instructors (course_id, instructor_id)
            VALUES (:courseId, :instructorId)
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to assign instructor: " . $e->getMessage();
    }
}

/**
 * Function to remove an instructor from a course
 * Admin or course creator can remove instructors
 *
 * @param int $courseId The course ID
 * @param int $instructorId The instructor's user ID
 * @return bool|string True if removal successful, error message otherwise
 */
function removeInstructor($courseId, $instructorId) {
    global $pdo;

    // Check if user is authorized to remove instructors
    if (!isAdmin()) {
        // Check if the user is the course creator
        $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $course = $stmt->fetch();
            if ($course['created_by'] != $_SESSION['user_id']) {
                return "Only administrators and course creators can remove instructors from courses.";
            }
        } else {
            return "Course not found.";
        }
    }

    try {
        // Check if the assignment exists
        $stmt = $pdo->prepare("
            SELECT * FROM course_instructors
            WHERE course_id = :courseId AND instructor_id = :instructorId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Instructor not assigned to this course.";
        }

        // Remove the instructor
        $stmt = $pdo->prepare("
            DELETE FROM course_instructors
            WHERE course_id = :courseId AND instructor_id = :instructorId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':instructorId', $instructorId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to remove instructor: " . $e->getMessage();
    }
}

/**
 * Function to get all instructors for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of instructors if successful, error message otherwise
 */
function getCourseInstructors($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name
            FROM course_instructors ci
            JOIN users u ON ci.instructor_id = u.user_id
            WHERE ci.course_id = :courseId
            ORDER BY u.last_name, u.first_name
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve course instructors: " . $e->getMessage();
    }
}

/**
 * Function to check if a user is an instructor for a course
 *
 * @param int $userId The user ID
 * @param int $courseId The course ID
 * @return bool True if user is an instructor, false otherwise
 */
function isInstructor($userId, $courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT * FROM course_instructors
            WHERE course_id = :courseId AND instructor_id = :userId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Function to get the primary instructor for a course
 * Returns the course creator or the first assigned instructor
 *
 * @param int $courseId The course ID
 * @return array|string Instructor data if successful, error message otherwise
 */
function getCourseInstructor($courseId) {
    global $pdo;

    try {
        // First try to get the course creator
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.email
            FROM courses c
            JOIN users u ON c.created_by = u.user_id
            WHERE c.course_id = :courseId
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch();
        }

        // If no creator found, try to get the first assigned instructor
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.email
            FROM course_instructors ci
            JOIN users u ON ci.instructor_id = u.user_id
            WHERE ci.course_id = :courseId
            ORDER BY ci.assigned_date ASC
            LIMIT 1
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch();
        }

        return "No instructor found for this course.";
    } catch (PDOException $e) {
        return "Failed to retrieve course instructor: " . $e->getMessage();
    }
}

// This function was removed to avoid duplication with the archiveCourse function at line 279

// This function was removed to avoid duplication with the restoreCourse function at line 343

// These functions were removed to avoid duplication with the getArchivedCoursesByStudent and getArchivedCoursesByTeacher functions at lines 406 and 437
