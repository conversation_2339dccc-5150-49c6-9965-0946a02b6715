<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to view this page.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to view this page
if (!isAdmin() && $course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You are not authorized to view enrollment requests for this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Process request approval/rejection
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && isset($_POST['request_id'])) {
    $requestId = intval($_POST['request_id']);
    $action = $_POST['action'];
    
    if ($action === 'approve') {
        $result = processEnrollmentRequest($requestId, 'approved', $_SESSION['user_id']);
        if ($result === true) {
            $_SESSION['success'] = "Enrollment request approved successfully.";
        } else {
            $_SESSION['error'] = $result;
        }
    } elseif ($action === 'reject') {
        $result = processEnrollmentRequest($requestId, 'rejected', $_SESSION['user_id']);
        if ($result === true) {
            $_SESSION['success'] = "Enrollment request rejected successfully.";
        } else {
            $_SESSION['error'] = $result;
        }
    }
    
    // Redirect to refresh the page
    header("location: course_enrollment_requests.php?id=$courseId");
    exit;
}

// Get pending enrollment requests
$requests = getPendingEnrollmentRequests($courseId);

// Set page title
$page_title = "Enrollment Requests - " . $course['title'];

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Enrollment Requests</h1>
    <div>
        <a href="course_view_full.php?id=<?php echo $courseId; ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Course
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
    </div>
    <div class="card-body">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?php 
                echo $_SESSION['success']; 
                unset($_SESSION['success']);
                ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?php 
                echo $_SESSION['error']; 
                unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>
        
        <?php if (is_array($requests) && count($requests) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Student</th>
                            <th>Email</th>
                            <th>Request Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requests as $request): ?>
                            <tr>
                                <td>
                                    <?php 
                                    echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); 
                                    echo ' (' . htmlspecialchars($request['username']) . ')';
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($request['email']); ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($request['request_date'])); ?></td>
                                <td>
                                    <form method="post" class="d-inline" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $courseId); ?>">
                                        <input type="hidden" name="request_id" value="<?php echo $request['request_id']; ?>">
                                        <input type="hidden" name="action" value="approve">
                                        <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Are you sure you want to approve this enrollment request?');">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                    <form method="post" class="d-inline" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $courseId); ?>">
                                        <input type="hidden" name="request_id" value="<?php echo $request['request_id']; ?>">
                                        <input type="hidden" name="action" value="reject">
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to reject this enrollment request?');">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h3>No pending enrollment requests</h3>
                <p class="empty-state-text">There are no pending enrollment requests for this course.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
