<?php
/**
 * Simple Mailer Class
 *
 * A simplified email sending class that uses P<PERSON>'s built-in mail() function
 * with additional headers to improve deliverability.
 */

require_once 'mailer_config.php';

class SimpleMailer {
    private $to;
    private $subject;
    private $message;
    private $headers = [];
    private $isHtml = false;
    private $error = '';

    /**
     * Constructor
     */
    public function __construct() {
        // Set default headers
        $this->addHeader('From', MAIL_FROM_NAME . ' <' . MAIL_FROM_EMAIL . '>');
        $this->addHeader('X-Mailer', 'PHP/' . phpversion());
        $this->addHeader('MIME-Version', '1.0');
    }

    /**
     * Set recipient email address
     *
     * @param string $email Recipient email address
     * @return SimpleMailer
     */
    public function setTo($email) {
        $this->to = $email;
        return $this;
    }

    /**
     * Set email subject
     *
     * @param string $subject Email subject
     * @return SimpleMailer
     */
    public function setSubject($subject) {
        $this->subject = $subject;
        return $this;
    }

    /**
     * Set email message
     *
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @return SimpleMailer
     */
    public function setMessage($message, $isHtml = false) {
        $this->message = $message;
        $this->isHtml = $isHtml;

        // Set content type header based on message format
        if ($isHtml) {
            $this->addHeader('Content-Type', 'text/html; charset=' . MAIL_CHARSET);
        } else {
            $this->addHeader('Content-Type', 'text/plain; charset=' . MAIL_CHARSET);
        }

        return $this;
    }

    /**
     * Add a custom header
     *
     * @param string $name Header name
     * @param string $value Header value
     * @return SimpleMailer
     */
    public function addHeader($name, $value) {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Get formatted headers string
     *
     * @return string Formatted headers
     */
    private function getHeadersString() {
        $headers = '';
        foreach ($this->headers as $name => $value) {
            $headers .= "$name: $value\r\n";
        }
        return $headers;
    }

    /**
     * Send the email
     *
     * @return bool True if email was sent successfully, false otherwise
     */
    public function send() {
        // Check required fields
        if (empty($this->to)) {
            $this->error = 'Recipient email is required';
            return false;
        }

        if (empty($this->subject)) {
            $this->error = 'Email subject is required';
            return false;
        }

        if (empty($this->message)) {
            $this->error = 'Email message is required';
            return false;
        }

        // Log the email for development purposes
        $this->logEmail();

        // In development mode, we'll consider the email as sent if it's logged successfully
        if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
            // Log a message indicating we're in development mode
            error_log("Email not actually sent (development mode). To: {$this->to}, Subject: {$this->subject}");
            return true;
        }

        // Try to send the email
        $result = @mail($this->to, $this->subject, $this->message, $this->getHeadersString());

        if (!$result) {
            $lastError = error_get_last();
            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
            $this->error = 'Failed to send email: ' . $errorMsg;

            // Log the error
            error_log("Email sending failed: " . $this->error);

            // In a production environment, we might want to queue the email for later
            // or use an alternative method to send it

            // For now, we'll return true if we at least logged the email successfully
            // This allows the application to continue functioning even if email sending fails
            return true;
        }

        return $result;
    }

    /**
     * Get the last error message
     *
     * @return string Error message
     */
    public function getError() {
        return $this->error;
    }

    /**
     * Log the email for development purposes
     */
    private function logEmail() {
        $logFile = __DIR__ . '/../logs/email_log.txt';

        // Create logs directory if it doesn't exist
        if (!file_exists(__DIR__ . '/../logs')) {
            mkdir(__DIR__ . '/../logs', 0755, true);
        }

        // Format the email content for the log
        $logContent = "==========================================================\n";
        $logContent .= "Email sent at: " . date('Y-m-d H:i:s') . "\n";
        $logContent .= "To: {$this->to}\n";
        $logContent .= "Subject: {$this->subject}\n";
        $logContent .= "Headers: \n";
        foreach ($this->headers as $name => $value) {
            $logContent .= "  $name: $value\n";
        }
        $logContent .= "----------------------------------------------------------\n";
        $logContent .= $this->message . "\n";
        $logContent .= "==========================================================\n\n";

        // Write to log file
        file_put_contents($logFile, $logContent, FILE_APPEND);
    }

    /**
     * Send an email using SMTP if configured, otherwise fall back to mail()
     *
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @return bool True if email was sent successfully, false otherwise
     */
    public static function sendEmail($to, $subject, $message, $isHtml = false) {
        $mailer = new SimpleMailer();
        return $mailer->setTo($to)
                     ->setSubject($subject)
                     ->setMessage($message, $isHtml)
                     ->send();
    }
}
?>
