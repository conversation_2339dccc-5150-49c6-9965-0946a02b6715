<?php
// Set page title
$page_title = "Test Bootstrap Tabs";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Test Bootstrap Tabs</h1>
            
            <div class="alert alert-info">
                <p>This page tests if Bootstrap tabs are working correctly in your browser.</p>
            </div>

            <ul class="nav nav-tabs mb-4" id="testTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="tab1-tab" data-toggle="tab" href="#tab1" role="tab" aria-controls="tab1" aria-selected="true">Tab 1</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tab2-tab" data-toggle="tab" href="#tab2" role="tab" aria-controls="tab2" aria-selected="false">Tab 2</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tab3-tab" data-toggle="tab" href="#tab3" role="tab" aria-controls="tab3" aria-selected="false">Tab 3</a>
                </li>
            </ul>

            <div class="tab-content" id="testTabsContent">
                <div class="tab-pane fade show active" id="tab1" role="tabpanel" aria-labelledby="tab1-tab">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Tab 1 Content</h5>
                            <p class="card-text">This is the content of Tab 1.</p>
                            <button class="btn btn-primary" onclick="showTab2()">Go to Tab 2</button>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="tab2" role="tabpanel" aria-labelledby="tab2-tab">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Tab 2 Content</h5>
                            <p class="card-text">This is the content of Tab 2.</p>
                            <button class="btn btn-primary" onclick="showTab3()">Go to Tab 3</button>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="tab3" role="tabpanel" aria-labelledby="tab3-tab">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Tab 3 Content</h5>
                            <p class="card-text">This is the content of Tab 3.</p>
                            <button class="btn btn-primary" onclick="showTab1()">Go to Tab 1</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <h3>Debug Information</h3>
                <div id="debug" class="alert alert-secondary">
                    <p>Click on tabs to see debug information here.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Function to show a specific tab
function showTab(tabId) {
    $('#testTabs a[href="#' + tabId + '"]').tab('show');
    logAction('Programmatically switched to ' + tabId);
}

function showTab1() {
    showTab('tab1');
}

function showTab2() {
    showTab('tab2');
}

function showTab3() {
    showTab('tab3');
}

// Function to log actions to the debug div
function logAction(message) {
    var now = new Date();
    var timestamp = now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds();
    $('#debug').append('<p>' + timestamp + ' - ' + message + '</p>');
}

// Initialize on page load
$(document).ready(function() {
    logAction('Page loaded');
    
    // Log when tabs are clicked
    $('#testTabs a').on('click', function (e) {
        e.preventDefault();
        logAction('Tab clicked: ' + $(this).attr('href'));
        $(this).tab('show');
    });

    // Log when tabs are shown
    $('#testTabs a').on('shown.bs.tab', function (e) {
        logAction('Tab shown: ' + $(this).attr('href'));
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
