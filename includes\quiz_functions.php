<?php
/**
 * Quiz Functions
 *
 * This file contains functions related to quizzes and quiz questions.
 */

require_once 'config.php';
require_once 'content_functions.php';

/**
 * Function to create a new quiz (DEPRECATED - Use createQuiz in activity_functions.php instead)
 * This function is kept for backward compatibility but should not be used in new code.
 *
 * @param int $courseId The course ID
 * @param string $title The quiz title
 * @param string $description The quiz description
 * @param int $points The maximum points
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS) or null if no due date
 * @param int $timeLimit The time limit in minutes
 * @param int $moduleId The module ID (optional)
 * @param bool $allowLateSubmissions Whether to allow late submissions
 * @param bool $isPublished Whether the quiz is published
 * @param int $createdBy The user ID of the creator
 * @param array $files Optional array of uploaded files
 * @return int|string Quiz ID if creation successful, error message otherwise
 */
function createQuizOld($courseId, $title, $description, $points, $dueDate = null, $timeLimit = 60, $moduleId = null, $allowLateSubmissions = false, $isPublished = true, $createdBy, $files = null) {
    global $pdo;

    try {
        // Check if user is authorized to create quizzes for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->bindParam(':userId', $createdBy);
            $stmt->bindParam(':instructorId', $createdBy);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to create quizzes for this course.";
            }
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create the activity with type 'quiz'
        $stmt = $pdo->prepare("
            INSERT INTO activities (course_id, title, description, activity_type, points, due_date, allow_late_submissions, is_published, created_by)
            VALUES (:courseId, :title, :description, 'quiz', :points, :dueDate, :allowLateSubmissions, :isPublished, :createdBy)
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':dueDate', $dueDate);
        $allowLateSubmissionsInt = $allowLateSubmissions ? 1 : 0;
        $stmt->bindParam(':allowLateSubmissions', $allowLateSubmissionsInt);
        $isPublishedInt = $isPublished ? 1 : 0;
        $stmt->bindParam(':isPublished', $isPublishedInt);
        $stmt->bindParam(':createdBy', $createdBy);

        $stmt->execute();

        $activityId = $pdo->lastInsertId();

        // Store quiz settings
        $stmt = $pdo->prepare("
            INSERT INTO quiz_settings (activity_id, time_limit)
            VALUES (:activityId, :timeLimit)
        ");

        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':timeLimit', $timeLimit);

        $stmt->execute();

        // If module is selected, associate the activity with the module
        if ($moduleId) {
            $stmt = $pdo->prepare("
                INSERT INTO module_activities (module_id, activity_id)
                VALUES (:moduleId, :activityId)
            ");

            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->bindParam(':activityId', $activityId);

            $stmt->execute();
        }

        // Handle file uploads if any
        if ($files && !empty($files['name'][0])) {
            // Create uploads directory if it doesn't exist
            $uploadDir = '../uploads/quizzes/' . $activityId . '/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Process each uploaded file
            $fileCount = count($files['name']);
            for ($i = 0; $i < $fileCount; $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $fileName = basename($files['name'][$i]);
                    $fileType = $files['type'][$i];
                    $fileSize = $files['size'][$i];
                    $fileTmpName = $files['tmp_name'][$i];

                    // Generate a unique filename to prevent overwriting
                    $uniqueFileName = uniqid() . '_' . $fileName;
                    $targetFilePath = $uploadDir . $uniqueFileName;

                    // Move the uploaded file to the target directory
                    if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                        // Save file information to the database
                        $stmt = $pdo->prepare("
                            INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size)
                            VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize)
                        ");

                        $relativePath = 'uploads/quizzes/' . $activityId . '/' . $uniqueFileName;

                        $stmt->bindParam(':activityId', $activityId);
                        $stmt->bindParam(':fileName', $fileName);
                        $stmt->bindParam(':filePath', $relativePath);
                        $stmt->bindParam(':fileType', $fileType);
                        $stmt->bindParam(':fileSize', $fileSize);

                        $stmt->execute();
                    } else {
                        // Rollback transaction if file upload fails
                        $pdo->rollBack();
                        return "Failed to upload file: " . $fileName;
                    }
                } elseif ($files['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                    // Rollback transaction if there's an error with the file
                    $pdo->rollBack();
                    return "Error uploading file: " . getQuizFileUploadError($files['error'][$i]);
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        return $activityId;
    } catch (PDOException $e) {
        // Rollback transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to create quiz: " . $e->getMessage();
    }
}

/**
 * Function to update a quiz
 * Admin can update any quiz, teachers can only update quizzes in their own courses
 *
 * @param int $quizId The quiz ID
 * @param string $title The quiz title
 * @param string $description The quiz description
 * @param int $timeLimit The time limit in minutes (optional)
 * @param float $passingScore The passing score
 * @return bool|string True if update successful, error message otherwise
 */
function updateQuiz($quizId, $title, $description, $timeLimit = null, $passingScore = 70.00) {
    global $pdo;

    try {
        // Check if user is authorized to update this quiz
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quizzes q
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE q.quiz_id = :quizId
            ");
            $stmt->bindParam(':quizId', $quizId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $quiz = $stmt->fetch();

                if (!isTeacher() || $quiz['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this quiz.";
                }
            } else {
                return "Quiz not found.";
            }
        }

        // Update the quiz
        $stmt = $pdo->prepare("
            UPDATE quizzes
            SET title = :title, description = :description, time_limit = :timeLimit, passing_score = :passingScore
            WHERE quiz_id = :quizId
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':timeLimit', $timeLimit);
        $stmt->bindParam(':passingScore', $passingScore);
        $stmt->bindParam(':quizId', $quizId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update quiz: " . $e->getMessage();
    }
}

/**
 * Function to delete a quiz
 * Admin can delete any quiz, teachers can only delete quizzes in their own courses
 *
 * @param int $quizId The quiz ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteQuiz($quizId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this quiz
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quizzes q
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE q.quiz_id = :quizId
            ");
            $stmt->bindParam(':quizId', $quizId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $quiz = $stmt->fetch();

                if (!isTeacher() || $quiz['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this quiz.";
                }
            } else {
                return "Quiz not found.";
            }
        }

        // Delete the quiz
        $stmt = $pdo->prepare("DELETE FROM quizzes WHERE quiz_id = :quizId");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Quiz not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete quiz: " . $e->getMessage();
    }
}

/**
 * Function to get all quizzes for a lesson
 *
 * @param int $lessonId The lesson ID
 * @return array|string Array of quizzes if successful, error message otherwise
 */
function getQuizzesByLesson($lessonId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT quiz_id, title, description, time_limit, passing_score, created_at, updated_at
            FROM quizzes
            WHERE lesson_id = :lessonId
            ORDER BY created_at
        ");
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve quizzes: " . $e->getMessage();
    }
}

/**
 * Function to get a quiz by ID
 *
 * @param int $quizId The quiz ID
 * @return array|string Quiz data if successful, error message otherwise
 */
function getQuizById($quizId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT quiz_id, lesson_id, title, description, time_limit, passing_score, created_at, updated_at
            FROM quizzes
            WHERE quiz_id = :quizId
        ");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Quiz not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve quiz: " . $e->getMessage();
    }
}

/**
 * Function to add a question to a quiz
 * Admin can add questions to any quiz, teachers can only add questions to quizzes in their own courses
 *
 * @param int $activityId The activity ID
 * @param string $questionText The question text
 * @param string $questionType The question type (multiple_choice, true_false, short_answer)
 * @param int $points The points for this question
 * @param int $position The position of the question (optional)
 * @return bool|string Question ID if creation successful, error message otherwise
 */
function addQuizQuestion($activityId, $questionText, $questionType, $points = 1, $position = 0) {
    global $pdo;

    try {
        // Check if the activity exists and is a quiz
        $stmt = $pdo->prepare("
            SELECT a.activity_id, a.activity_type, a.created_by, a.course_id
            FROM activities a
            WHERE a.activity_id = :activityId AND a.activity_type = 'quiz'
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found or not a quiz.";
        }

        $activity = $stmt->fetch();

        // Check if user is authorized to add questions to this quiz
        if (!isAdmin() && $activity['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM course_instructors ci
                WHERE ci.course_id = :courseId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':courseId', $activity['course_id']);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to add questions to this quiz.";
            }
        }

        // Add the question
        $stmt = $pdo->prepare("
            INSERT INTO quiz_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, :questionText, :questionType, :points, :position)
        ");

        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':questionText', $questionText);
        $stmt->bindParam(':questionType', $questionType);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':position', $position);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add question: " . $e->getMessage();
    }
}

/**
 * Function to update a quiz question
 * Admin can update any question, teachers can only update questions in their own quizzes
 *
 * @param int $questionId The question ID
 * @param string $questionText The question text
 * @param string $questionType The question type (multiple_choice, true_false, short_answer)
 * @param int $points The points for this question
 * @return bool|string True if update successful, error message otherwise
 */
function updateQuizQuestion($questionId, $questionText, $questionType, $points = 1) {
    global $pdo;

    try {
        // Check if user is authorized to update this question
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quiz_questions qq
                JOIN quizzes q ON qq.quiz_id = q.quiz_id
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE qq.question_id = :questionId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $question = $stmt->fetch();

                if (!isTeacher() || $question['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this question.";
                }
            } else {
                return "Question not found.";
            }
        }

        // Update the question
        $stmt = $pdo->prepare("
            UPDATE quiz_questions
            SET question_text = :questionText, question_type = :questionType, points = :points
            WHERE question_id = :questionId
        ");

        $stmt->bindParam(':questionText', $questionText);
        $stmt->bindParam(':questionType', $questionType);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':questionId', $questionId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update question: " . $e->getMessage();
    }
}

/**
 * Function to delete a quiz question
 * Admin can delete any question, teachers can only delete questions in their own quizzes
 *
 * @param int $questionId The question ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteQuizQuestion($questionId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this question
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quiz_questions qq
                JOIN quizzes q ON qq.quiz_id = q.quiz_id
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE qq.question_id = :questionId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $question = $stmt->fetch();

                if (!isTeacher() || $question['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this question.";
                }
            } else {
                return "Question not found.";
            }
        }

        // Delete the question
        $stmt = $pdo->prepare("DELETE FROM quiz_questions WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Question not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete question: " . $e->getMessage();
    }
}

/**
 * Function to add an option to a quiz question (DEPRECATED - Use addQuizOption from activity_functions.php instead)
 * Admin can add options to any question, teachers can only add options to questions in their own quizzes
 *
 * @param int $questionId The question ID
 * @param string $optionText The option text
 * @param bool $isCorrect Whether this option is correct
 * @return bool|string Option ID if creation successful, error message otherwise
 */
function addQuizOptionOld($questionId, $optionText, $isCorrect = false) {
    global $pdo;

    try {
        // Check if user is authorized to add options to this question
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quiz_questions qq
                JOIN quizzes q ON qq.quiz_id = q.quiz_id
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE qq.question_id = :questionId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $question = $stmt->fetch();

                if (!isTeacher() || $question['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to add options to this question.";
                }
            } else {
                return "Question not found.";
            }
        }

        // Add the option
        $stmt = $pdo->prepare("
            INSERT INTO quiz_options (question_id, option_text, is_correct)
            VALUES (:questionId, :optionText, :isCorrect)
        ");

        $stmt->bindParam(':questionId', $questionId);
        $stmt->bindParam(':optionText', $optionText);
        $stmt->bindParam(':isCorrect', $isCorrect, PDO::PARAM_BOOL);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add option: " . $e->getMessage();
    }
}

/**
 * Function to update a quiz option
 * Admin can update any option, teachers can only update options in their own quizzes
 *
 * @param int $optionId The option ID
 * @param string $optionText The option text
 * @param bool $isCorrect Whether this option is correct
 * @return bool|string True if update successful, error message otherwise
 */
function updateQuizOption($optionId, $optionText, $isCorrect = false) {
    global $pdo;

    try {
        // Check if user is authorized to update this option
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quiz_options qo
                JOIN quiz_questions qq ON qo.question_id = qq.question_id
                JOIN quizzes q ON qq.quiz_id = q.quiz_id
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE qo.option_id = :optionId
            ");
            $stmt->bindParam(':optionId', $optionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $option = $stmt->fetch();

                if (!isTeacher() || $option['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this option.";
                }
            } else {
                return "Option not found.";
            }
        }

        // Update the option
        $stmt = $pdo->prepare("
            UPDATE quiz_options
            SET option_text = :optionText, is_correct = :isCorrect
            WHERE option_id = :optionId
        ");

        $stmt->bindParam(':optionText', $optionText);
        $stmt->bindParam(':isCorrect', $isCorrect, PDO::PARAM_BOOL);
        $stmt->bindParam(':optionId', $optionId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update option: " . $e->getMessage();
    }
}

/**
 * Function to delete a quiz option
 * Admin can delete any option, teachers can only delete options in their own quizzes
 *
 * @param int $optionId The option ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteQuizOption($optionId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this option
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM quiz_options qo
                JOIN quiz_questions qq ON qo.question_id = qq.question_id
                JOIN quizzes q ON qq.quiz_id = q.quiz_id
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE qo.option_id = :optionId
            ");
            $stmt->bindParam(':optionId', $optionId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $option = $stmt->fetch();

                if (!isTeacher() || $option['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this option.";
                }
            } else {
                return "Option not found.";
            }
        }

        // Delete the option
        $stmt = $pdo->prepare("DELETE FROM quiz_options WHERE option_id = :optionId");
        $stmt->bindParam(':optionId', $optionId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Option not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete option: " . $e->getMessage();
    }
}

/**
 * Helper function to get file upload error message for quizzes
 *
 * @param int $errorCode The error code from $_FILES['error']
 * @return string Error message
 */
function getQuizFileUploadError($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return "The uploaded file exceeds the upload_max_filesize directive in php.ini";
        case UPLOAD_ERR_FORM_SIZE:
            return "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form";
        case UPLOAD_ERR_PARTIAL:
            return "The uploaded file was only partially uploaded";
        case UPLOAD_ERR_NO_FILE:
            return "No file was uploaded";
        case UPLOAD_ERR_NO_TMP_DIR:
            return "Missing a temporary folder";
        case UPLOAD_ERR_CANT_WRITE:
            return "Failed to write file to disk";
        case UPLOAD_ERR_EXTENSION:
            return "File upload stopped by extension";
        default:
            return "Unknown upload error";
    }
}

/**
 * Function to get all options for a question
 *
 * @param int $questionId The question ID
 * @return array|string Array of options if successful, error message otherwise
 */
function getQuizOptions($questionId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT option_id, option_text, is_correct
            FROM quiz_options
            WHERE question_id = :questionId
            ORDER BY position, option_id
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve options: " . $e->getMessage();
    }
}

/**
 * Function to add an option to a question (DEPRECATED - Use the version above)
 *
 * @param int $questionId The question ID
 * @param string $optionText The option text
 * @param bool $isCorrect Whether this option is correct
 * @param int $position The position of the option (optional)
 * @return int|string Option ID if creation successful, error message otherwise
 */
function addQuizOptionWithPosition($questionId, $optionText, $isCorrect = false, $position = 0) {
    global $pdo;

    try {
        // Check if question exists
        $stmt = $pdo->prepare("SELECT question_id FROM quiz_questions WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        // Add the option
        $stmt = $pdo->prepare("
            INSERT INTO quiz_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, :optionText, :isCorrect, :position)
        ");

        $stmt->bindParam(':questionId', $questionId);
        $stmt->bindParam(':optionText', $optionText);
        $isCorrectInt = $isCorrect ? 1 : 0;
        $stmt->bindParam(':isCorrect', $isCorrectInt);
        $stmt->bindParam(':position', $position);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add option: " . $e->getMessage();
    }
}

// This function has been moved to activity_functions.php to avoid duplication

// This function has been moved to activity_functions.php to avoid duplication