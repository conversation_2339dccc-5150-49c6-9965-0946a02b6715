<?php
// Include configuration file
require_once 'includes/config.php';

// Get the activity ID from the URL or use default
$activityId = isset($_GET['id']) ? intval($_GET['id']) : 6;

try {
    // Get current activity details
    $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        echo "<h2>Activity not found</h2>";
        echo "<p>No activity found with ID: $activityId</p>";
        exit;
    }

    $activity = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentType = $activity['activity_type'];

    echo "<h2>Activity Details</h2>";
    echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
    echo "<p><strong>Current Type:</strong> " . htmlspecialchars($currentType) . "</p>";

    // Update the activity type to 'activity'
    $newType = 'activity';
    $updateStmt = $pdo->prepare("UPDATE activities SET activity_type = :newType WHERE activity_id = :activityId");
    $updateStmt->bindParam(':newType', $newType);
    $updateStmt->bindParam(':activityId', $activityId);

    if ($updateStmt->execute()) {
        echo "<p style='color: green;'>Activity type updated from '{$currentType}' to '{$newType}'.</p>";
        echo "<p>Please <a href='activity_edit.php?id=$activityId#questions'>click here</a> to go to the edit page with the Questions tab active.</p>";
    } else {
        echo "<p style='color: red;'>Error updating activity type.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
