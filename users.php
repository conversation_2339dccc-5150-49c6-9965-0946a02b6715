<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/user_functions.php';
require_once 'includes/auth.php';

// Check if the user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: index.php");
    exit;
}

// Get all users
$users = getAllUsers();

// Check if users is an error message
if (is_string($users)) {
    $error = $users;
    $users = [];
}

// Get all roles
$roles = getAllRoles();

// Check if roles is an error message
if (is_string($roles)) {
    $error = $roles;
    $roles = [];
}

// Process user activation/deactivation
if (isset($_GET['action']) && isset($_GET['id'])) {
    $userId = $_GET['id'];
    $action = $_GET['action'];

    if ($action === 'activate') {
        $result = setUserActiveStatus($userId, true);
        if ($result === true) {
            $success = "User activated successfully.";
            // Refresh the users list
            $users = getAllUsers();
        } else {
            $error = $result;
        }
    } elseif ($action === 'deactivate') {
        $result = setUserActiveStatus($userId, false);
        if ($result === true) {
            $success = "User deactivated successfully.";
            // Refresh the users list
            $users = getAllUsers();
        } else {
            $error = $result;
        }
    }
}

// Process user creation
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = trim($_POST["username"]);
    $password = trim($_POST["password"]);
    $email = trim($_POST["email"]);
    $firstName = trim($_POST["first_name"]);
    $lastName = trim($_POST["last_name"]);
    $gender = isset($_POST["gender"]) ? trim($_POST["gender"]) : "";
    $birthday = isset($_POST["birthday"]) ? trim($_POST["birthday"]) : "";
    $phoneNumber = isset($_POST["phone_number"]) ? trim($_POST["phone_number"]) : "";
    $roleId = trim($_POST["role_id"]);

    // Validate input
    $input_error = false;

    if (empty($username)) {
        $username_err = "Please enter a username.";
        $input_error = true;
    }

    if (empty($password)) {
        $password_err = "Please enter a password.";
        $input_error = true;
    } elseif (strlen($password) < 6) {
        $password_err = "Password must have at least 6 characters.";
        $input_error = true;
    }

    if (empty($email)) {
        $email_err = "Please enter an email.";
        $input_error = true;
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email.";
        $input_error = true;
    }

    if (empty($firstName)) {
        $firstName_err = "Please enter a first name.";
        $input_error = true;
    }

    if (empty($lastName)) {
        $lastName_err = "Please enter a last name.";
        $input_error = true;
    }

    if (empty($roleId)) {
        $roleId_err = "Please select a role.";
        $input_error = true;
    }

    // Validate gender if provided
    if (!empty($gender) && !in_array($gender, ['male', 'female', 'other'])) {
        $gender_err = "Please select a valid gender.";
        $input_error = true;
    }

    // Validate birthday if provided
    if (!empty($birthday)) {
        $date = date_create_from_format('Y-m-d', $birthday);
        if (!$date || date_format($date, 'Y-m-d') !== $birthday) {
            $birthday_err = "Please enter a valid date in YYYY-MM-DD format.";
            $input_error = true;
        }
    }

    // Validate phone number if provided
    if (!empty($phoneNumber) && !preg_match('/^[0-9+\-\s()]{7,20}$/', $phoneNumber)) {
        $phoneNumber_err = "Please enter a valid phone number.";
        $input_error = true;
    }

    // If no input errors, create the user
    if (!$input_error) {
        $result = registerUser($username, $password, $email, $firstName, $lastName, $roleId, $gender, $birthday, $phoneNumber);

        if ($result === true) {
            $success = "User created successfully.";
            // Refresh the users list
            $users = getAllUsers();
            // Clear form data
            $username = $password = $email = $firstName = $lastName = $gender = $birthday = $phoneNumber = $roleId = "";
        } else {
            $error = $result;
        }
    }
}

// Set page title
$page_title = "Manage Users";

// Include header
require_once 'includes/header.php';
?>

<style>
    /* Date input styling */
    input[type="date"] {
        padding-right: 10px;
    }

    /* Select styling for consistency */
    select.form-control {
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0.375rem 0.75rem;
    }

    /* Helper text styling */
    .text-muted {
        color: #6c757d;
        font-size: 12px;
        margin-top: 5px;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Manage Users</h1>
    <a href="user_add.php" class="btn btn-primary" style="border-radius: 4px;">
        <i class="fas fa-plus"></i> Add User
    </a>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
<div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus"></i> Add New User</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div class="form-group">
                        <label>Username</label>
                        <input type="text" name="username" class="form-control <?php echo isset($username_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($username) ? $username : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($username_err) ? $username_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" name="password" class="form-control <?php echo isset($password_err) ? 'is-invalid' : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($password_err) ? $password_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" name="email" class="form-control <?php echo isset($email_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($email) ? $email : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($email_err) ? $email_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>First Name</label>
                        <input type="text" name="first_name" class="form-control <?php echo isset($firstName_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($firstName) ? $firstName : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($firstName_err) ? $firstName_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Last Name</label>
                        <input type="text" name="last_name" class="form-control <?php echo isset($lastName_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($lastName) ? $lastName : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($lastName_err) ? $lastName_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Gender</label>
                        <select name="gender" class="form-control <?php echo isset($gender_err) ? 'is-invalid' : ''; ?>">
                            <option value="">Select Gender</option>
                            <option value="male" <?php echo isset($gender) && $gender == 'male' ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo isset($gender) && $gender == 'female' ? 'selected' : ''; ?>>Female</option>
                            <option value="other" <?php echo isset($gender) && $gender == 'other' ? 'selected' : ''; ?>>Other</option>
                        </select>
                        <span class="invalid-feedback"><?php echo isset($gender_err) ? $gender_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Birthday</label>
                        <input type="date" name="birthday" class="form-control <?php echo isset($birthday_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($birthday) ? $birthday : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($birthday_err) ? $birthday_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Phone Number</label>
                        <input type="text" name="phone_number" class="form-control <?php echo isset($phoneNumber_err) ? 'is-invalid' : ''; ?>" value="<?php echo isset($phoneNumber) ? $phoneNumber : ''; ?>">
                        <span class="invalid-feedback"><?php echo isset($phoneNumber_err) ? $phoneNumber_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Role</label>
                        <select name="role_id" class="form-control <?php echo isset($roleId_err) ? 'is-invalid' : ''; ?>">
                            <option value="">Select Role</option>
                            <?php foreach ($roles as $role): ?>
                            <option value="<?php echo $role['role_id']; ?>" <?php echo isset($roleId) && $roleId == $role['role_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($role['role_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <span class="invalid-feedback"><?php echo isset($roleId_err) ? $roleId_err : ''; ?></span>
                    </div>
                    <div class="form-group">
                        <input type="submit" class="btn btn-primary" value="Add User">
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> User List</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>USERNAME</th>
                                <th>EMAIL</th>
                                <th>NAME</th>
                                <th>ROLE</th>
                                <th>STATUS</th>
                                <th>ACTIONS</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                                <td><?php echo htmlspecialchars(ucfirst($user['role_name'])); ?></td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                    <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                    <a href="users.php?action=deactivate&id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-warning" style="border: 2px solid #ffc107;" title="Deactivate" onclick="return confirm('Are you sure you want to deactivate this user?');"><i class="fas fa-user-slash"></i></a>
                                    <?php else: ?>
                                    <a href="users.php?action=activate&id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-success" style="border: 2px solid #28a745;" title="Activate"><i class="fas fa-user-check"></i></a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
