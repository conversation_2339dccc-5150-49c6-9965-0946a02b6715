<?php
// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "<br>");
}

echo "<h1>Database Fix Script</h1>";

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn->query($sql) === TRUE) {
    echo "Database created or already exists<br>";
} else {
    echo "Error creating database: " . $conn->error . "<br>";
}

// Select the database
$conn->select_db(DB_NAME);

// Check if users table exists
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows == 0) {
    // Create users table
    $sql = "CREATE TABLE users (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student',
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Users table created successfully<br>";
    } else {
        echo "Error creating users table: " . $conn->error . "<br>";
    }
} else {
    echo "Users table already exists<br>";
    
    // Check if role column exists
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
    if ($result->num_rows == 0) {
        // Add role column
        $sql = "ALTER TABLE users ADD COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student' AFTER password";
        
        if ($conn->query($sql) === TRUE) {
            echo "Role column added successfully<br>";
        } else {
            echo "Error adding role column: " . $conn->error . "<br>";
        }
    } else {
        echo "Role column already exists<br>";
        
        // Check role column type
        $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
        $row = $result->fetch_assoc();
        echo "Current role column type: " . $row['Type'] . "<br>";
        
        // If role column is not ENUM, modify it
        if (strpos($row['Type'], "enum('admin','teacher','student')") === false) {
            $sql = "ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student'";
            
            if ($conn->query($sql) === TRUE) {
                echo "Role column type modified successfully<br>";
            } else {
                echo "Error modifying role column type: " . $conn->error . "<br>";
            }
        }
    }
}

// Check if admin user exists, if not create one
$stmt = $conn->prepare("SELECT id FROM users WHERE username = 'admin'");
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows == 0) {
    // Create admin user
    $admin_password = password_hash("admin123", PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT INTO users (first_name, last_name, username, email, password, role) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssss", $first_name, $last_name, $username, $email, $password, $role);
    
    $first_name = "Admin";
    $last_name = "User";
    $username = "admin";
    $email = "<EMAIL>";
    $password = $admin_password;
    $role = "admin";
    
    if ($stmt->execute()) {
        echo "Admin user created successfully<br>";
    } else {
        echo "Error creating admin user: " . $stmt->error . "<br>";
    }
} else {
    echo "Admin user already exists<br>";
}

// Show the current table structure
$result = $conn->query("SHOW COLUMNS FROM users");
if ($result) {
    echo "<h2>Current Users Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

echo "<p>Database fix complete. You can now <a href='register.php'>register</a> or <a href='login.php'>login</a>.</p>";

$conn->close();
?>
