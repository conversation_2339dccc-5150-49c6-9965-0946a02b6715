<?php
/**
 * Database Normalization Analysis Report
 * 
 * This report explains the normalization improvements and benefits
 */

require_once 'includes/config.php';

$page_title = "Database Normalization Report";
require_once 'includes/header.php';
?>

<style>
    .report-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .normalization-level {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 15px;
        margin: 15px 0;
        border-radius: 0 5px 5px 0;
    }
    
    .before-after {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
    }
    
    .before, .after {
        padding: 15px;
        border-radius: 5px;
    }
    
    .before {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
    }
    
    .after {
        background: #d4edda;
        border: 1px solid #c3e6cb;
    }
    
    .table-structure {
        font-family: monospace;
        background: #f8f9fa;
        padding: 10px;
        border-radius: 3px;
        font-size: 12px;
    }
    
    .benefit-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .issue-resolved {
        background: #d4edda;
        border-left: 4px solid #28a745;
        padding: 10px 15px;
        margin: 10px 0;
    }
</style>

<div class="report-container">
    <h1><i class="fas fa-database"></i> Database Normalization Report</h1>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> Executive Summary</h5>
        <p>This report details the database normalization process applied to the e-learning system, 
        transforming the database structure to follow First Normal Form (1NF), Second Normal Form (2NF), 
        and Third Normal Form (3NF) principles. The normalization eliminates data redundancy, 
        improves data integrity, and enhances system performance.</p>
    </div>
    
    <h2>Normalization Levels Applied</h2>
    
    <div class="normalization-level">
        <h4><i class="fas fa-layer-group"></i> First Normal Form (1NF)</h4>
        <p><strong>Rule:</strong> Eliminate repeating groups and ensure atomic values</p>
        <ul>
            <li>✅ Separated user profile data from core user authentication data</li>
            <li>✅ Created separate tables for addresses, phone numbers, and file attachments</li>
            <li>✅ Eliminated multi-value fields and ensured each field contains atomic values</li>
        </ul>
    </div>
    
    <div class="normalization-level">
        <h4><i class="fas fa-key"></i> Second Normal Form (2NF)</h4>
        <p><strong>Rule:</strong> Eliminate partial dependencies on composite keys</p>
        <ul>
            <li>✅ Ensured all non-key attributes depend on the entire primary key</li>
            <li>✅ Created proper primary keys for all tables</li>
            <li>✅ Separated course instructor relationships into junction table</li>
        </ul>
    </div>
    
    <div class="normalization-level">
        <h4><i class="fas fa-sitemap"></i> Third Normal Form (3NF)</h4>
        <p><strong>Rule:</strong> Eliminate transitive dependencies</p>
        <ul>
            <li>✅ Created lookup tables for roles, statuses, categories, and types</li>
            <li>✅ Eliminated redundant storage of descriptive data</li>
            <li>✅ Ensured non-key attributes depend only on primary keys</li>
        </ul>
    </div>
    
    <h2>Key Improvements</h2>
    
    <div class="before-after">
        <div class="before">
            <h5>❌ Before Normalization</h5>
            <div class="table-structure">
users {
    user_id, username, email, password,
    first_name, last_name, gender, birthday,
    phone_number, profile_picture,
    role, is_active, created_at, updated_at
}

courses {
    course_id, title, description,
    created_by, capacity, semester,
    is_active, is_archived, created_at
}
            </div>
            <p><strong>Issues:</strong></p>
            <ul>
                <li>Role stored as string (denormalized)</li>
                <li>Mixed authentication and profile data</li>
                <li>No proper status management</li>
                <li>Limited extensibility</li>
            </ul>
        </div>
        
        <div class="after">
            <h5>✅ After Normalization</h5>
            <div class="table-structure">
users_normalized {
    user_id, username, email, password_hash,
    role_id → roles(role_id),
    status_id → user_statuses(status_id),
    last_login, created_at, updated_at
}

user_profiles {
    profile_id, user_id → users_normalized(user_id),
    first_name, last_name, gender, date_of_birth,
    phone_number, bio, profile_picture_url
}

roles {
    role_id, role_name, role_description, permissions
}

courses_normalized {
    course_id, course_code, title, description,
    category_id → course_categories(category_id),
    semester_id → semesters(semester_id),
    instructor_id → users_normalized(user_id)
}
            </div>
            <p><strong>Benefits:</strong></p>
            <ul>
                <li>Proper foreign key relationships</li>
                <li>Separated concerns (auth vs profile)</li>
                <li>Extensible lookup tables</li>
                <li>Better data integrity</li>
            </ul>
        </div>
    </div>
    
    <h2>Specific Issues Resolved</h2>
    
    <div class="benefit-card">
        <h4><i class="fas fa-users"></i> User Management Improvements</h4>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Role stored as ENUM string in users table<br>
            <strong>Solution:</strong> Created roles lookup table with role_id foreign key<br>
            <strong>Benefit:</strong> Easy to add new roles, better permission management
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Mixed authentication and profile data in single table<br>
            <strong>Solution:</strong> Separated into users_normalized and user_profiles tables<br>
            <strong>Benefit:</strong> Better security, optional profile data, cleaner structure
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> No proper user status management<br>
            <strong>Solution:</strong> Created user_statuses lookup table<br>
            <strong>Benefit:</strong> Flexible status management (active, suspended, pending, etc.)
        </div>
    </div>
    
    <div class="benefit-card">
        <h4><i class="fas fa-graduation-cap"></i> Course Management Improvements</h4>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> No course categorization system<br>
            <strong>Solution:</strong> Created course_categories table with hierarchical support<br>
            <strong>Benefit:</strong> Better course organization and filtering
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Semester information stored as string<br>
            <strong>Solution:</strong> Created semesters lookup table<br>
            <strong>Benefit:</strong> Proper academic year management, date ranges
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Single instructor per course limitation<br>
            <strong>Solution:</strong> Created course_instructors junction table<br>
            <strong>Benefit:</strong> Multiple instructors per course, role-based assignments
        </div>
    </div>
    
    <div class="benefit-card">
        <h4><i class="fas fa-tasks"></i> Activity and Assessment Improvements</h4>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Activity types stored as ENUM<br>
            <strong>Solution:</strong> Created activity_types lookup table<br>
            <strong>Benefit:</strong> Extensible activity types, configurable properties
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> Question types hardcoded<br>
            <strong>Solution:</strong> Created question_types lookup table<br>
            <strong>Benefit:</strong> Flexible question types, configurable options
        </div>
        
        <div class="issue-resolved">
            <strong>Issue:</strong> File attachments mixed with content<br>
            <strong>Solution:</strong> Separate activity_files and submission_files tables<br>
            <strong>Benefit:</strong> Better file management, metadata tracking
        </div>
    </div>
    
    <h2>Performance Benefits</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="benefit-card">
                <h5><i class="fas fa-tachometer-alt"></i> Query Performance</h5>
                <ul>
                    <li>Proper indexing on foreign keys</li>
                    <li>Reduced table sizes through normalization</li>
                    <li>Optimized JOIN operations</li>
                    <li>Better query execution plans</li>
                </ul>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="benefit-card">
                <h5><i class="fas fa-shield-alt"></i> Data Integrity</h5>
                <ul>
                    <li>Foreign key constraints prevent orphaned records</li>
                    <li>Referential integrity maintained automatically</li>
                    <li>Consistent data types and formats</li>
                    <li>Reduced data redundancy and inconsistencies</li>
                </ul>
            </div>
        </div>
    </div>
    
    <h2>Backward Compatibility</h2>
    
    <div class="alert alert-success">
        <h5><i class="fas fa-check-circle"></i> Compatibility Views Created</h5>
        <p>To ensure existing application code continues to work, compatibility views have been created:</p>
        <ul>
            <li><code>users_view</code> - Provides the original users table structure</li>
            <li><code>courses_view</code> - Provides the original courses table structure</li>
            <li><code>activities_view</code> - Provides the original activities table structure</li>
        </ul>
        <p>These views allow gradual migration of application code to use the normalized tables.</p>
    </div>
    
    <h2>Migration Statistics</h2>
    
    <?php
    try {
        // Get migration statistics
        $stats = [];
        
        $tables = ['users', 'courses', 'activities', 'enrollments'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $originalCount = $stmt->fetchColumn();
                
                $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}_normalized");
                $normalizedCount = $stmt->fetchColumn();
                
                $stats[$table] = [
                    'original' => $originalCount,
                    'normalized' => $normalizedCount,
                    'success' => $originalCount == $normalizedCount
                ];
            } catch (PDOException $e) {
                $stats[$table] = [
                    'original' => 'N/A',
                    'normalized' => 'N/A', 
                    'success' => false
                ];
            }
        }
        
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Table</th><th>Original Records</th><th>Normalized Records</th><th>Status</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($stats as $table => $data) {
            $statusIcon = $data['success'] ? '✅' : '❌';
            $statusText = $data['success'] ? 'Success' : 'Needs Review';
            
            echo "<tr>";
            echo "<td><strong>$table</strong></td>";
            echo "<td>{$data['original']}</td>";
            echo "<td>{$data['normalized']}</td>";
            echo "<td>$statusIcon $statusText</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-warning'>Unable to retrieve migration statistics: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <h2>Next Steps</h2>
    
    <div class="alert alert-primary">
        <h5><i class="fas fa-road"></i> Implementation Roadmap</h5>
        <ol>
            <li><strong>Phase 1:</strong> Test all functionality with compatibility views</li>
            <li><strong>Phase 2:</strong> Gradually update application code to use normalized tables</li>
            <li><strong>Phase 3:</strong> Remove compatibility views and old tables</li>
            <li><strong>Phase 4:</strong> Implement additional features enabled by normalization</li>
        </ol>
    </div>
    
    <div class="text-center mt-4">
        <a href="normalize_database.php" class="btn btn-primary">
            <i class="fas fa-play"></i> Run Normalization Process
        </a>
        <a href="analyze_database_structure.php" class="btn btn-secondary ml-2">
            <i class="fas fa-search"></i> Analyze Current Structure
        </a>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
