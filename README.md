# Classroom - E-Learning System

A Google Classroom-inspired e-learning platform built with PHP and MySQL, featuring a normalized SQL database and admin-only account management privileges.

## Features

- User Management (Admin only)
  - Create, update, delete, and manage user accounts
  - Assign roles (Ad<PERSON>, Teacher, Student)
  - Activate/deactivate user accounts

- Course Management
  - Create and manage courses (Admin and Teachers)
  - Organize content into modules and lessons
  - Enroll students in courses

- Content Management
  - Create and organize learning materials
  - Structure content into modules and lessons
  - Track student progress through lessons

- Assessment System
  - Create assignments with due dates
  - Submit and grade assignments
  - C<PERSON> quizzes with various question types
  - Automatically grade quizzes

- Discussion Forums
  - Create course-specific forums
  - Post topics and replies
  - Facilitate student-teacher communication

## Database Structure

The database follows normalization principles (up to 3NF) and includes the following tables:

- `roles`: Stores user roles (admin, teacher, student)
- `users`: Stores user account information
- `courses`: Stores course information
- `modules`: Stores module information for courses
- `lessons`: Stores lesson content for modules
- `enrollments`: Tracks student enrollment in courses
- `lesson_progress`: Tracks student progress through lessons
- `assignments`: Stores assignment information
- `submissions`: Stores student assignment submissions
- `quizzes`: Stores quiz information
- `quiz_questions`: Stores quiz questions
- `quiz_options`: Stores options for quiz questions
- `quiz_attempts`: Tracks student quiz attempts
- `quiz_responses`: Stores student responses to quiz questions
- `forums`: Stores forum information for courses
- `forum_topics`: Stores forum topics
- `forum_replies`: Stores replies to forum topics

## Installation

1. **Prerequisites**
   - PHP 7.4 or higher
   - MySQL 5.7 or higher
   - Web server (Apache, Nginx, etc.)
   - XAMPP, WAMP, MAMP, or similar local development environment

2. **Database Setup**
   - Create a new database in MySQL
   - Import the database schema from `database/elearning_schema.sql`

3. **Configuration**
   - Update the database connection settings in `includes/config.php`
   - Set the appropriate base URL in `includes/config.php`

4. **Web Server Setup**
   - Place the project files in your web server's document root
   - Ensure the web server has read/write permissions for the project directory

## Usage

1. **Access the System**
   - Open a web browser and navigate to the project URL
   - Log in with the default admin account:
     - Username: admin
     - Password: admin123

2. **User Management (Admin Only)**
   - Create new user accounts with appropriate roles
   - Manage existing user accounts
   - Activate/deactivate user accounts

3. **Course Management**
   - Create new courses (Admin and Teachers)
   - Organize course content into modules and lessons
   - Enroll students in courses

4. **Content Access**
   - Students can access enrolled courses
   - Navigate through modules and lessons
   - Track progress through course content

5. **Assessments**
   - Create and manage assignments and quizzes
   - Students can submit assignments and take quizzes
   - Teachers can grade assignments and review quiz results

6. **Discussion Forums**
   - Create and participate in course-specific forums
   - Post topics and replies
   - Engage in discussions with teachers and other students

## Security Features

- Password hashing using PHP's password_hash() function
- Session management with secure session settings
- Input sanitization to prevent XSS attacks
- Role-based access control
- Admin-only user management

## File Structure

- `css/`: Contains CSS stylesheets
  - `classroom.css`: Google Classroom-inspired styles
- `database/`: Contains database schema
- `includes/`: Contains PHP include files
  - `config.php`: Configuration settings
  - `auth.php`: Authentication functions
  - `user_functions.php`: User management functions
  - `course_functions.php`: Course management functions
  - `content_functions.php`: Content management functions
  - `assessment_functions.php`: Assignment functions
  - `quiz_functions.php`: Quiz management functions
  - `quiz_attempt_functions.php`: Quiz attempt functions
  - `forum_functions.php`: Forum functions
- `index.php`: Main dashboard
- `login.php`: Login page
- `logout.php`: Logout script
- `profile.php`: User profile page
- `users.php`: User management page (Admin only)
- `course_view.php`: Course view page with stream, classwork, people, and grades tabs
- `course_add.php`: Create new course page
- `assignment_view.php`: Assignment view and submission page
- `calendar.php`: Calendar view for assignments and events
- `post_announcement.php`: Handler for posting announcements
- Various other PHP files for system functionality

## Default Accounts

- **Admin Account**
  - Username: admin
  - Password: admin123

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Google Classroom for design inspiration
- Bootstrap for the responsive UI
- Font Awesome for icons
- Google Fonts for typography
- jQuery for JavaScript functionality
