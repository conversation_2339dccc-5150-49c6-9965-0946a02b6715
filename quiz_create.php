<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/quiz_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to access this page.";
    header("location: index.php");
    exit;
}

// Check if course_id is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to create quizzes for this course
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to create quizzes for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get modules for the course
$modules = getModulesByCourse($courseId);
if (is_string($modules)) {
    $modules = [];
}

// Initialize variables
$title = $description = $dueDate = "";
$moduleId = 0; // No module needed
$points = 0; // Points will be set per question later
$timeLimit = 60; // Default time limit: 60 minutes
$isPublished = true;
$allowLateSubmissions = false;
$title_err = $description_err = $timeLimit_err = $dueDate_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Module ID is always 0 (no module)
    $moduleId = 0;

    // Points will be set per question later
    $points = 0; // Default to 0

    // Validate time limit
    if (empty(trim($_POST["time_limit"]))) {
        $timeLimit_err = "Please enter a time limit.";
    } else {
        $timeLimit = intval($_POST["time_limit"]);
        if ($timeLimit <= 0) {
            $timeLimit_err = "Time limit must be greater than 0.";
        }
    }

    // Validate due date (optional)
    if (!empty(trim($_POST["due_date"]))) {
        $dueDate = trim($_POST["due_date"]);

        // Check if date is valid
        $dateTime = DateTime::createFromFormat('Y-m-d\TH:i', $dueDate);
        if (!$dateTime || $dateTime->format('Y-m-d\TH:i') !== $dueDate) {
            $dueDate_err = "Please enter a valid date and time.";
        }
    }

    // Check if published
    $isPublished = isset($_POST["is_published"]) ? true : false;

    // Check if late submissions are allowed
    $allowLateSubmissions = isset($_POST["allow_late_submissions"]) ? true : false;

    // File uploads have been removed

    // Check input errors before creating the quiz
    if (empty($title_err) && empty($description_err) && empty($timeLimit_err) && empty($dueDate_err)) {
        // Create the quiz
        $result = createQuiz($courseId, $title, $description, $points, $dueDate, $timeLimit, $moduleId, $allowLateSubmissions, $isPublished, $_SESSION['user_id'], null);

        if (is_numeric($result)) {
            // Quiz created successfully
            $activityId = $result;

            // Redirect to quiz edit page to add questions
            $_SESSION['success'] = "Quiz created successfully. Now add questions to your quiz.";
            header("location: quiz_edit.php?id=$activityId");
            exit;
        } else {
            // Error creating quiz
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Create Quiz";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Quiz</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create a quiz for students to complete and submit.</p>
        </div>
    </div>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
<div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<!-- Quiz form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>

            <div class="form-group">
                <label for="description">Instructions</label>
                <textarea name="description" id="description" rows="6" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>"><?php echo $description; ?></textarea>
                <span class="invalid-feedback"><?php echo $description_err; ?></span>
            </div>

            <!-- Points will be set per question later -->

            <div class="form-group">
                <label for="time_limit">Time Limit (minutes)</label>
                <input type="number" name="time_limit" id="time_limit" class="form-control <?php echo (!empty($timeLimit_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $timeLimit; ?>" min="1">
                <span class="invalid-feedback"><?php echo $timeLimit_err; ?></span>
                <small class="form-text text-muted">Time allowed for students to complete the quiz.</small>
            </div>

            <div class="form-group">
                <label for="due_date">Due Date (optional)</label>
                <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $dueDate; ?>">
                <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                <small class="form-text text-muted">If no due date is set, the quiz will be available indefinitely.</small>
            </div>

            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" <?php echo $allowLateSubmissions ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                    <small class="form-text text-muted">If checked, students can submit after the due date, but submissions will be marked as late.</small>
                </div>
            </div>



            <div class="form-group">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="is_published">Publish quiz (visible to students)</label>
                </div>
            </div>

            <div class="form-group">
                <label>Create Questions</label>
                <div class="alert alert-info">
                    <p class="mb-0">After creating this quiz, you'll be able to add questions of the following types:</p>
                    <ul class="mt-2 mb-0">
                        <li><strong>Multiple Choice</strong> - Students select one correct answer from several options</li>
                        <li><strong>True/False</strong> - Students select whether a statement is true or false</li>
                        <li><strong>Short Answer</strong> - Students type in a brief text response</li>
                    </ul>
                    <p class="mt-2 mb-0"><strong>Note:</strong> You'll be able to set points for each question individually after creating the quiz.</p>
                </div>
                <small class="form-text text-muted mt-2">You'll be redirected to the question editor after creating this quiz.</small>
            </div>

            <!-- No module field needed -->

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Create Quiz</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
