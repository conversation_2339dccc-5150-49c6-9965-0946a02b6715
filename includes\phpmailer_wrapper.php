<?php
/**
 * PHPMailer Wrapper Class
 *
 * A wrapper around <PERSON><PERSON><PERSON>ail<PERSON> to provide easy email sending functionality
 * with proper SMTP configuration and error handling.
 */

require_once 'mailer_config.php';
require_once 'fallback_mailer.php';

// Include PHPMailer classes
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/PHPMailer.php';
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/SMTP.php';
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class PHPMailerWrapper {
    private $mailer;
    private $error;

    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->setupSMTP();
    }

    /**
     * Setup SMTP configuration
     */
    private function setupSMTP() {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = SMTP_AUTH;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = SMTP_SECURE === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = SMTP_PORT;

            // Content settings
            $this->mailer->isHTML(false); // Default to plain text
            $this->mailer->CharSet = MAIL_CHARSET;
            $this->mailer->Encoding = MAIL_ENCODING;

            // Debug settings
            if (MAIL_DEBUG > 0) {
                $this->mailer->SMTPDebug = MAIL_DEBUG;
                $this->mailer->Debugoutput = 'html';
            }

            // Default sender
            if (defined('MAIL_FROM_EMAIL') && defined('MAIL_FROM_NAME')) {
                $this->mailer->setFrom(MAIL_FROM_EMAIL, MAIL_FROM_NAME);
            }

        } catch (Exception $e) {
            $this->error = "SMTP setup failed: " . $e->getMessage();
            error_log($this->error);
        }
    }

    /**
     * Send an email
     *
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @param string $fromEmail Optional sender email
     * @param string $fromName Optional sender name
     * @return bool True if email was sent successfully, false otherwise
     */
    public function sendEmail($to, $subject, $message, $isHtml = false, $fromEmail = null, $fromName = null) {
        try {
            // Clear any previous recipients
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();

            // Set sender if provided
            if ($fromEmail && $fromName) {
                $this->mailer->setFrom($fromEmail, $fromName);
            }

            // Recipients
            $this->mailer->addAddress($to);

            // Content
            $this->mailer->isHTML($isHtml);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $message;

            // If HTML, also set plain text version
            if ($isHtml) {
                $this->mailer->AltBody = strip_tags($message);
            }

            // Send the email
            $result = $this->mailer->send();

            if ($result) {
                // Log successful email
                $this->logEmail($to, $subject, 'SUCCESS');
                return true;
            } else {
                $this->error = "Failed to send email";
                $this->logEmail($to, $subject, 'FAILED: ' . $this->error);
                return false;
            }

        } catch (Exception $e) {
            $this->error = "Email sending failed: " . $e->getMessage();
            error_log($this->error);
            $this->logEmail($to, $subject, 'ERROR: ' . $this->error);

            // In development mode, return true to allow application to continue
            if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
                return true;
            }

            return false;
        }
    }

    /**
     * Send HTML email
     *
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $htmlMessage HTML email message
     * @param string $textMessage Optional plain text version
     * @return bool True if email was sent successfully, false otherwise
     */
    public function sendHtmlEmail($to, $subject, $htmlMessage, $textMessage = null) {
        try {
            // Clear any previous recipients
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();

            // Recipients
            $this->mailer->addAddress($to);

            // Content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $htmlMessage;
            $this->mailer->AltBody = $textMessage ?: strip_tags($htmlMessage);

            // Send the email
            $result = $this->mailer->send();

            if ($result) {
                $this->logEmail($to, $subject, 'SUCCESS (HTML)');
                return true;
            } else {
                $this->error = "Failed to send HTML email";
                $this->logEmail($to, $subject, 'FAILED: ' . $this->error);
                return false;
            }

        } catch (Exception $e) {
            $this->error = "HTML email sending failed: " . $e->getMessage();
            error_log($this->error);
            $this->logEmail($to, $subject, 'ERROR: ' . $this->error);

            // In development mode, return true to allow application to continue
            if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
                return true;
            }

            return false;
        }
    }

    /**
     * Get the last error message
     *
     * @return string Error message
     */
    public function getError() {
        return $this->error;
    }

    /**
     * Log email sending attempts
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $status Status message
     */
    private function logEmail($to, $subject, $status) {
        $logDir = __DIR__ . '/../logs';
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . '/email_log.txt';
        $logEntry = date('Y-m-d H:i:s') . " | TO: $to | SUBJECT: $subject | STATUS: $status" . PHP_EOL;

        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Test SMTP connection
     *
     * @return bool True if connection successful, false otherwise
     */
    public function testConnection() {
        try {
            $this->mailer->smtpConnect();
            $this->mailer->smtpClose();
            return true;
        } catch (Exception $e) {
            $this->error = "SMTP connection test failed: " . $e->getMessage();
            error_log($this->error);
            return false;
        }
    }

    /**
     * Static method to send email easily with fallback
     *
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @return bool True if email was sent successfully, false otherwise
     */
    public static function send($to, $subject, $message, $isHtml = false) {
        try {
            $mailer = new self();
            $result = $mailer->sendEmail($to, $subject, $message, $isHtml);

            // If PHPMailer failed, try fallback mailer
            if (!$result) {
                error_log("PHPMailer failed, trying fallback mailer");
                return FallbackMailer::send($to, $subject, $message, $isHtml);
            }

            return $result;

        } catch (Exception $e) {
            error_log("PHPMailer exception: " . $e->getMessage() . ", trying fallback mailer");
            return FallbackMailer::send($to, $subject, $message, $isHtml);
        }
    }
}
?>
