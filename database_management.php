<?php
/**
 * Database Management Dashboard
 * Central hub for database normalization and management tools
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

$page_title = "Database Management";
require_once 'includes/header.php';
?>

<style>
    .management-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .tool-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 25px;
        margin: 20px 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .tool-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }
    
    .tool-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        display: block;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-success { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-danger { background-color: #dc3545; }
    .status-info { background-color: #17a2b8; }
    
    .progress-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .step-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .step-item:last-child {
        border-bottom: none;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-weight: bold;
    }
    
    .step-number.completed {
        background: #28a745;
    }
    
    .step-number.current {
        background: #007bff;
    }
</style>

<div class="management-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-database"></i> Database Management Dashboard</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> Database Normalization Center</h5>
        <p>This dashboard provides tools to analyze, normalize, and optimize your database structure. 
        Follow the recommended process to ensure data integrity and improved performance.</p>
    </div>
    
    <?php
    // Check current database status
    $hasNormalizedTables = false;
    $hasOriginalTables = false;
    $migrationComplete = false;
    
    try {
        // Check if normalized tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE '%_normalized'");
        $normalizedTables = $stmt->fetchAll();
        $hasNormalizedTables = !empty($normalizedTables);
        
        // Check if original tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        $originalTables = $stmt->fetchAll();
        $hasOriginalTables = !empty($originalTables);
        
        // Check if migration is complete
        if ($hasNormalizedTables && $hasOriginalTables) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM users");
            $originalCount = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM users_normalized");
            $normalizedCount = $stmt->fetchColumn();
            
            $migrationComplete = ($originalCount == $normalizedCount && $normalizedCount > 0);
        }
        
    } catch (PDOException $e) {
        // Handle database errors gracefully
    }
    ?>
    
    <div class="progress-section">
        <h4><i class="fas fa-tasks"></i> Normalization Progress</h4>
        
        <div class="step-item">
            <div class="step-number <?php echo $hasOriginalTables ? 'completed' : 'current'; ?>">1</div>
            <div>
                <strong>Database Analysis</strong><br>
                <small class="text-muted">Analyze current database structure and identify normalization opportunities</small>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number <?php echo $hasNormalizedTables ? 'completed' : ($hasOriginalTables ? 'current' : ''); ?>">2</div>
            <div>
                <strong>Schema Creation</strong><br>
                <small class="text-muted">Create normalized table structure following 1NF, 2NF, and 3NF principles</small>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number <?php echo $migrationComplete ? 'completed' : ($hasNormalizedTables ? 'current' : ''); ?>">3</div>
            <div>
                <strong>Data Migration</strong><br>
                <small class="text-muted">Migrate existing data to normalized tables with integrity checks</small>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number <?php echo $migrationComplete ? 'current' : ''; ?>">4</div>
            <div>
                <strong>Testing & Validation</strong><br>
                <small class="text-muted">Test application functionality and validate data integrity</small>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Analysis Tools -->
        <div class="col-md-6">
            <div class="tool-card">
                <div class="text-center">
                    <i class="fas fa-search tool-icon text-primary"></i>
                    <h4>Database Analysis</h4>
                    <p class="text-muted">Analyze current database structure and identify normalization issues</p>
                </div>
                
                <div class="mb-3">
                    <span class="status-indicator <?php echo $hasOriginalTables ? 'status-success' : 'status-warning'; ?>"></span>
                    <strong>Status:</strong> <?php echo $hasOriginalTables ? 'Ready for Analysis' : 'Database Not Found'; ?>
                </div>
                
                <div class="text-center">
                    <a href="analyze_database_structure.php" class="btn btn-primary">
                        <i class="fas fa-search"></i> Analyze Structure
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Normalization Report -->
        <div class="col-md-6">
            <div class="tool-card">
                <div class="text-center">
                    <i class="fas fa-file-alt tool-icon text-info"></i>
                    <h4>Normalization Report</h4>
                    <p class="text-muted">Detailed report on normalization benefits and implementation plan</p>
                </div>
                
                <div class="mb-3">
                    <span class="status-indicator status-info"></span>
                    <strong>Status:</strong> Available
                </div>
                
                <div class="text-center">
                    <a href="normalization_report.php" class="btn btn-info">
                        <i class="fas fa-file-alt"></i> View Report
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Normalization Process -->
        <div class="col-md-6">
            <div class="tool-card">
                <div class="text-center">
                    <i class="fas fa-cogs tool-icon text-success"></i>
                    <h4>Run Normalization</h4>
                    <p class="text-muted">Execute the complete database normalization process</p>
                </div>
                
                <div class="mb-3">
                    <span class="status-indicator <?php echo $hasNormalizedTables ? 'status-success' : 'status-warning'; ?>"></span>
                    <strong>Status:</strong> <?php echo $hasNormalizedTables ? 'Normalized Tables Exist' : 'Ready to Normalize'; ?>
                </div>
                
                <div class="text-center">
                    <a href="normalize_database.php" class="btn btn-success">
                        <i class="fas fa-play"></i> Run Normalization
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Backup Management -->
        <div class="col-md-6">
            <div class="tool-card">
                <div class="text-center">
                    <i class="fas fa-download tool-icon text-warning"></i>
                    <h4>Backup Management</h4>
                    <p class="text-muted">Create and manage database backups before making changes</p>
                </div>
                
                <div class="mb-3">
                    <span class="status-indicator status-warning"></span>
                    <strong>Status:</strong> Backup Recommended
                </div>
                
                <div class="text-center">
                    <button class="btn btn-warning" onclick="createBackup()">
                        <i class="fas fa-download"></i> Create Backup
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Current Status Summary -->
    <div class="row">
        <div class="col-12">
            <div class="tool-card">
                <h4><i class="fas fa-chart-pie"></i> Current Database Status</h4>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <h5 class="text-primary"><?php echo $hasOriginalTables ? 'Yes' : 'No'; ?></h5>
                            <small class="text-muted">Original Tables</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <h5 class="text-success"><?php echo $hasNormalizedTables ? 'Yes' : 'No'; ?></h5>
                            <small class="text-muted">Normalized Tables</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <h5 class="text-info"><?php echo $migrationComplete ? 'Complete' : 'Pending'; ?></h5>
                            <small class="text-muted">Data Migration</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <h5 class="text-warning"><?php echo count($normalizedTables ?? []); ?></h5>
                            <small class="text-muted">Normalized Tables Count</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="tool-card">
                <h4><i class="fas fa-bolt"></i> Quick Actions</h4>
                
                <div class="btn-group-vertical w-100" role="group">
                    <a href="analyze_database_structure.php" class="btn btn-outline-primary mb-2">
                        <i class="fas fa-search"></i> Quick Database Analysis
                    </a>
                    <a href="normalization_report.php" class="btn btn-outline-info mb-2">
                        <i class="fas fa-file-alt"></i> View Normalization Benefits
                    </a>
                    <a href="normalize_database.php" class="btn btn-outline-success mb-2">
                        <i class="fas fa-play"></i> Start Normalization Process
                    </a>
                    <button class="btn btn-outline-warning" onclick="showBackupInfo()">
                        <i class="fas fa-info-circle"></i> Backup Information
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function createBackup() {
    if (confirm('This will create a backup of your current database. Continue?')) {
        // In a real implementation, this would trigger a backup process
        alert('Backup functionality would be implemented here.\n\nFor now, the normalization process automatically creates backups.');
    }
}

function showBackupInfo() {
    alert('Backup Information:\n\n' +
          '• Automatic backups are created before normalization\n' +
          '• Backups are stored in the database/ directory\n' +
          '• Backup files include timestamp for easy identification\n' +
          '• All table structures and data are preserved\n\n' +
          'The normalization process is safe and reversible.');
}
</script>

<?php require_once 'includes/footer.php'; ?>
