<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit activities for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get activity questions
$questions = getActivityQuestions($activityId);
if (is_string($questions)) {
    $questions = [];
}

// Initialize variables
$title = $activity['title'];
$description = $activity['description'];
$activityType = $activity['activity_type'];
$points = $activity['points'];
$dueDate = $activity['due_date'];
$isPublished = $activity['is_published'];
$title_err = $description_err = $points_err = $dueDate_err = "";
$success = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_activity'])) {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title for the activity.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description for the activity.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Validate points
    if ($activityType == "assignment" || $activityType == "quiz" || $activityType == "activity") {
        if (!isset($_POST["points"]) || !is_numeric($_POST["points"]) || $_POST["points"] < 0) {
            $points_err = "Please enter a valid number of points.";
        } else {
            $points = intval($_POST["points"]);
        }
    }

    // Validate due date
    if ($activityType == "assignment" || $activityType == "quiz" || $activityType == "activity") {
        if (empty($_POST["due_date"])) {
            // Due date is optional
            $dueDate = null;
        } else {
            // Format the date for MySQL
            $dueDate = date("Y-m-d H:i:s", strtotime($_POST["due_date"]));
        }
    }

    // Check if the activity should be published
    $isPublished = isset($_POST["is_published"]) ? true : false;

    // Check input errors before updating the activity
    if (empty($title_err) && empty($description_err) && empty($points_err) && empty($dueDate_err)) {
        // Update the activity
        $result = updateActivity($activityId, $title, $description, $points, $dueDate, $isPublished);

        if ($result === true) {
            // Activity updated successfully
            $success = "Activity updated successfully!";

            // Refresh activity data
            $activity = getActivityById($activityId);
            if (is_string($activity)) {
                $_SESSION['error'] = $activity;
                header("location: index.php");
                exit;
            }
        } else {
            // Error occurred
            $error = $result;
        }
    }
}

// Set page title
$page_title = "Edit Activity - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($activity['title']); ?></a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Activity</h1>
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <a href="#questions-section" class="btn btn-primary" onclick="showQuestionsSection()">
                    <i class="fas fa-cog"></i> Manage Questions
                </a>
                <?php endif; ?>
            </div>

            <!-- Debug information -->
            <div class="alert alert-info">
                <p><strong>Activity Type:</strong> <?php echo ucfirst($activityType); ?></p>
                <p><strong>Activity ID:</strong> <?php echo $activityId; ?></p>
                <p><strong>Should show Questions section:</strong> <?php echo ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz') ? 'Yes' : 'No'; ?></p>
                <p><a href="update_activity_type.php?id=<?php echo $activityId; ?>" class="btn btn-sm btn-warning">Change Activity Type</a></p>
            </div>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <!-- Activity Details Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Activity Details</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post">
                        <div class="form-group">
                            <label for="title">Title <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                            <span class="invalid-feedback"><?php echo $title_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea name="description" id="description" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                            <span class="invalid-feedback"><?php echo $description_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label>Activity Type</label>
                            <div class="form-control-plaintext"><?php echo ucfirst($activityType); ?></div>
                            <small class="form-text text-muted">Activity type cannot be changed after creation.</small>
                        </div>

                        <?php if ($activityType == "assignment" || $activityType == "quiz" || $activityType == "activity"): ?>
                        <div class="form-group">
                            <label for="points">Points</label>
                            <input type="number" name="points" id="points" class="form-control <?php echo (!empty($points_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $points; ?>" min="0">
                            <span class="invalid-feedback"><?php echo $points_err; ?></span>
                            <small class="form-text text-muted">Enter 0 for ungraded activities.</small>
                        </div>

                        <div class="form-group">
                            <label for="due_date">Due Date (optional)</label>
                            <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo !empty($dueDate) ? date('Y-m-d\TH:i', strtotime($dueDate)) : ''; ?>">
                            <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                            <small class="form-text text-muted">Leave blank if there is no due date.</small>
                        </div>
                        <?php endif; ?>



                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_published">Publish this activity (visible to students)</label>
                            </div>
                        </div>

                        <div class="form-group text-right">
                            <input type="hidden" name="update_activity" value="1">
                            <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-secondary mr-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Activity</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Questions Section -->
            <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
            <div id="questions-section" class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Questions</h5>
                    <button type="button" class="btn btn-light" onclick="toggleAddQuestionForm()">
                        <i class="fas fa-plus"></i> Add New Question
                    </button>
                </div>

                <div id="addQuestionForm" style="display: none;">
                    <div class="card-body border-bottom">
                        <form action="activity_edit.php?id=<?php echo $activityId; ?>" method="post">
                            <div class="form-group">
                                <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                <textarea name="question_text" id="question_text" class="form-control" rows="3" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="question_type">Type:</label>
                                <select name="question_type" id="question_type" class="form-control" onchange="toggleQuestionOptions()">
                                    <option value="multiple_choice">Multiple Choice</option>
                                    <option value="true_false">True/False</option>
                                    <option value="short_answer">Short Answer</option>
                                </select>
                            </div>

                            <!-- Multiple Choice Options -->
                            <div id="multiple_choice_options" class="question-options">
                                <div class="form-group">
                                    <label for="option_a">Option A:</label>
                                    <input type="text" name="option_a" id="option_a" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="option_b">Option B:</label>
                                    <input type="text" name="option_b" id="option_b" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="option_c">Option C:</label>
                                    <input type="text" name="option_c" id="option_c" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="option_d">Option D:</label>
                                    <input type="text" name="option_d" id="option_d" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="mc_correct_answer">Correct Answer:</label>
                                    <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                        <option value="">-- Select --</option>
                                        <option value="A">Option A</option>
                                        <option value="B">Option B</option>
                                        <option value="C">Option C</option>
                                        <option value="D">Option D</option>
                                    </select>
                                </div>
                            </div>

                            <!-- True/False Options -->
                            <div id="true_false_options" class="question-options" style="display: none;">
                                <div class="form-group">
                                    <label for="tf_correct_answer">Correct Answer:</label>
                                    <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                        <option value="">-- Select --</option>
                                        <option value="true">True</option>
                                        <option value="false">False</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Short Answer Options -->
                            <div id="short_answer_options" class="question-options" style="display: none;">
                                <div class="form-group">
                                    <label for="sa_correct_answer">Correct Answer:</label>
                                    <input type="text" name="sa_correct_answer" id="sa_correct_answer" class="form-control">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="question_points">Points <span class="text-danger">*</span></label>
                                <input type="number" name="question_points" id="question_points" class="form-control" value="1" min="1" required>
                            </div>

                            <div class="form-group text-right">
                                <button type="button" class="btn btn-secondary mr-2" onclick="toggleAddQuestionForm()">Cancel</button>
                                <input type="hidden" name="add_question" value="1">
                                <button type="submit" class="btn btn-success">Add Question</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="list-group list-group-flush">
                    <?php if (count($questions) > 0): ?>
                        <?php foreach ($questions as $index => $question): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                <span class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                            </div>
                            <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                            <?php if (in_array($question['question_type'], ['multiple_choice', 'true_false']) && isset($question['options'])): ?>
                            <div class="options-list ml-4 mb-3">
                                <?php foreach ($question['options'] as $option): ?>
                                <div class="option <?php echo $option['is_correct'] ? 'text-success font-weight-bold' : ''; ?>">
                                    <?php
                                    if ($question['question_type'] == 'multiple_choice') {
                                        echo '<i class="far fa-circle mr-2"></i>';
                                    } elseif ($question['question_type'] == 'true_false') {
                                        echo '<i class="far fa-square mr-2"></i>';
                                    }
                                    ?>
                                    <?php echo htmlspecialchars($option['option_text']); ?>
                                    <?php if ($option['is_correct']): ?>
                                    <i class="fas fa-check text-success ml-2"></i>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>

                            <div class="btn-group">
                                <a href="activity_question_edit.php?id=<?php echo $question['question_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="activity_edit.php?id=<?php echo $activityId; ?>&delete_question=<?php echo $question['question_id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this question?');">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="list-group-item text-center py-4">
                            <p class="text-muted mb-0">No questions added yet. Add questions above.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Question styling */
.list-group-item {
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.option-container {
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-container:hover {
    background-color: #f0f0f0;
}

.card-header.bg-primary {
    background-color: #007bff !important;
}
</style>

<script>
function toggleQuestionOptions() {
    var questionType = document.getElementById('question_type').value;
    var multipleChoiceOptions = document.getElementById('multiple_choice_options');
    var trueFalseOptions = document.getElementById('true_false_options');
    var shortAnswerOptions = document.getElementById('short_answer_options');

    // Hide all options first
    multipleChoiceOptions.style.display = 'none';
    trueFalseOptions.style.display = 'none';
    shortAnswerOptions.style.display = 'none';

    // Show the appropriate options based on question type
    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
        // Make option A and B required for multiple choice
        document.getElementById('option_a').setAttribute('required', 'required');
        document.getElementById('option_b').setAttribute('required', 'required');
        document.getElementById('mc_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'true_false') {
        trueFalseOptions.style.display = 'block';
        document.getElementById('tf_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'short_answer') {
        shortAnswerOptions.style.display = 'block';
        document.getElementById('sa_correct_answer').setAttribute('required', 'required');
    }
}

function toggleAddQuestionForm() {
    var form = document.getElementById('addQuestionForm');
    if (form.style.display === 'none') {
        form.style.display = 'block';
    } else {
        form.style.display = 'none';
    }
}

function showQuestionsSection() {
    // Scroll to the questions section
    document.getElementById('questions-section').scrollIntoView({ behavior: 'smooth' });

    // Show the add question form
    document.getElementById('addQuestionForm').style.display = 'block';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the question options
    if (document.getElementById('question_type')) {
        toggleQuestionOptions();
    }

    // Check if we should show the questions form based on URL
    if (window.location.href.indexOf('manage_questions=1') > -1) {
        console.log('URL contains manage_questions parameter');

        // Show the questions section
        setTimeout(function() {
            showQuestionsSection();
        }, 200);
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
