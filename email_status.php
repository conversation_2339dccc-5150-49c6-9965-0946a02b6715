<?php
/**
 * Email Status Dashboard
 * Shows email configuration status and recent email logs
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

// Get email configuration status
require_once 'includes/mailer_config.php';

$configStatus = [
    'smtp_configured' => !empty(SMTP_HOST) && !empty(SMTP_USERNAME),
    'smtp_host' => SMTP_HOST,
    'smtp_port' => SMTP_PORT,
    'smtp_username' => SMTP_USERNAME,
    'smtp_secure' => SMTP_SECURE,
    'from_email' => defined('MAIL_FROM_EMAIL') ? MAIL_FROM_EMAIL : '',
    'from_name' => defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : ''
];

// Test SMTP connection if configured
$connectionStatus = null;
if ($configStatus['smtp_configured']) {
    require_once 'includes/phpmailer_wrapper.php';
    try {
        $mailer = new PHPMailerWrapper();
        $connectionStatus = $mailer->testConnection();
        $connectionError = $connectionStatus ? null : $mailer->getError();
    } catch (Exception $e) {
        $connectionStatus = false;
        $connectionError = $e->getMessage();
    }
}

// Read recent email logs
$emailLogs = [];
$logFile = __DIR__ . '/logs/email_log.txt';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $logLines = explode("\n", $logContent);
    
    // Parse recent log entries (last 10)
    $recentLines = array_slice(array_reverse($logLines), 0, 20);
    foreach ($recentLines as $line) {
        if (preg_match('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| TO: (.+?) \| SUBJECT: (.+?) \| STATUS: (.+)/', $line, $matches)) {
            $emailLogs[] = [
                'timestamp' => $matches[1],
                'to' => $matches[2],
                'subject' => $matches[3],
                'status' => $matches[4]
            ];
        }
    }
}

$page_title = "Email Status";
require_once 'includes/header.php';
?>

<style>
    .status-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-success { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-danger { background-color: #dc3545; }
    
    .log-entry {
        border-left: 4px solid #e9ecef;
        padding: 10px 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 5px 5px 0;
    }
    
    .log-entry.success { border-left-color: #28a745; }
    .log-entry.error { border-left-color: #dc3545; }
    .log-entry.warning { border-left-color: #ffc107; }
    
    .config-table th {
        width: 30%;
        background: #f8f9fa;
    }
</style>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-envelope-open-text"></i> Email System Status</h1>
        <div>
            <a href="email_settings.php" class="btn btn-primary">
                <i class="fas fa-cog"></i> Configure Email
            </a>
            <a href="test_email.php" class="btn btn-secondary ml-2">
                <i class="fas fa-paper-plane"></i> Test Email
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Configuration Status -->
        <div class="col-md-6">
            <div class="card status-card">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> Configuration Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="status-indicator <?php echo $configStatus['smtp_configured'] ? 'status-success' : 'status-danger'; ?>"></span>
                        <strong>SMTP Configuration:</strong>
                        <?php echo $configStatus['smtp_configured'] ? 'Configured' : 'Not Configured'; ?>
                    </div>
                    
                    <?php if ($configStatus['smtp_configured']): ?>
                    <div class="mb-3">
                        <span class="status-indicator <?php echo $connectionStatus === true ? 'status-success' : ($connectionStatus === false ? 'status-danger' : 'status-warning'); ?>"></span>
                        <strong>SMTP Connection:</strong>
                        <?php 
                        if ($connectionStatus === true) {
                            echo 'Connected';
                        } elseif ($connectionStatus === false) {
                            echo 'Failed';
                        } else {
                            echo 'Not Tested';
                        }
                        ?>
                    </div>
                    <?php endif; ?>
                    
                    <table class="table table-sm config-table">
                        <tr>
                            <th>SMTP Host:</th>
                            <td><?php echo htmlspecialchars($configStatus['smtp_host'] ?: 'Not set'); ?></td>
                        </tr>
                        <tr>
                            <th>SMTP Port:</th>
                            <td><?php echo htmlspecialchars($configStatus['smtp_port'] ?: 'Not set'); ?></td>
                        </tr>
                        <tr>
                            <th>Security:</th>
                            <td><?php echo htmlspecialchars($configStatus['smtp_secure'] ?: 'None'); ?></td>
                        </tr>
                        <tr>
                            <th>Username:</th>
                            <td><?php echo htmlspecialchars($configStatus['smtp_username'] ?: 'Not set'); ?></td>
                        </tr>
                        <tr>
                            <th>From Email:</th>
                            <td><?php echo htmlspecialchars($configStatus['from_email'] ?: 'Not set'); ?></td>
                        </tr>
                        <tr>
                            <th>From Name:</th>
                            <td><?php echo htmlspecialchars($configStatus['from_name'] ?: 'Not set'); ?></td>
                        </tr>
                    </table>
                    
                    <?php if ($connectionStatus === false && isset($connectionError)): ?>
                    <div class="alert alert-danger mt-3">
                        <strong>Connection Error:</strong><br>
                        <?php echo htmlspecialchars($connectionError); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Email Activity -->
        <div class="col-md-6">
            <div class="card status-card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> Recent Email Activity</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($emailLogs)): ?>
                        <p class="text-muted">No recent email activity found.</p>
                    <?php else: ?>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <?php foreach ($emailLogs as $log): ?>
                            <div class="log-entry <?php echo strpos($log['status'], 'ERROR') !== false ? 'error' : (strpos($log['status'], 'SUCCESS') !== false ? 'success' : 'warning'); ?>">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted"><?php echo htmlspecialchars($log['timestamp']); ?></small>
                                    <span class="badge badge-<?php echo strpos($log['status'], 'ERROR') !== false ? 'danger' : (strpos($log['status'], 'SUCCESS') !== false ? 'success' : 'warning'); ?>">
                                        <?php echo strpos($log['status'], 'ERROR') !== false ? 'Failed' : (strpos($log['status'], 'SUCCESS') !== false ? 'Sent' : 'Logged'); ?>
                                    </span>
                                </div>
                                <div><strong>To:</strong> <?php echo htmlspecialchars($log['to']); ?></div>
                                <div><strong>Subject:</strong> <?php echo htmlspecialchars($log['subject']); ?></div>
                                <?php if (strpos($log['status'], 'ERROR') !== false): ?>
                                <div class="mt-1"><small class="text-danger"><?php echo htmlspecialchars($log['status']); ?></small></div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Setup Options -->
    <div class="row">
        <div class="col-12">
            <div class="card status-card">
                <div class="card-header">
                    <h5><i class="fas fa-rocket"></i> Quick Setup Options</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fab fa-google fa-3x text-danger mb-3"></i>
                                <h6>Gmail Setup</h6>
                                <p class="text-muted">Quick setup for Gmail accounts</p>
                                <a href="setup_gmail.php" class="btn btn-outline-danger">
                                    <i class="fab fa-google"></i> Setup Gmail
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                                <h6>Manual Setup</h6>
                                <p class="text-muted">Configure custom SMTP settings</p>
                                <a href="email_settings.php" class="btn btn-outline-primary">
                                    <i class="fas fa-cog"></i> Manual Setup
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-paper-plane fa-3x text-success mb-3"></i>
                                <h6>Test Email</h6>
                                <p class="text-muted">Send a test email to verify setup</p>
                                <a href="test_email.php" class="btn btn-outline-success">
                                    <i class="fas fa-paper-plane"></i> Test Email
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
