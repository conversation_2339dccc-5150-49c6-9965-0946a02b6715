<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to create activities.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to create activities for this course
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to create activities for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Initialize variables
$title = $description = "";
$activityType = "activity"; // Fixed to activity
$points = 0; // Points will be set per question later
$dueDate = "";
$isPublished = true;
$moduleId = 0; // Default to no module
$title_err = $description_err = $dueDate_err = "";
$success = "";

// No need to get modules since we're not showing the module selection

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title for the activity.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description for the activity.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Activity type is fixed to "activity"
    $activityType = "activity";

    // Points will be set per question later
    $points = 0; // Default to 0

    // Validate due date
    if (empty($_POST["due_date"])) {
        // Due date is optional
        $dueDate = null;
    } else {
        // Format the date for MySQL
        $dueDate = date("Y-m-d H:i:s", strtotime($_POST["due_date"]));
    }

    // Check if late submissions are allowed
    $allowLateSubmissions = isset($_POST["allow_late_submissions"]) ? true : false;

    // Check if the activity should be published
    $isPublished = isset($_POST["is_published"]) ? true : false;

    // Module ID is always 0 (no module)
    $moduleId = 0;

    // File uploads have been removed

    // Check input errors before creating the activity
    if (empty($title_err) && empty($description_err) && empty($dueDate_err)) {
        // Create the activity
        $result = createActivity($courseId, $title, $description, $activityType, $points, $dueDate, $isPublished, $_SESSION['user_id'], $allowLateSubmissions, null);

        if (is_numeric($result)) {
            // Activity created successfully
            $activityId = $result;

            // No module handling needed

            // Redirect to the activity edit page
            $_SESSION['success'] = "Activity created successfully!";
            header("location: activity_edit.php?id=$activityId");
            exit;
        } else {
            // Error occurred
            $error = $result;
        }
    }
}

// Set page title
$page_title = "Create Activity";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Activity</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create an activity with multiple choice, true/false, and short answer questions for students to complete.</p>
        </div>
    </div>
</div>

<!-- Activity form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>

            <div class="form-group">
                <label for="description">Instructions</label>
                <textarea name="description" id="description" rows="6" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>"><?php echo htmlspecialchars($description); ?></textarea>
                <span class="invalid-feedback"><?php echo $description_err; ?></span>
            </div>

            <!-- Activity type is fixed to "activity" -->
            <input type="hidden" name="activity_type" value="activity">

            <!-- Points will be set per question later -->

            <div id="due-date-group" class="form-group">
                <label for="due_date">Due Date (optional)</label>
                <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo !empty($dueDate) ? date('Y-m-d\TH:i', strtotime($dueDate)) : ''; ?>">
                <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                <small class="form-text text-muted">If no due date is set, the activity will be available indefinitely.</small>
            </div>

            <div id="late-submissions-group" class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" <?php echo isset($allowLateSubmissions) && $allowLateSubmissions ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                    <small class="form-text text-muted">If checked, students can submit after the due date, but submissions will be marked as late.</small>
                </div>
            </div>



            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="is_published">Publish this activity (visible to students)</label>
                </div>
            </div>

            <div class="form-group">
                <label>Create Questions</label>
                <div class="alert alert-info">
                    <p class="mb-0">After creating this activity, you'll be able to add questions of the following types:</p>
                    <ul class="mt-2 mb-0">
                        <li><strong>Multiple Choice</strong> - Students select one correct answer from several options</li>
                        <li><strong>True/False</strong> - Students select whether a statement is true or false</li>
                        <li><strong>Short Answer</strong> - Students type in a brief text response</li>
                    </ul>
                    <p class="mt-2 mb-0"><strong>Note:</strong> You'll be able to set points for each question individually after creating the activity.</p>
                </div>
                <small class="form-text text-muted mt-2">You'll be redirected to the question editor after creating this activity.</small>
            </div>

            <!-- No module field needed -->

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Create Activity</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for form fields -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // No need for file input handling or field toggling
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
