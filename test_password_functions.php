<?php
// Test PHP password functions
echo "<h2>PHP Password Functions Test</h2>";

// Test password
$password = 'admin123';
echo "Test password: $password<br><br>";

// Test password_hash function
echo "<h3>Testing password_hash function:</h3>";
$hash1 = password_hash($password, PASSWORD_DEFAULT);
echo "Hash 1: $hash1<br>";

$hash2 = password_hash($password, PASSWORD_DEFAULT);
echo "Hash 2: $hash2<br>";

echo "Are the hashes different? " . ($hash1 !== $hash2 ? "Yes (expected)" : "No (unexpected)") . "<br><br>";

// Test password_verify function
echo "<h3>Testing password_verify function:</h3>";
echo "Verifying password against Hash 1: " . (password_verify($password, $hash1) ? "Success" : "Failed") . "<br>";
echo "Verifying password against Hash 2: " . (password_verify($password, $hash2) ? "Success" : "Failed") . "<br>";
echo "Verifying wrong password against Hash 1: " . (password_verify('wrongpassword', $hash1) ? "Success (unexpected)" : "Failed (expected)") . "<br><br>";

// Test with a known hash format
echo "<h3>Testing with a known hash format:</h3>";
$knownHash = '$2y$10$8MNE5ERTjmVE1nFH.vFkLuQK/JYZCXsEzk3Y9g6R.7ewH1Svv5cce'; // This is a hash of 'admin123'
echo "Known hash: $knownHash<br>";
echo "Verifying 'admin123' against known hash: " . (password_verify('admin123', $knownHash) ? "Success" : "Failed") . "<br>";

// Get PHP version and hash info
echo "<h3>PHP Environment Information:</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Password hash info: <pre>" . print_r(password_get_info($hash1), true) . "</pre>";

// Test with different algorithms if available
echo "<h3>Testing different hashing algorithms:</h3>";
if (defined('PASSWORD_BCRYPT')) {
    $bcryptHash = password_hash($password, PASSWORD_BCRYPT);
    echo "BCRYPT hash: $bcryptHash<br>";
    echo "Verifying password against BCRYPT hash: " . (password_verify($password, $bcryptHash) ? "Success" : "Failed") . "<br>";
}

if (defined('PASSWORD_ARGON2I')) {
    $argon2iHash = password_hash($password, PASSWORD_ARGON2I);
    echo "ARGON2I hash: $argon2iHash<br>";
    echo "Verifying password against ARGON2I hash: " . (password_verify($password, $argon2iHash) ? "Success" : "Failed") . "<br>";
}

if (defined('PASSWORD_ARGON2ID')) {
    $argon2idHash = password_hash($password, PASSWORD_ARGON2ID);
    echo "ARGON2ID hash: $argon2idHash<br>";
    echo "Verifying password against ARGON2ID hash: " . (password_verify($password, $argon2idHash) ? "Success" : "Failed") . "<br>";
}
?>
