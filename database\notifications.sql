-- Notifications Table
CREATE TABLE IF NOT EXISTS notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    related_id INT,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- User Notification Settings Table
CREATE TABLE IF NOT EXISTS user_notification_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_notifications TINYINT(1) DEFAULT 1,
    event_creation TINYINT(1) DEFAULT 1,
    event_updates TINYINT(1) DEFAULT 1,
    event_reminders TINYINT(1) DEFAULT 1,
    status_changes TINYINT(1) DEFAULT 1,
    system_announcements TINYINT(1) DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Insert default settings for existing users
INSERT INTO user_notification_settings (user_id, email_notifications, event_creation, event_updates, event_reminders, status_changes, system_announcements)
SELECT user_id, 1, 1, 1, 1, 1, 1 FROM users
WHERE NOT EXISTS (SELECT 1 FROM user_notification_settings WHERE user_notification_settings.user_id = users.user_id);
