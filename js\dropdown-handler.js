/**
 * Dropdown handler for Bootstrap dropdowns
 * This script ensures that dropdown menus work properly in the e-learning system
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all Bootstrap dropdowns
    if (typeof $ !== 'undefined' && typeof $.fn.dropdown !== 'undefined') {
        $('.dropdown-toggle').dropdown();
    }

    // Fix for dropdown menu positioning
    $('.btn-group, .dropdown').on('show.bs.dropdown', function () {
        // Get the dropdown menu
        const $menu = $(this).find('.dropdown-menu');
        const $button = $(this).find('.dropdown-toggle');
        
        // Calculate position
        const buttonOffset = $button.offset();
        const buttonHeight = $button.outerHeight();
        
        // Ensure the dropdown menu is properly positioned
        $menu.css({
            'display': 'block',
            'position': 'absolute',
            'top': buttonHeight + 'px',
            'left': '0px',
            'right': 'auto',
            'z-index': '1050',
            'will-change': 'transform'
        });
        
        // If dropdown would go off the right edge of the screen, align it to the right
        const menuWidth = $menu.outerWidth();
        const buttonWidth = $button.outerWidth();
        const windowWidth = $(window).width();
        
        if (buttonOffset.left + menuWidth > windowWidth) {
            $menu.css({
                'left': 'auto',
                'right': '0px'
            });
        }
    });
    
    // Close dropdowns when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown-toggle').length && 
            !$(e.target).closest('.dropdown-menu').length) {
            $('.dropdown-menu.show').removeClass('show');
            $('.btn-group.show, .dropdown.show').removeClass('show');
        }
    });
    
    // Prevent dropdown from closing when clicking inside it
    $('.dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });
    
    // Make sure dropdown items work properly
    $('.dropdown-item').on('click', function() {
        const $dropdown = $(this).closest('.dropdown, .btn-group');
        setTimeout(function() {
            $dropdown.find('.dropdown-menu').removeClass('show');
            $dropdown.removeClass('show');
        }, 100);
    });
});
