<?php
/**
 * Fallback Mailer for Development
 * 
 * This mailer is used when <PERSON><PERSON><PERSON><PERSON><PERSON> fails or in development environments
 * where SMTP is not available. It logs emails instead of sending them.
 */

class FallbackMailer {
    private $error;
    
    /**
     * Send an email (or log it in development mode)
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @return bool Always returns true in development mode
     */
    public function sendEmail($to, $subject, $message, $isHtml = false) {
        // In development mode, just log the email
        if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
            $this->logEmail($to, $subject, $message, 'DEVELOPMENT_LOG');
            return true;
        }
        
        // Try PHP's built-in mail() function as fallback
        try {
            $headers = "From: " . (defined('MAIL_FROM_EMAIL') ? MAIL_FROM_EMAIL : 'noreply@localhost') . "\r\n";
            $headers .= "Reply-To: " . (defined('MAIL_FROM_EMAIL') ? MAIL_FROM_EMAIL : 'noreply@localhost') . "\r\n";
            $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
            
            if ($isHtml) {
                $headers .= "MIME-Version: 1.0\r\n";
                $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            } else {
                $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
            }
            
            $result = @mail($to, $subject, $message, $headers);
            
            if ($result) {
                $this->logEmail($to, $subject, $message, 'SENT_VIA_MAIL_FUNCTION');
                return true;
            } else {
                $this->error = "PHP mail() function failed";
                $this->logEmail($to, $subject, $message, 'FAILED_MAIL_FUNCTION');
                return false;
            }
            
        } catch (Exception $e) {
            $this->error = "Fallback mailer error: " . $e->getMessage();
            $this->logEmail($to, $subject, $message, 'ERROR: ' . $this->error);
            return false;
        }
    }
    
    /**
     * Get the last error message
     * 
     * @return string Error message
     */
    public function getError() {
        return $this->error;
    }
    
    /**
     * Log email for debugging purposes
     * 
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message
     * @param string $status Status message
     */
    private function logEmail($to, $subject, $message, $status) {
        $logDir = __DIR__ . '/../logs';
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/email_log.txt';
        $logEntry = "\n" . str_repeat("=", 80) . "\n";
        $logEntry .= "TIMESTAMP: " . date('Y-m-d H:i:s') . "\n";
        $logEntry .= "STATUS: " . $status . "\n";
        $logEntry .= "TO: " . $to . "\n";
        $logEntry .= "SUBJECT: " . $subject . "\n";
        $logEntry .= "MESSAGE:\n" . $message . "\n";
        $logEntry .= str_repeat("=", 80) . "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Static method to send email easily
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message
     * @param bool $isHtml Whether the message is HTML
     * @return bool True if email was sent successfully, false otherwise
     */
    public static function send($to, $subject, $message, $isHtml = false) {
        $mailer = new self();
        return $mailer->sendEmail($to, $subject, $message, $isHtml);
    }
}
?>
