<?php
// Test database connection
echo "<h2>Database Connection Test</h2>";

// Database credentials
$db_server = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'maxcel_elearning';

echo "Connecting to database: $db_name on server: $db_server<br>";

try {
    $pdo = new PDO("mysql:host=$db_server;dbname=$db_name", $db_username, $db_password);
    
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Database connection successful!<br>";
    
    // Test query to get users
    $stmt = $pdo->query("SELECT user_id, username, email, first_name, last_name FROM users");
    $users = $stmt->fetchAll();
    
    echo "<h3>Users in Database:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>First Name</th><th>Last Name</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['user_id'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . $user['first_name'] . "</td>";
        echo "<td>" . $user['last_name'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Test query to get roles
    $stmt = $pdo->query("SELECT * FROM roles");
    $roles = $stmt->fetchAll();
    
    echo "<h3>Roles in Database:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Role Name</th><th>Description</th></tr>";
    
    foreach ($roles as $role) {
        echo "<tr>";
        echo "<td>" . $role['role_id'] . "</td>";
        echo "<td>" . $role['role_name'] . "</td>";
        echo "<td>" . $role['description'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>
