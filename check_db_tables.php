<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';
require_once 'includes/quiz_functions.php';

// Function to display table structure
function displayTableStructure($pdo, $tableName) {
    echo "<h3>Table Structure: $tableName</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE $tableName");
        
        if ($stmt) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] === null ? 'NULL' : $row['Default']) . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>Error: Unable to get table structure.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}

// Function to display table data
function displayTableData($pdo, $tableName, $limit = 10) {
    echo "<h3>Table Data: $tableName (Limited to $limit rows)</h3>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM $tableName LIMIT $limit");
        
        if ($stmt) {
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($rows) > 0) {
                echo "<table border='1' cellpadding='5'>";
                
                // Table header
                echo "<tr>";
                foreach (array_keys($rows[0]) as $column) {
                    echo "<th>" . htmlspecialchars($column) . "</th>";
                }
                echo "</tr>";
                
                // Table data
                foreach ($rows as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . htmlspecialchars($value !== null ? $value : 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No data found in table.</p>";
            }
        } else {
            echo "<p>Error: Unable to get table data.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}

// Function to test adding a quiz question
function testAddQuizQuestion($pdo, $activityId) {
    echo "<h3>Testing addQuizActivityQuestion Function</h3>";
    
    $questionText = "Test question added at " . date('Y-m-d H:i:s');
    $questionType = "multiple_choice";
    $points = 5;
    
    echo "<p>Attempting to add question to activity ID: $activityId</p>";
    echo "<p>Question Text: " . htmlspecialchars($questionText) . "</p>";
    echo "<p>Question Type: $questionType</p>";
    echo "<p>Points: $points</p>";
    
    try {
        $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);
        
        if (is_numeric($result)) {
            echo "<p style='color:green;'>Success! Question added with ID: $result</p>";
            
            // Add some options
            $optionA = "Test option A";
            $optionB = "Test option B";
            
            $optionResultA = addQuizOption($result, $optionA, true);
            $optionResultB = addQuizOption($result, $optionB, false);
            
            echo "<p>Option A Result: " . (is_numeric($optionResultA) ? "Success (ID: $optionResultA)" : $optionResultA) . "</p>";
            echo "<p>Option B Result: " . (is_numeric($optionResultB) ? "Success (ID: $optionResultB)" : $optionResultB) . "</p>";
            
            return $result;
        } else {
            echo "<p style='color:red;'>Error: $result</p>";
            return false;
        }
    } catch (Exception $e) {
        echo "<p style='color:red;'>Exception: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Main content
echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Tables Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; margin-bottom: 20px; }
        th { background-color: #f2f2f2; }
        td, th { padding: 8px; text-align: left; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Database Tables Check</h1>";

// Get database name
try {
    $stmt = $pdo->query("SELECT DATABASE()");
    $dbName = $stmt->fetchColumn();
    echo "<h2>Current Database: $dbName</h2>";
} catch (PDOException $e) {
    echo "<p class='error'>Error getting database name: " . $e->getMessage() . "</p>";
}

// List all tables
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Tables in Database:</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check if quiz_questions table exists
    if (in_array('quiz_questions', $tables)) {
        echo "<p class='success'>quiz_questions table exists!</p>";
        displayTableStructure($pdo, 'quiz_questions');
        displayTableData($pdo, 'quiz_questions');
    } else {
        echo "<p class='error'>quiz_questions table does not exist!</p>";
    }
    
    // Check if quiz_options table exists
    if (in_array('quiz_options', $tables)) {
        echo "<p class='success'>quiz_options table exists!</p>";
        displayTableStructure($pdo, 'quiz_options');
        displayTableData($pdo, 'quiz_options');
    } else {
        echo "<p class='error'>quiz_options table does not exist!</p>";
    }
    
    // Test adding a quiz question
    $testQuestionId = testAddQuizQuestion($pdo, 7); // Use quiz ID 7
    
    if ($testQuestionId) {
        echo "<h3>Verifying Question Added</h3>";
        $stmt = $pdo->prepare("SELECT * FROM quiz_questions WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $testQuestionId);
        $stmt->execute();
        $question = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($question) {
            echo "<p class='success'>Question found in database!</p>";
            echo "<pre>" . print_r($question, true) . "</pre>";
            
            // Check options
            $stmt = $pdo->prepare("SELECT * FROM quiz_options WHERE question_id = :questionId");
            $stmt->bindParam(':questionId', $testQuestionId);
            $stmt->execute();
            $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($options) > 0) {
                echo "<p class='success'>Options found for question!</p>";
                echo "<pre>" . print_r($options, true) . "</pre>";
            } else {
                echo "<p class='error'>No options found for question!</p>";
            }
        } else {
            echo "<p class='error'>Question not found in database!</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
