<?php
// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "<br>");
}

echo "<h1>Direct Database Fix Script</h1>";

// Try to drop the role_id column directly
$sql = "ALTER TABLE users DROP COLUMN role_id";
if ($conn->query($sql) === TRUE) {
    echo "Successfully dropped role_id column<br>";
} else {
    echo "Error dropping role_id column: " . $conn->error . "<br>";
}

// Show the current table structure
$result = $conn->query("SHOW COLUMNS FROM users");
if ($result) {
    echo "<h2>Current Users Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

echo "<p>Database fix complete. You can now <a href='register.php'>register</a> or <a href='login.php'>login</a>.</p>";

$conn->close();
?>
