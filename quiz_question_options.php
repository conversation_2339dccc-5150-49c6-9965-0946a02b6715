<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to manage question options.";
    header("location: index.php");
    exit;
}

// Check if question ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$questionId = intval($_GET['id']);

// Get question details
$stmt = $pdo->prepare("
    SELECT q.*, a.activity_id, a.title as activity_title, a.course_id
    FROM quiz_questions q
    JOIN activities a ON q.activity_id = a.activity_id
    WHERE q.question_id = :questionId
");
$stmt->bindParam(':questionId', $questionId);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    $_SESSION['error'] = "Question not found.";
    header("location: index.php");
    exit;
}

$question = $stmt->fetch();
$activityId = $question['activity_id'];
$courseId = $question['course_id'];

// Check if question type supports options
if ($question['question_type'] != 'multiple_choice' && $question['question_type'] != 'true_false') {
    $_SESSION['error'] = "This question type does not support options.";
    header("location: quiz_edit.php?id=$activityId");
    exit;
}

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this question
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to manage options for this question.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get existing options
$stmt = $pdo->prepare("
    SELECT * FROM quiz_options
    WHERE question_id = :questionId
    ORDER BY position, option_id
");
$stmt->bindParam(':questionId', $questionId);
$stmt->execute();
$options = $stmt->fetchAll();

// Process option addition
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_option'])) {
    $optionText = trim($_POST['option_text']);
    $isCorrect = isset($_POST['is_correct']) ? true : false;
    
    if (!empty($optionText)) {
        $result = addQuizOption($questionId, $optionText, $isCorrect);
        
        if (is_numeric($result)) {
            $_SESSION['success'] = "Option added successfully!";
            header("location: quiz_question_options.php?id=$questionId");
            exit;
        } else {
            $error = $result;
        }
    } else {
        $error = "Please enter option text.";
    }
}

// Process option deletion
if (isset($_GET['delete_option']) && !empty($_GET['delete_option'])) {
    $optionId = intval($_GET['delete_option']);
    
    // Check if option exists and belongs to this question
    $stmt = $pdo->prepare("
        SELECT * FROM quiz_options
        WHERE option_id = :optionId AND question_id = :questionId
    ");
    $stmt->bindParam(':optionId', $optionId);
    $stmt->bindParam(':questionId', $questionId);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Option not found or does not belong to this question.";
        header("location: quiz_question_options.php?id=$questionId");
        exit;
    }
    
    // Delete the option
    $stmt = $pdo->prepare("DELETE FROM quiz_options WHERE option_id = :optionId");
    $stmt->bindParam(':optionId', $optionId);
    $stmt->execute();
    
    $_SESSION['success'] = "Option deleted successfully!";
    header("location: quiz_question_options.php?id=$questionId");
    exit;
}

// Process option update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_options'])) {
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // First, set all options to not correct
        $stmt = $pdo->prepare("
            UPDATE quiz_options
            SET is_correct = 0
            WHERE question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();
        
        // Update option texts and set correct options
        foreach ($_POST['option_text'] as $optionId => $text) {
            $isCorrect = isset($_POST['is_correct']) && in_array($optionId, $_POST['is_correct']) ? 1 : 0;
            
            $stmt = $pdo->prepare("
                UPDATE quiz_options
                SET option_text = :text, is_correct = :isCorrect
                WHERE option_id = :optionId AND question_id = :questionId
            ");
            $stmt->bindParam(':text', $text);
            $stmt->bindParam(':isCorrect', $isCorrect);
            $stmt->bindParam(':optionId', $optionId);
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();
        }
        
        $pdo->commit();
        $_SESSION['success'] = "Options updated successfully!";
        header("location: quiz_question_options.php?id=$questionId");
        exit;
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Failed to update options: " . $e->getMessage();
    }
}

// Set page title
$page_title = "Manage Quiz Question Options";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($question['activity_title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="quiz_edit.php?id=<?php echo $activityId; ?>">Edit Quiz</a></li>
                    <li class="breadcrumb-item active">Manage Question Options</li>
                </ol>
            </nav>
            
            <h1 class="mb-4">Manage Quiz Question Options</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Question</h5>
                </div>
                <div class="card-body">
                    <h5><?php echo htmlspecialchars($question['question_text']); ?></h5>
                    <p><strong>Type:</strong> <?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></p>
                    <p><strong>Points:</strong> <?php echo $question['points']; ?></p>
                </div>
            </div>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Add New Option</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $questionId); ?>" method="post">
                        <div class="form-group">
                            <label for="option_text">Option Text <span class="text-danger">*</span></label>
                            <input type="text" name="option_text" id="option_text" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_correct" name="is_correct">
                                <label class="custom-control-label" for="is_correct">This is the correct answer</label>
                            </div>
                            <?php if ($question['question_type'] == 'true_false'): ?>
                            <small class="form-text text-muted">For True/False questions, mark "True" as correct or leave unchecked for "False".</small>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group text-right">
                            <input type="hidden" name="add_option" value="1">
                            <button type="submit" class="btn btn-success">Add Option</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if (count($options) > 0): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Existing Options</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $questionId); ?>" method="post">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Option Text</th>
                                        <th width="120">Correct</th>
                                        <th width="100">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($options as $option): ?>
                                    <tr>
                                        <td>
                                            <input type="text" name="option_text[<?php echo $option['option_id']; ?>]" class="form-control" value="<?php echo htmlspecialchars($option['option_text']); ?>" required>
                                        </td>
                                        <td class="text-center">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="correct_<?php echo $option['option_id']; ?>" name="is_correct[]" value="<?php echo $option['option_id']; ?>" <?php echo $option['is_correct'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="correct_<?php echo $option['option_id']; ?>"></label>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $questionId . '&delete_option=' . $option['option_id']); ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this option?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="form-group text-right mt-3">
                            <input type="hidden" name="update_options" value="1">
                            <a href="quiz_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-secondary mr-2">Back to Quiz</a>
                            <button type="submit" class="btn btn-primary">Update Options</button>
                        </div>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-info">
                <p class="mb-0">No options added yet. Add options using the form above.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
