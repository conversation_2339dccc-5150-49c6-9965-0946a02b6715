<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/quiz_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to manage quiz questions.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_POST['activity_id']) || empty($_POST['activity_id'])) {
    $_SESSION['error'] = "Activity ID is required.";
    header("location: index.php");
    exit;
}

$activityId = intval($_POST['activity_id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists and is a quiz
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

if ($activity['activity_type'] != 'quiz') {
    $_SESSION['error'] = "This activity is not a quiz.";
    header("location: activity_view.php?id=$activityId");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this quiz
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to manage questions for this quiz.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Process form data
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received in quiz_question_handler.php: " . print_r($_POST, true));

    // Enhanced debugging
    file_put_contents('debug_quiz_post.log', print_r($_POST, true));

    // Add a new question
    if (isset($_POST['add_question'])) {
        $questionText = trim($_POST['question_text']);
        $questionType = $_POST['question_type'];
        $points = intval($_POST['points']);

        if (!empty($questionText) && !empty($questionType) && $points > 0) {
            // Use the appropriate function for quiz questions
            $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);

            if (is_numeric($result)) {
                $questionId = $result;
                $_SESSION['success'] = "Question added successfully!";

                // Handle different question types
                if ($questionType == 'multiple_choice') {
                    // Add multiple choice options
                    $correctAnswer = trim($_POST['mc_correct_answer']);

                    // Add options A, B, C, D if they exist
                    if (!empty($_POST['option_a'])) {
                        $optionA = trim($_POST['option_a']);
                        $isCorrect = ($correctAnswer == 'A');
                        $optionResult = addQuizOption($questionId, $optionA, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option A: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_b'])) {
                        $optionB = trim($_POST['option_b']);
                        $isCorrect = ($correctAnswer == 'B');
                        $optionResult = addQuizOption($questionId, $optionB, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option B: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_c'])) {
                        $optionC = trim($_POST['option_c']);
                        $isCorrect = ($correctAnswer == 'C');
                        $optionResult = addQuizOption($questionId, $optionC, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option C: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_d'])) {
                        $optionD = trim($_POST['option_d']);
                        $isCorrect = ($correctAnswer == 'D');
                        $optionResult = addQuizOption($questionId, $optionD, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $_SESSION['error'] = "Question added but failed to add option D: " . $optionResult;
                        }
                    }
                }
                elseif ($questionType == 'true_false') {
                    // Add true/false options
                    $correctAnswer = $_POST['tf_correct_answer'];

                    // Add True option
                    $isCorrect = ($correctAnswer == 'true');
                    $optionResult = addQuizOption($questionId, 'True', $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to add True option: " . $optionResult;
                    }

                    // Add False option
                    $isCorrect = ($correctAnswer == 'false');
                    $optionResult = addQuizOption($questionId, 'False', $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to add False option: " . $optionResult;
                    }
                }
                elseif ($questionType == 'short_answer' && isset($_POST['sa_correct_answer'])) {
                    // Add short answer correct answer
                    $correctAnswer = trim($_POST['sa_correct_answer']);
                    $optionResult = addQuizOption($questionId, $correctAnswer, true);

                    if (!is_numeric($optionResult)) {
                        $_SESSION['error'] = "Question added but failed to set correct answer: " . $optionResult;
                    }
                }
            } else {
                $_SESSION['error'] = $result;
            }
        } else {
            $_SESSION['error'] = "Please fill in all question fields.";
        }
    }

    // Redirect back to the quiz edit page
    header("location: quiz_edit.php?id=$activityId#questions");
    exit;
} else {
    // If not a POST request, redirect to the quiz edit page
    header("location: quiz_edit.php?id=$activityId");
    exit;
}
?>
