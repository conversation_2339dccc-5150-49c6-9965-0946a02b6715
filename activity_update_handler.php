<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_POST['activity_id']) || empty($_POST['activity_id'])) {
    $_SESSION['error'] = "Activity ID is required.";
    header("location: index.php");
    exit;
}

$activityId = intval($_POST['activity_id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit activities for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Process form data
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received in activity_update_handler.php: " . print_r($_POST, true));

    // Get form data
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $dueDate = !empty($_POST['due_date']) ? date("Y-m-d H:i:s", strtotime($_POST['due_date'])) : null;
    $isPublished = isset($_POST['is_published']) ? true : false;
    $allowLateSubmissions = isset($_POST['allow_late_submissions']) ? true : false;

    // Validate form data
    $errors = [];

    if (empty($title)) {
        $errors[] = "Title is required.";
    }

    if (empty($description)) {
        $errors[] = "Description is required.";
    }

    // Check for errors
    if (empty($errors)) {
        // Update the activity
        $result = updateActivity($activityId, $title, $description, 0, $dueDate, $isPublished, $allowLateSubmissions, null);

        if ($result === true) {
            $_SESSION['success'] = "Activity updated successfully!";
        } else {
            $_SESSION['error'] = $result;
        }
    } else {
        $_SESSION['error'] = implode("<br>", $errors);
    }

    // Redirect back to the activity edit page
    header("location: activity_edit.php?id=$activityId");
    exit;
} else {
    // If not a POST request, redirect to the activity edit page
    header("location: activity_edit.php?id=$activityId");
    exit;
}
?>
