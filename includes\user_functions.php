<?php
/**
 * User Management Functions
 *
 * This file contains functions related to user management.
 */

require_once 'config.php';

/**
 * Function to get all users
 * Only admin can access all users
 *
 * @return array|string Array of users if successful, error message otherwise
 */
function getAllUsers() {
    global $pdo;

    // Only admin can get all users
    if (!isAdmin()) {
        return "Only administrators can access all user accounts.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.gender, u.birthday, u.phone_number,
                   u.role_id, u.is_active, u.created_at, u.updated_at, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve users: " . $e->getMessage();
    }
}

/**
 * Function to get a user by ID
 * Admin can get any user, users can only get their own profile
 *
 * @param int $userId The user ID
 * @return array|string User data if successful, error message otherwise
 */
function getUserById($userId) {
    global $pdo;

    // Check if user is authorized to get this profile
    if (!isAdmin() && $_SESSION['user_id'] != $userId) {
        // Don't show error on index.php page to avoid disrupting the UI
        $currentPage = basename($_SERVER['PHP_SELF']);
        if ($currentPage === 'index.php') {
            return [
                'user_id' => $userId,
                'username' => 'Unknown',
                'email' => '',
                'first_name' => 'Unknown',
                'last_name' => 'User',
                'role_name' => 'unknown'
            ];
        }
        return "You are not authorized to access this user profile.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.gender, u.birthday, u.phone_number,
                   u.profile_picture, u.role_id, u.is_active, u.created_at, u.updated_at, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :userId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "User not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve user: " . $e->getMessage();
    }
}

/**
 * Function to get all roles
 *
 * @return array|string Array of roles if successful, error message otherwise
 */
function getAllRoles() {
    global $pdo;

    try {
        // Check if roles table exists, if not create it
        $stmt = $pdo->query('SHOW TABLES LIKE "roles"');
        if ($stmt->rowCount() == 0) {
            // Create roles table
            $pdo->exec("
                CREATE TABLE roles (
                    role_id INT PRIMARY KEY AUTO_INCREMENT,
                    role_name VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT
                )
            ");

            // Insert default roles
            $pdo->exec("
                INSERT INTO roles (role_id, role_name, description) VALUES
                (1, 'admin', 'Administrator with full system access'),
                (2, 'teacher', 'Instructor who can create and manage courses'),
                (3, 'student', 'Student who can enroll in courses')
            ");
        }

        // Get roles from database
        $currentPage = basename($_SERVER['PHP_SELF']);
        if ($currentPage === 'users.php' || $currentPage === 'user_add.php') {
            // For admin user creation, only return admin and teacher roles (no student)
            $stmt = $pdo->prepare("SELECT role_id, role_name, description FROM roles WHERE role_name IN ('admin', 'teacher') ORDER BY role_id");
        } else {
            // Return all roles for other pages
            $stmt = $pdo->prepare("SELECT role_id, role_name, description FROM roles ORDER BY role_id");
        }

        $stmt->execute();
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        return "Failed to retrieve roles: " . $e->getMessage();
    }
}

/**
 * Function to get a role by ID
 *
 * @param int $roleId The role ID (1, 2, 3)
 * @return array|string Role data if successful, error message otherwise
 */
function getRoleById($roleId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT role_id, role_name, description FROM roles WHERE role_id = :roleId");
        $stmt->bindParam(':roleId', $roleId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Role not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve role: " . $e->getMessage();
    }
}

/**
 * Function to count users by role
 * Only admin can access this information
 *
 * @return array|string Array of user counts by role if successful, error message otherwise
 */
function countUsersByRole() {
    global $pdo;

    // Only admin can get user counts
    if (!isAdmin()) {
        return "Only administrators can access user statistics.";
    }

    try {
        // Ensure roles table exists
        getAllRoles();

        $stmt = $pdo->prepare("
            SELECT r.role_name, COUNT(u.user_id) as user_count
            FROM roles r
            LEFT JOIN users u ON r.role_id = u.role_id
            GROUP BY r.role_id, r.role_name
            ORDER BY r.role_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve user counts: " . $e->getMessage();
    }
}

/**
 * Function to search users by name or email
 * Only admin can search all users
 *
 * @param string $searchTerm The search term
 * @return array|string Array of matching users if successful, error message otherwise
 */
function searchUsers($searchTerm) {
    global $pdo;

    // Only admin can search all users
    if (!isAdmin()) {
        return "Only administrators can search user accounts.";
    }

    try {
        // Ensure roles table exists
        getAllRoles();

        $searchTerm = "%$searchTerm%";

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.role_id, u.is_active, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.username LIKE :searchTerm
               OR u.email LIKE :searchTerm
               OR u.first_name LIKE :searchTerm
               OR u.last_name LIKE :searchTerm
            ORDER BY u.user_id
        ");
        $stmt->bindParam(':searchTerm', $searchTerm);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to search users: " . $e->getMessage();
    }
}

/**
 * Function to get active users
 * Only admin can access this information
 *
 * @return array|string Array of active users if successful, error message otherwise
 */
function getActiveUsers() {
    global $pdo;

    // Only admin can get active users
    if (!isAdmin()) {
        return "Only administrators can access active user information.";
    }

    try {
        // Ensure roles table exists
        getAllRoles();

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.is_active = 1
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve active users: " . $e->getMessage();
    }
}

/**
 * Function to get inactive users
 * Only admin can access this information
 *
 * @return array|string Array of inactive users if successful, error message otherwise
 */
function getInactiveUsers() {
    global $pdo;

    // Only admin can get inactive users
    if (!isAdmin()) {
        return "Only administrators can access inactive user information.";
    }

    try {
        // Ensure roles table exists
        getAllRoles();

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.is_active = 0
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve inactive users: " . $e->getMessage();
    }
}

/**
 * Function to activate or deactivate a user
 * Only admin can activate/deactivate users
 *
 * @param int $userId The user ID
 * @param bool $isActive The active status
 * @return bool|string True if successful, error message otherwise
 */
function setUserActiveStatus($userId, $isActive) {
    global $pdo;

    // Only admin can activate/deactivate users
    if (!isAdmin()) {
        return "Only administrators can activate or deactivate user accounts.";
    }

    try {
        $stmt = $pdo->prepare("UPDATE users SET is_active = :isActive WHERE user_id = :userId");
        $stmt->bindParam(':isActive', $isActive, PDO::PARAM_BOOL);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "User not found or status already set.";
        }
    } catch (PDOException $e) {
        return "Failed to update user status: " . $e->getMessage();
    }
}

/**
 * Function to get users by role
 * Admin can get any users, teachers can only get students
 *
 * @param string $roleName The role name (admin, teacher, student)
 * @return array|string Array of users if successful, error message otherwise
 */
function getUsersByRole($roleName) {
    global $pdo;

    // Check permissions
    if (!isAdmin() && ($roleName == 'admin' || $roleName == 'teacher')) {
        return "You don't have permission to access this information.";
    }

    try {
        // Ensure roles table exists
        getAllRoles();

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.is_active
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE r.role_name = :roleName AND u.is_active = 1
            ORDER BY u.last_name, u.first_name
        ");
        $stmt->bindParam(':roleName', $roleName);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve users by role: " . $e->getMessage();
    }
}
?>
