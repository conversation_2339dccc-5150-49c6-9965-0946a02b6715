<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin
if (!isAdmin()) {
    echo "You must be an admin to run this script.";
    exit;
}

echo "<h1>Create Sample Activity</h1>";

try {
    // Check if there are any courses
    $stmt = $pdo->query("SELECT * FROM courses ORDER BY course_id LIMIT 1");
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        echo "<p>No courses found. Please create a course first.</p>";
        echo "<p><a href='course_create.php' class='btn btn-primary'>Create Course</a></p>";
        exit;
    }
    
    $courseId = $course['course_id'];
    $userId = $_SESSION['user_id'];
    
    echo "<p>Using course ID: $courseId, Title: " . htmlspecialchars($course['title']) . "</p>";
    
    // Check if there are any activities
    $stmt = $pdo->query("SELECT COUNT(*) FROM activities");
    $activityCount = $stmt->fetchColumn();
    
    echo "<p>Current activity count: $activityCount</p>";
    
    // Create a sample activity
    $title = "Sample Activity " . date('Y-m-d H:i:s');
    $description = "This is a sample activity created for testing purposes.";
    $activityType = "activity";
    $points = 10;
    $dueDate = date('Y-m-d H:i:s', strtotime('+1 week'));
    $isPublished = true;
    $allowLateSubmissions = true;
    
    echo "<p>Creating sample activity with title: " . htmlspecialchars($title) . "</p>";
    
    $result = createActivity($courseId, $title, $description, $activityType, $points, $dueDate, $isPublished, $allowLateSubmissions);
    
    if (is_numeric($result)) {
        $activityId = $result;
        echo "<p>Activity created successfully with ID: $activityId</p>";
        
        // Create sample questions
        echo "<h2>Creating Sample Questions</h2>";
        
        // Create multiple choice question
        $questionText = "What is the capital of France?";
        $questionType = "multiple_choice";
        $questionPoints = 5;
        
        echo "<p>Creating multiple choice question: " . htmlspecialchars($questionText) . "</p>";
        
        $questionResult = addActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
        
        if (is_numeric($questionResult)) {
            $questionId = $questionResult;
            echo "<p>Multiple choice question created with ID: $questionId</p>";
            
            // Add options
            $options = [
                ["Paris", true],
                ["London", false],
                ["Berlin", false],
                ["Madrid", false]
            ];
            
            foreach ($options as $index => $option) {
                $optionText = $option[0];
                $isCorrect = $option[1];
                
                $optionResult = addActivityQuestionOption($questionId, $optionText, $isCorrect);
                
                if (is_numeric($optionResult)) {
                    echo "<p>Option added: " . htmlspecialchars($optionText) . " (Correct: " . ($isCorrect ? "Yes" : "No") . ")</p>";
                } else {
                    echo "<p>Error adding option: " . $optionResult . "</p>";
                }
            }
        } else {
            echo "<p>Error creating multiple choice question: " . $questionResult . "</p>";
        }
        
        // Create true/false question
        $questionText = "The Earth is flat.";
        $questionType = "true_false";
        $questionPoints = 3;
        
        echo "<p>Creating true/false question: " . htmlspecialchars($questionText) . "</p>";
        
        $questionResult = addActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
        
        if (is_numeric($questionResult)) {
            $questionId = $questionResult;
            echo "<p>True/false question created with ID: $questionId</p>";
            
            // Add options
            $options = [
                ["True", false],
                ["False", true]
            ];
            
            foreach ($options as $index => $option) {
                $optionText = $option[0];
                $isCorrect = $option[1];
                
                $optionResult = addActivityQuestionOption($questionId, $optionText, $isCorrect);
                
                if (is_numeric($optionResult)) {
                    echo "<p>Option added: " . htmlspecialchars($optionText) . " (Correct: " . ($isCorrect ? "Yes" : "No") . ")</p>";
                } else {
                    echo "<p>Error adding option: " . $optionResult . "</p>";
                }
            }
        } else {
            echo "<p>Error creating true/false question: " . $questionResult . "</p>";
        }
        
        // Create short answer question
        $questionText = "What is the chemical symbol for water?";
        $questionType = "short_answer";
        $questionPoints = 4;
        
        echo "<p>Creating short answer question: " . htmlspecialchars($questionText) . "</p>";
        
        $questionResult = addActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
        
        if (is_numeric($questionResult)) {
            $questionId = $questionResult;
            echo "<p>Short answer question created with ID: $questionId</p>";
            
            // Add correct answer
            $correctAnswer = "H2O";
            $optionResult = addActivityQuestionOption($questionId, $correctAnswer, true);
            
            if (is_numeric($optionResult)) {
                echo "<p>Correct answer added: " . htmlspecialchars($correctAnswer) . "</p>";
            } else {
                echo "<p>Error adding correct answer: " . $optionResult . "</p>";
            }
        } else {
            echo "<p>Error creating short answer question: " . $questionResult . "</p>";
        }
        
        echo "<h2>Sample Activity Created Successfully</h2>";
        echo "<p><a href='activity_edit.php?id=$activityId' class='btn btn-primary'>Edit Activity</a></p>";
        echo "<p><a href='debug_questions.php?id=$activityId' class='btn btn-info'>Debug Questions</a></p>";
    } else {
        echo "<p>Error creating activity: " . $result . "</p>";
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
