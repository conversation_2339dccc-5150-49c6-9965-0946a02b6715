<?php
// Include configuration file
require_once 'includes/config.php';

// Set page title
$page_title = "Setup Notification Settings";

// Create the unified notification settings table
try {
    global $pdo;

    // Check if the table already exists
    $tableExists = false;
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($stmt->rowCount() > 0) {
        // Drop the existing table to recreate it
        $pdo->exec("DROP TABLE user_notification_settings");
        $message = "The user_notification_settings table has been dropped and will be recreated.";
        $messageType = "warning";
    }

    // Create the table with all possible notification settings for all user types
    $sql = "CREATE TABLE user_notification_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        
        /* General settings for all users */
        email_notifications TINYINT(1) DEFAULT 1,
        theme VARCHAR(20) DEFAULT 'light',
        
        /* Student-specific settings */
        assignment_reminders TINYINT(1) DEFAULT 1,
        course_announcements TINYINT(1) DEFAULT 1,
        due_date_reminders TINYINT(1) DEFAULT 1,
        grade_updates TINYINT(1) DEFAULT 1,
        
        /* Instructor-specific settings */
        student_submissions TINYINT(1) DEFAULT 1,
        enrollment_requests TINYINT(1) DEFAULT 1,
        course_activity TINYINT(1) DEFAULT 1,
        
        /* Admin-specific settings */
        user_registrations TINYINT(1) DEFAULT 1,
        course_creation TINYINT(1) DEFAULT 1,
        account_changes TINYINT(1) DEFAULT 1,
        error_alerts TINYINT(1) DEFAULT 1,
        weekly_reports TINYINT(1) DEFAULT 1,
        
        /* Comment settings for all users */
        comments_on_posts TINYINT(1) DEFAULT 1,
        comments_mentions TINYINT(1) DEFAULT 1,
        private_comments TINYINT(1) DEFAULT 1,
        
        /* Calendar settings for all users */
        event_creation TINYINT(1) DEFAULT 1,
        event_updates TINYINT(1) DEFAULT 1,
        event_reminders TINYINT(1) DEFAULT 1,
        status_changes TINYINT(1) DEFAULT 1,
        system_announcements TINYINT(1) DEFAULT 1,
        
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE KEY (user_id)
    )";

    $pdo->exec($sql);
    $message = "The user_notification_settings table has been created successfully.";
    $messageType = "success";
    
    // Add default settings for all existing users
    $stmt = $pdo->query("SELECT user_id, role_id FROM users");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        // Get role name
        $roleStmt = $pdo->prepare("SELECT role_name FROM roles WHERE role_id = ?");
        $roleStmt->execute([$user['role_id']]);
        $role = $roleStmt->fetchColumn();
        
        // Insert default settings based on role
        $insertStmt = $pdo->prepare("INSERT INTO user_notification_settings 
            (user_id, email_notifications, theme, 
             assignment_reminders, course_announcements, due_date_reminders, grade_updates,
             student_submissions, enrollment_requests, course_activity,
             user_registrations, course_creation, account_changes, error_alerts, weekly_reports,
             comments_on_posts, comments_mentions, private_comments,
             event_creation, event_updates, event_reminders, status_changes, system_announcements)
            VALUES (?, 1, 'light', 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)");
        
        $insertStmt->execute([$user['user_id']]);
    }
    
    $userCount = count($users);
    $message .= " Default notification settings added for $userCount users.";
    
} catch (PDOException $e) {
    $message = "Error creating user_notification_settings table: " . $e->getMessage();
    $messageType = "danger";
}

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Setup Notification Settings</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo $message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <p>This script creates the unified user_notification_settings table in the database.</p>
                    <p>This table stores notification preferences for all user types (students, instructors, and admins).</p>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
