<?php
// Classwork tab content - shows modules, materials, and assignments
require_once 'includes/activity_functions.php';
?>
<div id="classwork-tab" class="course-tab-content">
    <!-- Classwork content section -->

    <!-- Modules and content -->
    <?php if (count($modules) > 0): ?>
        <?php foreach ($modules as $module): ?>
        <div class="card mb-4 module-card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo htmlspecialchars($module['title']); ?></h5>
            </div>
            <div class="list-group list-group-flush">
                <?php
                // Get module content
                $moduleContent = getModuleContent($module['module_id']);
                if (!is_string($moduleContent) && count($moduleContent) > 0):
                ?>
                    <?php foreach ($moduleContent as $content): ?>
                    <div class="list-group-item">
                        <div class="d-flex align-items-center">
                            <?php if ($content['content_type'] == 'assignment'): ?>
                                <div class="content-icon mr-3 bg-primary-light">
                                    <i class="fas fa-clipboard-list text-primary"></i>
                                </div>
                            <?php elseif ($content['content_type'] == 'material'): ?>
                                <div class="content-icon mr-3 bg-info-light">
                                    <i class="fas fa-file-alt text-info"></i>
                                </div>
                            <?php elseif ($content['content_type'] == 'activity'): ?>
                                <div class="content-icon mr-3 bg-success-light">
                                    <i class="fas fa-tasks text-success"></i>
                                </div>
                            <?php elseif ($content['content_type'] == 'quiz'): ?>
                                <div class="content-icon mr-3 bg-warning-light">
                                    <i class="fas fa-question-circle text-warning"></i>
                                </div>
                            <?php endif; ?>

                            <div class="flex-grow-1">
                                <h6 class="mb-0">
                                    <?php echo htmlspecialchars($content['title']); ?>
                                    <?php if (isStudent()): ?>
                                        <?php if (isActivityCompleted($content['content_id'], $_SESSION['user_id'])): ?>
                                            <span class="badge badge-success ml-2"><i class="fas fa-check"></i> Done</span>
                                        <?php elseif (!empty($content['due_date']) && strtotime($content['due_date']) < time()): ?>
                                            <span class="badge badge-danger ml-2"><i class="fas fa-exclamation-circle"></i> Missing</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </h6>
                                <small class="text-muted">
                                    <?php echo ucfirst($content['content_type']); ?> •
                                    <?php echo date('M j, Y', strtotime($content['created_at'])); ?>
                                    <?php if (!empty($content['due_date'])): ?>
                                    • Due <?php echo date('M j, Y g:i A', strtotime($content['due_date'])); ?>
                                    <?php endif; ?>
                                </small>
                            </div>

                            <div>
                                <a href="content_view.php?id=<?php echo $content['content_id']; ?>&type=<?php echo $content['content_type']; ?>" class="btn btn-sm btn-outline-primary">
                                    View
                                </a>
                                <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
                                <?php if ($content['content_type'] == 'material'): ?>
                                <a href="material_edit.php?id=<?php echo $content['content_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php elseif ($content['content_type'] == 'assignment'): ?>
                                <a href="assignment_edit.php?id=<?php echo $content['content_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php elseif ($content['content_type'] == 'quiz'): ?>
                                <a href="quiz_edit.php?id=<?php echo $content['content_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php else: ?>
                                <a href="activity_edit.php?id=<?php echo $content['content_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="list-group-item text-center py-4">
                        <p class="text-muted mb-0">No content in this module yet</p>
                    </div>
                <?php endif; ?>
            </div>
            <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
            <div class="card-footer bg-white">
                <div class="d-flex justify-content-end">
                    <a href="module_edit.php?id=<?php echo $module['module_id']; ?>" class="btn btn-sm btn-outline-secondary mr-2">
                        <i class="fas fa-edit"></i> Edit Module
                    </a>
                    <a href="module_delete.php?id=<?php echo $module['module_id']; ?>" class="btn btn-sm btn-outline-danger"
                       onclick="return confirm('Are you sure you want to delete this module? All content within this module will also be deleted.');">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Activities not in modules -->
    <?php
    // Get activities not in modules
    require_once 'includes/activity_functions.php';
    $activities = getActivitiesByCourse($courseId);

    // Filter out activities that are already in modules
    $moduleActivities = [];
    if (count($modules) > 0) {
        foreach ($modules as $module) {
            $moduleContent = getModuleContent($module['module_id']);
            if (!is_string($moduleContent) && count($moduleContent) > 0) {
                foreach ($moduleContent as $content) {
                    $moduleActivities[] = $content['content_id'];
                }
            }
        }
    }

    // Filter activities not in modules
    $unmappedActivities = [];
    if (!is_string($activities) && count($activities) > 0) {
        foreach ($activities as $activity) {
            if (!in_array($activity['activity_id'], $moduleActivities)) {
                $unmappedActivities[] = $activity;
            }
        }
    }

    if (count($unmappedActivities) > 0):
    ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Unorganized Activities</h5>
        </div>
        <div class="list-group list-group-flush">
            <?php foreach ($unmappedActivities as $activity): ?>
            <div class="list-group-item">
                <div class="d-flex align-items-center">
                    <?php if ($activity['activity_type'] == 'assignment'): ?>
                        <div class="content-icon mr-3 bg-primary-light">
                            <i class="fas fa-clipboard-list text-primary"></i>
                        </div>
                    <?php elseif ($activity['activity_type'] == 'material'): ?>
                        <div class="content-icon mr-3 bg-info-light">
                            <i class="fas fa-file-alt text-info"></i>
                        </div>
                    <?php elseif ($activity['activity_type'] == 'activity'): ?>
                        <div class="content-icon mr-3 bg-success-light">
                            <i class="fas fa-tasks text-success"></i>
                        </div>
                    <?php elseif ($activity['activity_type'] == 'quiz'): ?>
                        <div class="content-icon mr-3 bg-warning-light">
                            <i class="fas fa-question-circle text-warning"></i>
                        </div>
                    <?php endif; ?>

                    <div class="flex-grow-1">
                        <h6 class="mb-0">
                            <?php echo htmlspecialchars($activity['title']); ?>
                            <?php if (isStudent()): ?>
                                <?php if (isActivityCompleted($activity['activity_id'], $_SESSION['user_id'])): ?>
                                    <span class="badge badge-success ml-2"><i class="fas fa-check"></i> Done</span>
                                <?php elseif (!empty($activity['due_date']) && strtotime($activity['due_date']) < time()): ?>
                                    <span class="badge badge-danger ml-2"><i class="fas fa-exclamation-circle"></i> Missing</span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </h6>
                        <small class="text-muted">
                            <?php echo ucfirst($activity['activity_type']); ?> •
                            <?php echo date('M j, Y', strtotime($activity['created_at'])); ?>
                            <?php if (!empty($activity['due_date'])): ?>
                            • Due <?php echo date('M j, Y g:i A', strtotime($activity['due_date'])); ?>
                            <?php endif; ?>
                        </small>
                    </div>

                    <div>
                        <?php if ($activity['activity_type'] == 'material'): ?>
                        <a href="content_view.php?id=<?php echo $activity['activity_id']; ?>&type=<?php echo $activity['activity_type']; ?>" class="btn btn-sm btn-outline-primary">
                            View
                        </a>
                        <?php else: ?>
                        <a href="activity_view.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-primary">
                            View
                        </a>
                        <?php endif; ?>
                        <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
                        <?php if ($activity['activity_type'] == 'quiz'): ?>
                        <a href="quiz_edit.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php elseif ($activity['activity_type'] == 'assignment'): ?>
                        <a href="assignment_edit.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php elseif ($activity['activity_type'] == 'material'): ?>
                        <a href="material_edit.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php else: ?>
                        <a href="activity_edit.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php endif; ?>
                        <?php if ($activity['activity_type'] != 'material'): ?>
                        <a href="activity_submissions.php?id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-users"></i> Submissions
                        </a>
                        <?php endif; ?>
                        <a href="activity_action.php?action=delete&id=<?php echo $activity['activity_id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this activity? This action cannot be undone.');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (count($modules) == 0 && count($unmappedActivities) == 0): ?>
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-book"></i>
            </div>
            <h3>No classwork yet</h3>
            <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
            <p class="empty-state-text">Create modules and add content to get started</p>
            <a href="module_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Module
            </a>
            <?php else: ?>
            <p class="empty-state-text">Your instructor hasn't added any classwork yet</p>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>


