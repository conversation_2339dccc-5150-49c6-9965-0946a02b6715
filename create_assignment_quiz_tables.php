<?php
// Include configuration file
require_once 'includes/config.php';

// Create or update activities table
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activities (
            activity_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            activity_type ENUM('material', 'assignment', 'quiz', 'question') NOT NULL,
            points INT DEFAULT 0,
            due_date DATETIME DEFAULT NULL,
            is_published TINYINT(1) DEFAULT 1,
            allow_late_submissions TINYINT(1) DEFAULT 0,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");

    // Check if allow_late_submissions column exists, add if not
    $stmt = $pdo->prepare("SHOW COLUMNS FROM activities LIKE 'allow_late_submissions'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE activities ADD COLUMN allow_late_submissions TINYINT(1) DEFAULT 0 AFTER is_published");
        echo "Added allow_late_submissions column to activities table.<br>";
    }

    echo "Activities table created or updated.<br>";

    // Create activity_files table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_files (
            file_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(255) NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            file_size INT NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE
        )
    ");

    echo "Activity files table created.<br>";

    // Create module_activities table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS module_activities (
            module_activity_id INT AUTO_INCREMENT PRIMARY KEY,
            module_id INT NOT NULL,
            activity_id INT NOT NULL,
            position INT DEFAULT 0,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
            UNIQUE KEY (module_id, activity_id)
        )
    ");

    echo "Module activities table created.<br>";

    // Create quiz_settings table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quiz_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            time_limit INT DEFAULT 60,
            passing_score DECIMAL(5,2) DEFAULT 70.00,
            shuffle_questions TINYINT(1) DEFAULT 0,
            show_results_immediately TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE
        )
    ");

    echo "Quiz settings table created.<br>";

    // Create quiz_questions table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quiz_questions (
            question_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            question_text TEXT NOT NULL,
            question_type ENUM('multiple_choice', 'true_false', 'short_answer') NOT NULL,
            points INT DEFAULT 1,
            position INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE
        )
    ");

    echo "Quiz questions table created.<br>";

    // Create quiz_options table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quiz_options (
            option_id INT AUTO_INCREMENT PRIMARY KEY,
            question_id INT NOT NULL,
            option_text TEXT NOT NULL,
            is_correct TINYINT(1) DEFAULT 0,
            position INT DEFAULT 0,
            FOREIGN KEY (question_id) REFERENCES quiz_questions(question_id) ON DELETE CASCADE
        )
    ");

    echo "Quiz options table created.<br>";

    // Create quiz_attempts table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quiz_attempts (
            attempt_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            user_id INT NOT NULL,
            start_time DATETIME NOT NULL,
            end_time DATETIME DEFAULT NULL,
            score DECIMAL(5,2) DEFAULT NULL,
            is_completed TINYINT(1) DEFAULT 0,
            is_graded TINYINT(1) DEFAULT 0,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");

    echo "Quiz attempts table created.<br>";

    // Create quiz_responses table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quiz_responses (
            response_id INT AUTO_INCREMENT PRIMARY KEY,
            attempt_id INT NOT NULL,
            question_id INT NOT NULL,
            selected_option_id INT DEFAULT NULL,
            text_response TEXT DEFAULT NULL,
            is_correct TINYINT(1) DEFAULT 0,
            points_earned DECIMAL(5,2) DEFAULT 0,
            FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(attempt_id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES quiz_questions(question_id) ON DELETE CASCADE,
            FOREIGN KEY (selected_option_id) REFERENCES quiz_options(option_id) ON DELETE SET NULL
        )
    ");

    echo "Quiz responses table created.<br>";

    // Create assignment_submissions table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS assignment_submissions (
            submission_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            user_id INT NOT NULL,
            content TEXT DEFAULT NULL,
            file_path VARCHAR(255) DEFAULT NULL,
            file_name VARCHAR(255) DEFAULT NULL,
            file_type VARCHAR(100) DEFAULT NULL,
            file_size INT DEFAULT NULL,
            submission_date DATETIME NOT NULL,
            is_late TINYINT(1) DEFAULT 0,
            grade DECIMAL(5,2) DEFAULT NULL,
            feedback TEXT DEFAULT NULL,
            graded_by INT DEFAULT NULL,
            graded_at DATETIME DEFAULT NULL,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (graded_by) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");

    echo "Assignment submissions table created.<br>";

    echo "Setup completed successfully!";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
