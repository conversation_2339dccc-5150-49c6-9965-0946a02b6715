<?php
// <PERSON><PERSON><PERSON> to reset the admin password
require_once 'includes/config.php';
global $pdo;

// Admin details
$username = 'admin';
$newPassword = 'admin123';

try {
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "Admin user not found.<br>";
    } else {
        // Get the user ID
        $user = $stmt->fetch();
        $userId = $user['user_id'];
        
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update the admin password
        $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :userId");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        
        echo "Admin password reset successfully!<br>";
        echo "Username: $username<br>";
        echo "New Password: $newPassword<br>";
        echo "You can now log in with these credentials.<br>";
        
        // Display the hashed password for verification
        echo "<br>Technical details (for debugging):<br>";
        echo "Hashed password: $hashedPassword<br>";
        
        // Verify the password hash works
        if (password_verify($newPassword, $hashedPassword)) {
            echo "Password verification test: SUCCESS<br>";
        } else {
            echo "Password verification test: FAILED<br>";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
