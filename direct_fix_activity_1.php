<?php
// Include configuration file
require_once 'includes/config.php';

// Set the activity ID
$activityId = 1;

try {
    // Update the activity type directly to 'activity'
    $stmt = $pdo->prepare("UPDATE activities SET activity_type = 'activity' WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    
    if ($stmt->execute()) {
        echo "<p>Activity type updated to 'activity' successfully!</p>";
        
        // Force refresh the page by redirecting
        echo "<script>
            setTimeout(function() {
                window.location.href = 'activity_edit.php?id=1#questions';
            }, 1000);
        </script>";
        
        echo "<p>Redirecting to the activity edit page in 1 second...</p>";
        echo "<p>If you are not redirected, <a href='activity_edit.php?id=1#questions'>click here</a>.</p>";
    } else {
        echo "<p>Error updating activity type.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
