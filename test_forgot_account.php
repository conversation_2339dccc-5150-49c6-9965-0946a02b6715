<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/email_helper.php';

echo "<h1>Testing Forgot Account Flow</h1>";

// Test the maskEmail function
echo "<h2>Testing maskEmail Function</h2>";
$testEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    'a@b.c',
    ''
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Original Email</th><th>Masked Email</th></tr>";
foreach ($testEmails as $email) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($email) . "</td>";
    echo "<td>" . htmlspecialchars(maskEmail($email)) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test user lookup by username
echo "<h2>Testing User Lookup by Username</h2>";
$testUsernames = [
    'admin',
    'student',
    'nonexistent'
];

global $pdo;
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Username</th><th>Found?</th><th>User ID</th><th>Email</th><th>Masked Email</th></tr>";

foreach ($testUsernames as $username) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($username) . "</td>";
    
    try {
        $stmt = $pdo->prepare("SELECT user_id, username, email FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch();
            echo "<td>Yes</td>";
            echo "<td>" . $user['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars(maskEmail($user['email'])) . "</td>";
        } else {
            echo "<td>No</td>";
            echo "<td colspan='3'>User not found</td>";
        }
    } catch (PDOException $e) {
        echo "<td colspan='4'>Error: " . $e->getMessage() . "</td>";
    }
    
    echo "</tr>";
}
echo "</table>";

// Test verification code generation
echo "<h2>Testing Verification Code Generation</h2>";
echo "<p>Generated codes:</p>";
echo "<ul>";
for ($i = 0; $i < 5; $i++) {
    echo "<li>" . generateVerificationCode() . "</li>";
}
echo "</ul>";

// Show current session data
echo "<h2>Current Session Data</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Show email log if it exists
echo "<h2>Email Log</h2>";
$logFile = __DIR__ . '/logs/email_log.txt';
if (file_exists($logFile)) {
    echo "<pre>" . htmlspecialchars(file_get_contents($logFile)) . "</pre>";
} else {
    echo "<p>Email log file does not exist.</p>";
}

echo "<p><a href='forgot_account.php'>Go to Forgot Account Page</a></p>";
?>
