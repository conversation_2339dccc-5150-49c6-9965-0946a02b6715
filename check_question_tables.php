<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Database Structure Check</h1>";

try {
    // Check activity_question_options table structure
    $stmt = $pdo->query("DESCRIBE activity_question_options");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>activity_question_options Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Check for sample data in activity_question_options
    $stmt = $pdo->query("SELECT * FROM activity_question_options LIMIT 10");
    $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Sample Data in activity_question_options</h2>";
    
    if (count($options) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($options[0]) as $key) {
            echo "<th>" . $key . "</th>";
        }
        echo "</tr>";
        
        foreach ($options as $option) {
            echo "<tr>";
            foreach ($option as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No data found in activity_question_options table.</p>";
    }
    
    // Check activity_questions table structure
    $stmt = $pdo->query("DESCRIBE activity_questions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>activity_questions Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Check for sample data in activity_questions
    $stmt = $pdo->query("SELECT * FROM activity_questions LIMIT 10");
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Sample Data in activity_questions</h2>";
    
    if (count($questions) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($questions[0]) as $key) {
            echo "<th>" . $key . "</th>";
        }
        echo "</tr>";
        
        foreach ($questions as $question) {
            echo "<tr>";
            foreach ($question as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No data found in activity_questions table.</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}
?>
