<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$fileId = intval($_GET['id']);

// Get file information from database
try {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM activity_files WHERE file_id = :fileId");
    $stmt->bindParam(':fileId', $fileId);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        header("HTTP/1.0 404 Not Found");
        echo "File not found.";
        exit;
    }
    
    $fileInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    $filePath = $fileInfo['file_path'];
    $fileName = $fileInfo['file_name'];
    $fileType = $fileInfo['file_type'];
    $fileSize = $fileInfo['file_size'];
    
} catch (PDOException $e) {
    header("HTTP/1.0 500 Internal Server Error");
    echo "Database error: " . $e->getMessage();
    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found. Please contact the administrator.";
    exit;
}

// Get file extension
$fileInfo = pathinfo($filePath);
$fileExtension = isset($fileInfo['extension']) ? strtolower($fileInfo['extension']) : '';

// Set appropriate headers
header('Content-Type: ' . $fileType);
header('Content-Length: ' . $fileSize);
header('Content-Disposition: inline; filename="' . $fileName . '"');

// Output file content
readfile($filePath);
exit;
