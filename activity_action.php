<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to perform this action.";
    header("location: index.php");
    exit;
}

// Check if action and activity ID are provided
if (!isset($_GET['action']) || !isset($_GET['id'])) {
    $_SESSION['error'] = "Invalid request.";
    header("location: index.php");
    exit;
}

$action = $_GET['action'];
$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];

// Check if user has permission to modify this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($activity['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You do not have permission to modify this activity.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Process the action
switch ($action) {
    case 'delete':
        $result = deleteActivity($activityId);

        if ($result === true) {
            $_SESSION['success'] = "Activity deleted successfully.";
            header("location: course_view_full.php?id=$courseId&tab=classwork");
            exit;
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    case 'publish':
        $result = updateActivity($activityId, $activity['title'], $activity['description'], null, null, true, null, null);

        if ($result === true) {
            $_SESSION['success'] = "Activity published successfully.";
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    case 'unpublish':
        $result = updateActivity($activityId, $activity['title'], $activity['description'], null, null, false, null, null);

        if ($result === true) {
            $_SESSION['success'] = "Activity unpublished successfully.";
        } else {
            $_SESSION['error'] = $result;
        }
        break;

    default:
        $_SESSION['error'] = "Invalid action.";
        break;
}

// Redirect back to the activity page
header("location: activity_view.php?id=$activityId");
exit;
