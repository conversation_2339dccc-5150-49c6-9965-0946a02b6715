<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';

/**
 * Helper function to format file size
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size with appropriate unit
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if content ID and type are provided
if (!isset($_GET['id']) || empty($_GET['id']) || !isset($_GET['type']) || empty($_GET['type'])) {
    $_SESSION['error'] = "Content ID and type are required.";
    header("location: index.php");
    exit;
}

$contentId = intval($_GET['id']);
$contentType = $_GET['type'];

// Validate content type
$validTypes = ['assignment', 'material', 'quiz'];
if (!in_array($contentType, $validTypes)) {
    $_SESSION['error'] = "Invalid content type.";
    header("location: index.php");
    exit;
}

// Get content details based on type
$content = null;
$courseId = null;

if ($contentType == 'assignment') {
    $content = getAssignmentById($contentId);
    if (!is_string($content)) {
        $courseId = $content['course_id'];
    }
} elseif ($contentType == 'material') {
    $content = getMaterialById($contentId);
    if (!is_string($content)) {
        $courseId = $content['course_id'];
    }
} elseif ($contentType == 'quiz') {
    $content = getQuizById($contentId);
    if (!is_string($content)) {
        $courseId = $content['course_id'];
    }
}

// Check if content exists
if (is_string($content) || $content === null) {
    $_SESSION['error'] = is_string($content) ? $content : "Content not found.";
    header("location: index.php");
    exit;
}

// Get course details
$course = getCourseById($courseId);
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this course
if (!isAdmin() &&
    !isInstructor($_SESSION['user_id'], $courseId) &&
    !(isTeacher() && $course['created_by'] == $_SESSION['user_id']) &&
    !isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You do not have access to this course.";
    header("location: index.php");
    exit;
}

// For assignments, check if student has already submitted
$submission = null;
if ($contentType == 'assignment' && isStudent()) {
    $submission = getStudentSubmissionForAssignment($contentId, $_SESSION['user_id']);
    if (is_string($submission)) {
        $submission = null;
    }
}

// Initialize variables for submission
$submissionContent = "";
$submissionContent_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_assignment'])) {
    // Check if user is a student
    if (!isStudent()) {
        $_SESSION['error'] = "Only students can submit assignments.";
        header("location: content_view.php?id=$contentId&type=$contentType");
        exit;
    }

    // Validate submission content
    if (empty(trim($_POST["submission_content"]))) {
        $submissionContent_err = "Please enter your submission.";
    } else {
        $submissionContent = trim($_POST["submission_content"]);
    }

    // Check input errors before submitting the assignment
    if (empty($submissionContent_err)) {
        // Handle file upload if present
        $filePath = null;
        if (isset($_FILES['submission_file']) && $_FILES['submission_file']['error'] == 0) {
            $uploadDir = 'uploads/submissions/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = time() . '_' . $_FILES['submission_file']['name'];
            $filePath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['submission_file']['tmp_name'], $filePath)) {
                // File uploaded successfully
            } else {
                $_SESSION['error'] = "Error uploading file.";
                $filePath = null;
            }
        }

        // Submit the assignment
        $result = submitAssignment($contentId, $_SESSION['user_id'], $submissionContent, $filePath);

        if (is_numeric($result)) {
            // Submission successful
            $_SESSION['success'] = "Assignment submitted successfully.";
            header("location: content_view.php?id=$contentId&type=$contentType");
            exit;
        } else {
            // Error submitting assignment
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = htmlspecialchars($content['title']);

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Add custom styles for material content -->
<?php if ($contentType == 'material'): ?>
<style>
    .content-material {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #333;
    }

    .material-attachments .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .material-attachments .badge {
        font-size: 0.8rem;
    }
</style>
<?php endif; ?>

<!-- Content header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?php echo htmlspecialchars($content['title']); ?></h1>
    <div class="d-flex align-items-center">
        <?php if ($contentType == 'assignment'): ?>
            <span class="badge badge-primary p-2 mr-2">Assignment</span>
            <?php if (!empty($content['due_date'])): ?>
                <?php if (strtotime($content['due_date']) < time()): ?>
                    <span class="badge badge-danger p-2 mr-2">Due Date Passed</span>
                <?php else: ?>
                    <span class="badge badge-info p-2 mr-2">Due: <?php echo date('M j, Y g:i A', strtotime($content['due_date'])); ?></span>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (isTeacher() || isAdmin() || (isInstructor($_SESSION['user_id'], $courseId))): ?>
                <a href="assignment_edit.php?id=<?php echo $contentId; ?>" class="btn btn-sm btn-outline-primary ml-2">
                    <i class="fas fa-edit"></i> Edit
                </a>
            <?php endif; ?>
        <?php elseif ($contentType == 'material'): ?>
            <span class="badge badge-info p-2 mr-2">Material</span>
            <?php if (isTeacher() || isAdmin() || (isInstructor($_SESSION['user_id'], $courseId))): ?>
                <a href="material_edit.php?id=<?php echo $contentId; ?>" class="btn btn-sm btn-outline-primary ml-2">
                    <i class="fas fa-edit"></i> Edit
                </a>
            <?php endif; ?>
        <?php elseif ($contentType == 'quiz'): ?>
            <span class="badge badge-warning p-2 mr-2">Quiz</span>
            <?php if (isTeacher() || isAdmin() || (isInstructor($_SESSION['user_id'], $courseId))): ?>
                <a href="quiz_edit.php?id=<?php echo $contentId; ?>" class="btn btn-sm btn-outline-primary ml-2">
                    <i class="fas fa-edit"></i> Edit
                </a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Content details -->
<div class="card mb-4">
    <div class="card-body">
        <?php if ($contentType == 'assignment'): ?>
            <div class="mb-3">
                <h5>Instructions</h5>
                <div class="p-3 bg-light rounded">
                    <?php echo nl2br(htmlspecialchars($content['description'])); ?>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <div><strong>Points:</strong> <?php echo $content['points']; ?></div>
                <?php if (!empty($content['due_date'])): ?>
                <div><strong>Due:</strong> <?php echo date('M j, Y g:i A', strtotime($content['due_date'])); ?></div>
                <?php endif; ?>
            </div>
        <?php elseif ($contentType == 'material'): ?>
            <div class="content-material mb-4">
                <div class="p-4 bg-light rounded">
                    <?php echo nl2br(htmlspecialchars($content['content'])); ?>
                </div>
            </div>

            <?php
            // Get attached files for this material
            require_once 'includes/activity_functions.php';
            $files = getActivityFiles($contentId);
            if (!is_string($files) && count($files) > 0):
            ?>
            <div class="material-attachments mt-4">
                <h5><i class="fas fa-paperclip mr-2"></i>Attached Files</h5>
                <div class="list-group">
                    <?php foreach ($files as $file): ?>
                        <?php
                        $icon = 'file';
                        $isOfficeDoc = false;

                        if (strpos($file['file_type'], 'image') !== false) {
                            $icon = 'file-image';
                        } elseif (strpos($file['file_type'], 'pdf') !== false) {
                            $icon = 'file-pdf';
                        } elseif (strpos($file['file_type'], 'word') !== false || strpos($file['file_type'], 'document') !== false) {
                            $icon = 'file-word';
                            $isOfficeDoc = true;
                        } elseif (strpos($file['file_type'], 'excel') !== false || strpos($file['file_type'], 'spreadsheet') !== false) {
                            $icon = 'file-excel';
                            $isOfficeDoc = true;
                        } elseif (strpos($file['file_type'], 'zip') !== false || strpos($file['file_type'], 'compressed') !== false) {
                            $icon = 'file-archive';
                        } elseif (strpos($file['file_type'], 'text') !== false) {
                            $icon = 'file-alt';
                        } elseif (strpos($file['file_type'], 'powerpoint') !== false || strpos($file['file_type'], 'presentation') !== false) {
                            $icon = 'file-powerpoint';
                            $isOfficeDoc = true;
                        }

                        // Determine the appropriate viewer based on file type
                        if ($isOfficeDoc) {
                            // Office documents use the specialized preview
                            $fileUrl = "file_preview.php?file=" . urlencode($file['file_path']);
                        } else {
                            // Other files use the standard viewer
                            $fileUrl = "file_view_page.php?file=" . urlencode($file['file_path']);
                        }
                        $linkTarget = '_blank'; // Open in new tab
                        $downloadAttr = ''; // No download attribute
                        ?>
                    <a href="<?php echo $fileUrl; ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" target="<?php echo $linkTarget; ?>" <?php echo $downloadAttr; ?>>
                        <div>
                            <i class="fas fa-<?php echo $icon; ?> mr-2"></i>
                            <?php echo htmlspecialchars($file['file_name']); ?>
                        </div>
                        <div>
                            <span class="badge badge-primary badge-pill mr-2">
                                <?php echo formatFileSize($file['file_size']); ?>
                            </span>
                            <i class="fas fa-external-link-alt text-muted"></i>
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>


        <?php elseif ($contentType == 'quiz'): ?>
            <div class="mb-3">
                <h5>Quiz Description</h5>
                <div class="p-3 bg-light rounded">
                    <?php echo nl2br(htmlspecialchars($content['description'])); ?>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <div><strong>Questions:</strong> <?php echo $content['question_count']; ?></div>
                <div><strong>Points:</strong> <?php echo $content['points']; ?></div>
                <?php if (!empty($content['time_limit'])): ?>
                <div><strong>Time Limit:</strong> <?php echo $content['time_limit']; ?> minutes</div>
                <?php endif; ?>
            </div>
            <div class="mt-3">
                <a href="quiz_take.php?id=<?php echo $contentId; ?>" class="btn btn-primary">Start Quiz</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Assignment submission section (for students only) -->
<?php if ($contentType == 'assignment' && isStudent()): ?>
    <?php if ($submission): ?>
        <!-- Existing submission -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Your Submission</h5>
                <div>
                    <?php if ($submission['is_graded']): ?>
                        <span class="badge badge-success p-2">
                            <i class="fas fa-check-circle mr-1"></i> Graded: <?php echo $submission['score']; ?>/<?php echo $content['points']; ?>
                        </span>
                    <?php else: ?>
                        <span class="badge badge-secondary p-2">
                            <i class="fas fa-clock mr-1"></i> Submitted
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Submission Date:</h6>
                    <p><?php echo date('M j, Y g:i A', strtotime($submission['submission_date'])); ?></p>
                </div>
                <div class="mb-3">
                    <h6>Your Answer:</h6>
                    <div class="p-3 bg-light rounded">
                        <?php echo nl2br(htmlspecialchars($submission['content'])); ?>
                    </div>
                </div>
                <?php if (!empty($submission['file_path'])): ?>
                <div class="mb-3">
                    <h6>Attached File:</h6>
                    <?php
                    $fileExt = strtolower(pathinfo($submission['file_path'], PATHINFO_EXTENSION));
                    $isOfficeDoc = in_array($fileExt, ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']);

                    if ($isOfficeDoc):
                    ?>
                    <a href="file_viewer.php?file=<?php echo urlencode($submission['file_path']); ?>&download=true" class="btn btn-outline-primary" download>
                        <i class="fas fa-download mr-1"></i> Download Your Attachment
                    </a>
                    <?php else: ?>
                    <a href="file_view_page.php?file=<?php echo urlencode($submission['file_path']); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-file mr-1"></i> View Your Attachment
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php if ($submission['is_graded']): ?>
                <div class="mt-4">
                    <h5>Feedback from Instructor:</h5>
                    <?php if (!empty($submission['feedback'])): ?>
                    <div class="p-3 bg-light rounded">
                        <?php echo nl2br(htmlspecialchars($submission['feedback'])); ?>
                    </div>
                    <?php else: ?>
                    <p class="text-muted">No feedback provided.</p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <div class="mt-3">
                    <a href="submission_view.php?id=<?php echo $submission['submission_id']; ?>" class="btn btn-primary">View Full Submission</a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- New submission form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Submit Assignment</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $contentId . '&type=' . $contentType); ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="submission_content">Your Answer</label>
                        <textarea name="submission_content" id="submission_content" rows="6" class="form-control <?php echo (!empty($submissionContent_err)) ? 'is-invalid' : ''; ?>"><?php echo $submissionContent; ?></textarea>
                        <span class="invalid-feedback"><?php echo $submissionContent_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label for="submission_file">Attach File (optional)</label>
                        <input type="file" name="submission_file" id="submission_file" class="form-control-file">
                        <small class="form-text text-muted">Accepted file types: PDF, DOC, DOCX, TXT, JPG, PNG</small>
                    </div>
                    <div class="form-group mb-0">
                        <button type="submit" name="submit_assignment" class="btn btn-primary">Submit Assignment</button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
