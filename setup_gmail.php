<?php
/**
 * Gmail Setup Helper
 * Automatically configures email settings for Gmail
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

$success_msg = "";
$error_msg = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $gmail_email = trim($_POST["gmail_email"]);
    $gmail_password = trim($_POST["gmail_password"]);
    $from_name = trim($_POST["from_name"]);
    
    // Validate inputs
    if (empty($gmail_email) || empty($gmail_password) || empty($from_name)) {
        $error_msg = "All fields are required.";
    } elseif (!filter_var($gmail_email, FILTER_VALIDATE_EMAIL)) {
        $error_msg = "Please enter a valid Gmail address.";
    } else {
        // Update mailer configuration with Gmail settings
        $configFile = __DIR__ . '/includes/mailer_config.php';
        $configContent = file_get_contents($configFile);
        
        // Gmail SMTP settings
        $configContent = preg_replace("/define\('SMTP_HOST', '.*?'\);/", "define('SMTP_HOST', 'smtp.gmail.com');", $configContent);
        $configContent = preg_replace("/define\('SMTP_PORT', \d+\);/", "define('SMTP_PORT', 587);", $configContent);
        $configContent = preg_replace("/define\('SMTP_SECURE', '.*?'\);/", "define('SMTP_SECURE', 'tls');", $configContent);
        $configContent = preg_replace("/define\('SMTP_AUTH', (?:true|false)\);/", "define('SMTP_AUTH', true);", $configContent);
        $configContent = preg_replace("/define\('SMTP_USERNAME', '.*?'\);/", "define('SMTP_USERNAME', '$gmail_email');", $configContent);
        $configContent = preg_replace("/define\('SMTP_PASSWORD', '.*?'\);/", "define('SMTP_PASSWORD', '$gmail_password');", $configContent);
        $configContent = preg_replace("/define\('MAIL_FROM_EMAIL', .*?\);/", "define('MAIL_FROM_EMAIL', '$gmail_email');", $configContent);
        $configContent = preg_replace("/define\('MAIL_FROM_NAME', .*?\);/", "define('MAIL_FROM_NAME', '$from_name');", $configContent);
        $configContent = preg_replace("/define\('MAIL_DEBUG', \d+\);/", "define('MAIL_DEBUG', 0);", $configContent);
        
        // Write the updated configuration
        if (file_put_contents($configFile, $configContent)) {
            // Test the connection
            require_once 'includes/phpmailer_wrapper.php';
            $mailer = new PHPMailerWrapper();
            
            if ($mailer->testConnection()) {
                $success_msg = "Gmail configuration saved and tested successfully! You can now send emails.";
            } else {
                $error_msg = "Configuration saved but connection test failed. Please check your Gmail credentials and ensure you're using an App Password if 2FA is enabled.";
            }
        } else {
            $error_msg = "Failed to save configuration. Please check file permissions.";
        }
    }
}

$page_title = "Gmail Setup";
require_once 'includes/header.php';
?>

<style>
    .setup-container {
        max-width: 600px;
        margin: 50px auto;
        padding: 30px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    
    .setup-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .setup-header h1 {
        color: #4285F4;
        margin-bottom: 10px;
    }
    
    .gmail-logo {
        font-size: 3rem;
        color: #EA4335;
        margin-bottom: 20px;
    }
    
    .alert {
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .form-control {
        border-radius: 8px;
        border: 2px solid #e1e5e9;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #4285F4;
        box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
    }
    
    .btn-gmail {
        background: linear-gradient(45deg, #EA4335, #4285F4);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-gmail:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #4285F4;
        padding: 15px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }
    
    .back-link {
        color: #6c757d;
        text-decoration: none;
        margin-bottom: 20px;
        display: inline-block;
    }
    
    .back-link:hover {
        color: #4285F4;
        text-decoration: none;
    }
</style>

<div class="container">
    <a href="email_settings.php" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Email Settings
    </a>
    
    <div class="setup-container">
        <div class="setup-header">
            <div class="gmail-logo">
                <i class="fab fa-google"></i>
            </div>
            <h1>Gmail Quick Setup</h1>
            <p class="text-muted">Configure your Gmail account for sending emails</p>
        </div>
        
        <?php if (!empty($error_msg)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_msg; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success_msg)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_msg; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h6><i class="fas fa-info-circle"></i> Important Notes:</h6>
            <ul class="mb-0">
                <li>Use your Gmail address and password</li>
                <li>If you have 2-Factor Authentication enabled, create an <strong>App Password</strong></li>
                <li>Go to Google Account → Security → App passwords to create one</li>
                <li>Use the App Password instead of your regular password</li>
            </ul>
        </div>
        
        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <div class="form-group">
                <label for="gmail_email">Gmail Address</label>
                <input type="email" name="gmail_email" id="gmail_email" class="form-control" 
                       placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="gmail_password">Gmail Password / App Password</label>
                <input type="password" name="gmail_password" id="gmail_password" class="form-control" 
                       placeholder="Your Gmail password or App Password" required>
                <small class="form-text text-muted">
                    Use an App Password if you have 2-Factor Authentication enabled
                </small>
            </div>
            
            <div class="form-group">
                <label for="from_name">Sender Name</label>
                <input type="text" name="from_name" id="from_name" class="form-control" 
                       placeholder="<?php echo APP_NAME; ?>" value="<?php echo APP_NAME; ?>" required>
                <small class="form-text text-muted">
                    This name will appear as the sender in emails
                </small>
            </div>
            
            <div class="form-group text-center mt-4">
                <button type="submit" class="btn btn-gmail">
                    <i class="fas fa-cog"></i> Configure Gmail & Test Connection
                </button>
            </div>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                Need help? <a href="https://support.google.com/accounts/answer/185833" target="_blank">
                    Learn about App Passwords
                </a>
            </small>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
