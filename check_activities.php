<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin
if (!isAdmin()) {
    echo "You must be an admin to run this script.";
    exit;
}

echo "<h1>Activity Database Check</h1>";

try {
    // Check activities table
    $stmt = $pdo->query("SELECT * FROM activities ORDER BY activity_id");
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Activities in Database</h2>";
    echo "<p>Total activities found: " . count($activities) . "</p>";
    
    if (count($activities) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Course ID</th><th>Created By</th><th>Created At</th><th>Actions</th></tr>";
        
        foreach ($activities as $activity) {
            echo "<tr>";
            echo "<td>" . $activity['activity_id'] . "</td>";
            echo "<td>" . htmlspecialchars($activity['title']) . "</td>";
            echo "<td>" . $activity['activity_type'] . "</td>";
            echo "<td>" . $activity['course_id'] . "</td>";
            echo "<td>" . $activity['created_by'] . "</td>";
            echo "<td>" . $activity['created_at'] . "</td>";
            echo "<td>";
            echo "<a href='activity_view.php?id=" . $activity['activity_id'] . "'>View</a> | ";
            echo "<a href='activity_edit.php?id=" . $activity['activity_id'] . "'>Edit</a> | ";
            echo "<a href='debug_questions.php?id=" . $activity['activity_id'] . "'>Debug Questions</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Check if activity ID 6 exists
    $stmt = $pdo->prepare("SELECT * FROM activities WHERE activity_id = 6");
    $stmt->execute();
    $activity6 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Activity ID 6 Check</h2>";
    if ($activity6) {
        echo "<p>Activity ID 6 exists:</p>";
        echo "<pre>";
        print_r($activity6);
        echo "</pre>";
    } else {
        echo "<p>Activity ID 6 does not exist in the database.</p>";
    }
    
    // Check activity_questions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
    $activityQuestionsExists = $stmt->rowCount() > 0;
    
    echo "<h2>activity_questions Table Check</h2>";
    echo "<p>activity_questions table exists: " . ($activityQuestionsExists ? "Yes" : "No") . "</p>";
    
    if ($activityQuestionsExists) {
        $stmt = $pdo->query("SELECT * FROM activity_questions ORDER BY activity_id, question_id");
        $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Total questions found: " . count($questions) . "</p>";
        
        if (count($questions) > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Question ID</th><th>Activity ID</th><th>Question Text</th><th>Type</th><th>Points</th></tr>";
            
            foreach ($questions as $question) {
                echo "<tr>";
                echo "<td>" . $question['question_id'] . "</td>";
                echo "<td>" . $question['activity_id'] . "</td>";
                echo "<td>" . htmlspecialchars(substr($question['question_text'], 0, 50)) . (strlen($question['question_text']) > 50 ? '...' : '') . "</td>";
                echo "<td>" . $question['question_type'] . "</td>";
                echo "<td>" . $question['points'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    }
    
    // Check activity_question_options table
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_question_options'");
    $activityQuestionOptionsExists = $stmt->rowCount() > 0;
    
    echo "<h2>activity_question_options Table Check</h2>";
    echo "<p>activity_question_options table exists: " . ($activityQuestionOptionsExists ? "Yes" : "No") . "</p>";
    
    if ($activityQuestionOptionsExists) {
        $stmt = $pdo->query("SELECT * FROM activity_question_options ORDER BY question_id, option_id");
        $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Total options found: " . count($options) . "</p>";
        
        if (count($options) > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Option ID</th><th>Question ID</th><th>Option Text</th><th>Is Correct</th></tr>";
            
            foreach ($options as $option) {
                echo "<tr>";
                echo "<td>" . $option['option_id'] . "</td>";
                echo "<td>" . $option['question_id'] . "</td>";
                echo "<td>" . htmlspecialchars($option['option_text']) . "</td>";
                echo "<td>" . ($option['is_correct'] ? "Yes" : "No") . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
