<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    // Get a list of valid activities to suggest
    try {
        $stmt = $pdo->query("SELECT activity_id, title, activity_type FROM activities ORDER BY activity_id LIMIT 10");
        $validActivities = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($validActivities) > 0) {
            $activitySuggestions = "Available activities: ";
            foreach ($validActivities as $idx => $act) {
                if ($idx > 0) {
                    $activitySuggestions .= ", ";
                }
                $activitySuggestions .= "ID " . $act['activity_id'] . " (" . $act['title'] . ")";
            }
            $_SESSION['error'] = $activity . " " . $activitySuggestions;
        } else {
            $_SESSION['error'] = $activity . " No activities found in the database.";
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = $activity;
    }

    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit activities for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get modules for this course
$modules = getCourseModules($courseId);
if (is_string($modules)) {
    $modules = [];
}

// Get activity questions based on activity type
if ($activity['activity_type'] == 'quiz') {
    // For quiz activities, use getQuizQuestions
    $questions = getQuizQuestions($activityId);
    error_log("Quiz questions for ID $activityId: " . print_r($questions, true));
} else {
    // For regular activities, use getActivityQuestions
    $questions = getActivityQuestions($activityId);
    error_log("Activity questions for ID $activityId: " . print_r($questions, true));
}

// Handle error or empty results
if (is_string($questions)) {
    error_log("Error retrieving questions: $questions");
    $questions = [];
}

// Initialize variables
$title = $activity['title'];
$description = $activity['description'];
$activityType = $activity['activity_type'];
$points = $activity['points'];
$dueDate = $activity['due_date'];
$isPublished = $activity['is_published'];
$allowLateSubmissions = isset($activity['allow_late_submissions']) ? $activity['allow_late_submissions'] : false;
$title_err = $description_err = $points_err = $dueDate_err = "";
$success = "";

// Get existing files
$activityFiles = getActivityFiles($activityId);

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received: " . print_r($_POST, true));

    // Check which form was submitted
    if (isset($_POST['update_activity'])) {
        error_log("Processing update_activity form submission");

        // Validate title
        if (empty(trim($_POST["title"]))) {
            $title_err = "Please enter a title for the activity.";
            error_log("Title validation failed: empty title");
        } else {
            $title = trim($_POST["title"]);
            error_log("Title validated: $title");
        }

        // Validate description
        if (empty(trim($_POST["description"]))) {
            $description_err = "Please enter a description for the activity.";
            error_log("Description validation failed: empty description");
        } else {
            $description = trim($_POST["description"]);
            error_log("Description validated: " . substr($description, 0, 50) . "...");
        }

        // Validate points
        if (!isset($_POST["points"]) || !is_numeric($_POST["points"]) || $_POST["points"] < 0) {
            $points_err = "Please enter a valid number of points.";
            error_log("Points validation failed: " . (isset($_POST["points"]) ? $_POST["points"] : "not set"));
        } else {
            $points = intval($_POST["points"]);
            error_log("Points validated: $points");
        }

        // Validate due date
        if (empty($_POST["due_date"])) {
            // Due date is optional
            $dueDate = null;
            error_log("Due date is null (optional)");
        } else {
            // Format the date for MySQL
            $dueDate = date("Y-m-d H:i:s", strtotime($_POST["due_date"]));
            error_log("Due date validated: $dueDate");
        }

        // Check if the activity should be published
        $isPublished = isset($_POST["is_published"]) ? true : false;
        error_log("Is published: " . ($isPublished ? "Yes" : "No"));

        // Check if late submissions are allowed
        $allowLateSubmissions = isset($_POST["allow_late_submissions"]) ? true : false;
        error_log("Allow late submissions: " . ($allowLateSubmissions ? "Yes" : "No"));

        // File uploads have been removed

        // Check input errors before updating the activity
        if (empty($title_err) && empty($description_err) && empty($points_err) && empty($dueDate_err)) {
            error_log("All validation passed, updating activity ID: $activityId");

            // Update the activity (set points to 0)
            $result = updateActivity($activityId, $title, $description, 0, $dueDate, $isPublished, $allowLateSubmissions, null);
            error_log("Update result: " . ($result === true ? "Success" : $result));

            if ($result === true) {
                // Activity updated successfully
                $success = "Activity updated successfully!";
                error_log("Activity updated successfully!");

                // Refresh activity data
                $activity = getActivityById($activityId);
                if (is_string($activity)) {
                    $_SESSION['error'] = $activity;
                    error_log("Error refreshing activity data: " . $activity);
                    header("location: index.php");
                    exit;
                }
            } else {
                // Error occurred
                $error = $result;
                error_log("Error updating activity: " . $result);
            }
        } else {
            error_log("Validation errors: " .
                (!empty($title_err) ? "Title: $title_err " : "") .
                (!empty($description_err) ? "Description: $description_err " : "") .
                (!empty($points_err) ? "Points: $points_err " : "") .
                (!empty($dueDate_err) ? "Due Date: $dueDate_err " : "") .
                (!empty($file_err) ? "File: $file_err" : ""));
        }
    } elseif (isset($_POST['add_question'])) {
    error_log("Processing add_question form submission");
    error_log("POST data: " . print_r($_POST, true));

    $questionText = trim($_POST['question_text']);
    $questionType = $_POST['question_type'];
    $questionPoints = intval($_POST['question_points']);

    error_log("Question Text: $questionText");
    error_log("Question Type: $questionType");
    error_log("Question Points: $questionPoints");

    if (!empty($questionText) && !empty($questionType) && $questionPoints > 0) {
        if ($activityType == 'quiz') {
            error_log("Calling addQuizActivityQuestion with activityId=$activityId, questionText=$questionText, questionType=$questionType, questionPoints=$questionPoints");
            $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
            error_log("Result from addQuizActivityQuestion: " . print_r($result, true));
        } else {
            error_log("Calling addActivityQuestion with activityId=$activityId, questionText=$questionText, questionType=$questionType, questionPoints=$questionPoints");
            $result = addActivityQuestion($activityId, $questionText, $questionType, $questionPoints);
            error_log("Result from addActivityQuestion: " . print_r($result, true));
        }

        if (is_numeric($result)) {
            $questionId = $result;
            $success = "Question added successfully!";

            // Handle different question types
            if ($questionType == 'multiple_choice') {
                // Add multiple choice options
                $options = array();
                $correctAnswer = trim($_POST['mc_correct_answer']);

                // Add options A, B, C, D if they exist
                if (!empty($_POST['option_a'])) {
                    $optionA = trim($_POST['option_a']);
                    $isCorrect = ($correctAnswer == 'A');
                    $optionResult = addActivityQuestionOption($questionId, $optionA, $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add option A: " . $optionResult;
                    }
                }

                if (!empty($_POST['option_b'])) {
                    $optionB = trim($_POST['option_b']);
                    $isCorrect = ($correctAnswer == 'B');
                    $optionResult = addActivityQuestionOption($questionId, $optionB, $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add option B: " . $optionResult;
                    }
                }

                if (!empty($_POST['option_c'])) {
                    $optionC = trim($_POST['option_c']);
                    $isCorrect = ($correctAnswer == 'C');
                    $optionResult = addActivityQuestionOption($questionId, $optionC, $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add option C: " . $optionResult;
                    }
                }

                if (!empty($_POST['option_d'])) {
                    $optionD = trim($_POST['option_d']);
                    $isCorrect = ($correctAnswer == 'D');
                    $optionResult = addActivityQuestionOption($questionId, $optionD, $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add option D: " . $optionResult;
                    }
                }
            }
            elseif ($questionType == 'true_false') {
                // Add true/false options
                $correctAnswer = $_POST['tf_correct_answer'];

                // Add True option
                $isCorrect = ($correctAnswer == 'true');
                $optionResult = addActivityQuestionOption($questionId, 'True', $isCorrect);
                if (!is_numeric($optionResult)) {
                    $error = "Question added but failed to add True option: " . $optionResult;
                }

                // Add False option
                $isCorrect = ($correctAnswer == 'false');
                $optionResult = addActivityQuestionOption($questionId, 'False', $isCorrect);
                if (!is_numeric($optionResult)) {
                    $error = "Question added but failed to add False option: " . $optionResult;
                }
            }
            elseif ($questionType == 'short_answer' && isset($_POST['sa_correct_answer'])) {
                // Add short answer correct answer
                $correctAnswer = trim($_POST['sa_correct_answer']);
                $optionResult = addActivityQuestionOption($questionId, $correctAnswer, true);

                if (!is_numeric($optionResult)) {
                    $error = "Question added but failed to set correct answer: " . $optionResult;
                }
            }

            // Refresh questions based on activity type
            if ($activityType == 'quiz') {
                $questions = getQuizQuestions($activityId);
            } else {
                $questions = getActivityQuestions($activityId);
            }

            if (is_string($questions)) {
                $questions = [];
            }
        } else {
            $error = $result;
        }
    } else {
        $error = "Please fill in all question fields.";
    }
    } elseif ((isset($_GET['delete_question']) && !empty($_GET['delete_question'])) ||
             (isset($_POST['delete_question']) && !empty($_POST['delete_question']))) {

    // Get question ID from either GET or POST
    $questionId = isset($_GET['delete_question']) ?
                  intval($_GET['delete_question']) :
                  intval($_POST['delete_question']);

    error_log("Deleting question ID: $questionId for activity type: $activityType");

    // Delete question based on activity type
    if ($activityType == 'quiz') {
        $result = deleteActivityQuizQuestion($questionId);
    } else {
        $result = deleteActivityQuestion($questionId);
    }

    error_log("Delete result: " . ($result === true ? "Success" : $result));

    // Determine which page to redirect back to
    $redirectUrl = "";

    // Check if we're in activity_edit.php
    if (strpos($_SERVER['PHP_SELF'], 'activity_edit.php') !== false) {
        $redirectUrl = "activity_edit.php?id=$activityId#questions";
    }
    // Check if we're in quiz_edit.php
    else if (strpos($_SERVER['PHP_SELF'], 'quiz_edit.php') !== false) {
        $redirectUrl = "quiz_edit.php?id=$activityId#questions";
    }
    // Default fallback
    else {
        $redirectUrl = "activity_edit.php?id=$activityId#questions";
    }

    if ($result === true) {
        $_SESSION['success'] = "Question deleted successfully!";
        header("location: $redirectUrl");
        exit;
    } else {
        $_SESSION['error'] = $result;
        header("location: $redirectUrl");
        exit;
    }
}
}

// Set page title
$page_title = "Edit Activity - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($activity['title']); ?></a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Activity</h1>
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <a href="?id=<?php echo $activityId; ?>&manage_questions=1" class="btn btn-primary" id="manageQuestionsBtn">
                    <i class="fas fa-question-circle"></i> Manage Questions
                </a>
                <?php endif; ?>
            </div>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>



            <ul class="nav nav-tabs mb-4" id="activityTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="true">Activity Details</a>
                </li>
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <li class="nav-item">
                    <a class="nav-link" id="questions-tab" data-toggle="tab" href="#questions" role="tab" aria-controls="questions" aria-selected="false">Questions</a>
                </li>
                <?php endif; ?>
            </ul>

            <script>
                // Ensure tabs are properly initialized when the page loads
                $(document).ready(function() {
                    // Initialize Bootstrap tabs
                    $('#activityTabs a').on('click', function (e) {
                        e.preventDefault();
                        $(this).tab('show');
                    });

                    // Check if there's a hash in the URL to activate the correct tab
                    var hash = window.location.hash;
                    if (hash) {
                        $('#activityTabs a[href="' + hash + '"]').tab('show');
                    }

                    // Check if we should show the questions tab based on URL
                    if (window.location.href.indexOf('manage_questions=1') > -1) {
                        setTimeout(function() {
                            $('#questions-tab').tab('show');
                        }, 100);
                    }
                });
            </script>

            <div class="tab-content" id="activityTabsContent">
                <!-- Activity Details Tab -->
                <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                    <div class="card">
                        <div class="card-body">
                            <form action="activity_update_handler.php" method="post" enctype="multipart/form-data" id="update-activity-form">
                                <div class="form-group">
                                    <label for="title">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                                    <span class="invalid-feedback"><?php echo $title_err; ?></span>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description <span class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                                    <span class="invalid-feedback"><?php echo $description_err; ?></span>
                                </div>

                                <div class="form-group">
                                    <label>Activity Type</label>
                                    <div class="form-control-plaintext"><?php echo ucfirst($activityType); ?></div>
                                    <small class="form-text text-muted">Activity type cannot be changed after creation.</small>
                                </div>





                                <div class="form-group">
                                    <label for="due_date">Due Date (optional)</label>
                                    <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo !empty($dueDate) ? date('Y-m-d\TH:i', strtotime($dueDate)) : ''; ?>">
                                    <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                                    <small class="form-text text-muted">Leave blank if there is no due date.</small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" <?php echo $allowLateSubmissions ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                                        <small class="form-text text-muted">If checked, students can submit after the due date, but submissions will be marked as late.</small>
                                    </div>
                                </div>



                                <?php if (!empty($activityFiles) && !is_string($activityFiles)): ?>
                                <div class="form-group">
                                    <label>Existing Files</label>
                                    <div class="list-group">
                                        <?php foreach ($activityFiles as $file): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <a href="<?php echo htmlspecialchars($file['file_path']); ?>" target="_blank">
                                                <?php echo htmlspecialchars($file['file_name']); ?>
                                            </a>
                                            <span class="badge badge-primary badge-pill">
                                                <?php echo formatFileSize($file['file_size']); ?>
                                            </span>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="is_published">Publish this activity (visible to students)</label>
                                    </div>
                                </div>

                                <div class="form-group text-right">
                                    <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                                    <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary" id="update-activity-btn">Update Activity</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Questions Tab -->
                <?php if ($activityType == 'activity' || $activityType == 'assignment' || $activityType == 'quiz'): ?>
                <div class="tab-pane fade" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                    <div class="text-right mb-3">
                        <a href="#" class="btn btn-primary" data-toggle="collapse" data-target="#addQuestionForm">
                            <i class="fas fa-plus"></i> Add New Question
                        </a>
                    </div>

                    <div class="collapse mb-4" id="addQuestionForm">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Add New Question</h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId . '#questions'); ?>" method="post" id="questionAddForm">
                                    <div class="form-group">
                                        <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                        <textarea name="question_text" id="question_text" class="form-control" rows="3" required></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_type">Type:</label>
                                        <select name="question_type" id="question_type" class="form-control" onchange="toggleQuestionOptions()">
                                            <option value="multiple_choice">Multiple Choice</option>
                                            <option value="true_false">True/False</option>
                                            <option value="short_answer">Short Answer</option>
                                        </select>
                                    </div>

                                    <!-- Multiple Choice Options -->
                                    <div id="multiple_choice_options" class="question-options">
                                        <div class="form-group">
                                            <label for="option_a">Option A:</label>
                                            <input type="text" name="option_a" id="option_a" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_b">Option B:</label>
                                            <input type="text" name="option_b" id="option_b" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_c">Option C:</label>
                                            <input type="text" name="option_c" id="option_c" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_d">Option D:</label>
                                            <input type="text" name="option_d" id="option_d" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="mc_correct_answer">Correct Answer:</label>
                                            <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="A">Option A</option>
                                                <option value="B">Option B</option>
                                                <option value="C">Option C</option>
                                                <option value="D">Option D</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- True/False Options -->
                                    <div id="true_false_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="tf_correct_answer">Correct Answer:</label>
                                            <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="true">True</option>
                                                <option value="false">False</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Short Answer Options -->
                                    <div id="short_answer_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="sa_correct_answer">Correct Answer:</label>
                                            <input type="text" name="sa_correct_answer" id="sa_correct_answer" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_points">Points <span class="text-danger">*</span></label>
                                        <input type="number" name="question_points" id="question_points" class="form-control" value="1" min="1" required>
                                    </div>

                                    <div class="form-group text-right">
                                        <button type="button" class="btn btn-secondary mr-2" data-toggle="collapse" data-target="#addQuestionForm">Cancel</button>
                                        <input type="hidden" name="add_question" value="1">
                                        <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                                        <button type="submit" class="btn btn-success" id="add-question-btn">Add Question</button>
                                    </div>
                                </form>

                                <script>
                                    // Direct form submission without JavaScript validation
                                    document.getElementById('questionAddForm').addEventListener('submit', function(e) {
                                        console.log('Form submitted directly');
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <!-- Questions List -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Questions</h5>
                        </div>
                        <div class="list-group list-group-flush" id="questionsList">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <p class="mt-2">Loading questions...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<style>
/* Enhanced tab styling */
.nav-tabs .nav-link {
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 0;
    border-top: 3px solid transparent;
}

.nav-tabs .nav-link.active {
    border-top: 3px solid #007bff;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-top: 3px solid #cce5ff;
    background-color: #f8f9fa;
}

/* Question styling */
.list-group-item {
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.option-container {
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-container:hover {
    background-color: #f0f0f0;
}

/* Enhanced styling for correct answers */
.bg-success-light {
    background-color: rgba(40, 167, 69, 0.1);
}

.option-badge {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.border-primary {
    border-color: #007bff !important;
}

.correct-answer-summary .badge {
    font-size: 14px;
    padding: 6px 10px;
}

.text-success {
    color: #28a745 !important;
}
</style>

<script>
function toggleQuestionOptions() {
    var questionType = document.getElementById('question_type').value;
    var multipleChoiceOptions = document.getElementById('multiple_choice_options');
    var trueFalseOptions = document.getElementById('true_false_options');
    var shortAnswerOptions = document.getElementById('short_answer_options');

    // Hide all options first
    multipleChoiceOptions.style.display = 'none';
    trueFalseOptions.style.display = 'none';
    shortAnswerOptions.style.display = 'none';

    // Show the appropriate options based on question type
    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
        // Make option A and B required for multiple choice
        document.getElementById('option_a').setAttribute('required', 'required');
        document.getElementById('option_b').setAttribute('required', 'required');
        document.getElementById('mc_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'true_false') {
        trueFalseOptions.style.display = 'block';
        document.getElementById('tf_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'short_answer') {
        shortAnswerOptions.style.display = 'block';
        document.getElementById('sa_correct_answer').setAttribute('required', 'required');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleQuestionOptions();

    // Initialize Bootstrap tabs
    $('#activityTabs a[data-toggle="tab"]').tab();

    // Check if there's a hash in the URL to activate the correct tab
    var hash = window.location.hash;
    if (hash) {
        $('#activityTabs a[href="' + hash + '"]').tab('show');
    } else if (window.location.search.includes('tab=questions')) {
        // If there's a tab parameter in the URL, activate that tab
        $('#activityTabs a[href="#questions"]').tab('show');
    }

    // Add hash to URL when tab is clicked
    $('#activityTabs a').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.hash;
    });

    // Make the "Manage Questions" button work with the tabs
    $('#manageQuestionsBtn').on('click', function(e) {
        e.preventDefault();
        $('#activityTabs a[href="#questions"]').tab('show');
    });

    // Check if the URL has a query parameter to show the questions tab
    if (window.location.href.indexOf('manage_questions=1') > -1) {
        console.log('Showing questions tab');
        // Force the questions tab to be shown
        setTimeout(function() {
            $('#activityTabs a[href="#questions"]').tab('show');
            // Show the add question form automatically
            $('#addQuestionFormContainer').collapse('show');
        }, 100);
    }



    // Add form submission handler for the update activity form
    const updateForm = document.getElementById('update-activity-form');
    if (updateForm) {
        updateForm.addEventListener('submit', function(e) {
            console.log('Update form submitted to activity_update_handler.php');

            // Log form data
            const formData = new FormData(this);
            for (const pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            // Make sure activity_id field is included
            if (!formData.has('activity_id')) {
                console.error('Error: activity_id field is missing!');
                alert('Error: activity_id field is missing. Please refresh the page and try again.');
                e.preventDefault();
                return false;
            }

            // Continue with form submission
            return true;
        });
    }

    // Add click handler for the update button
    const updateBtn = document.getElementById('update-activity-btn');
    if (updateBtn) {
        updateBtn.addEventListener('click', function() {
            console.log('Update Activity button clicked');
        });
    }

    // Comment out the validation to allow direct form submission
    /*
    $('#questionAddForm').on('submit', function(e) {
        console.log('Add Question form submitted');

        // Validate the form
        var form = $(this);
        var questionText = form.find('#question_text').val();
        var questionType = form.find('#question_type').val();
        var activityId = form.find('input[name="activity_id"]').val();

        console.log('Activity ID:', activityId);
        console.log('Question Text:', questionText);
        console.log('Question Type:', questionType);

        // Make sure activity_id is included
        if (!activityId) {
            console.error('Error: activity_id field is missing!');
            alert('Error: activity_id field is missing. Please refresh the page and try again.');
            e.preventDefault();
            return false;
        }

        if (questionType === 'multiple_choice') {
            var optionA = form.find('#option_a').val();
            var optionB = form.find('#option_b').val();
            var correctAnswer = form.find('#mc_correct_answer').val();

            console.log('Option A:', optionA);
            console.log('Option B:', optionB);
            console.log('Correct Answer:', correctAnswer);

            if (!optionA || !optionB || !correctAnswer) {
                alert('Please fill in at least options A and B, and select a correct answer.');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'true_false') {
            var tfCorrectAnswer = form.find('#tf_correct_answer').val();
            console.log('TF Correct Answer:', tfCorrectAnswer);

            if (!tfCorrectAnswer) {
                alert('Please select the correct answer (True or False).');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'short_answer') {
            var saCorrectAnswer = form.find('#sa_correct_answer').val();
            console.log('SA Correct Answer:', saCorrectAnswer);

            if (!saCorrectAnswer) {
                alert('Please enter the correct answer for the short answer question.');
                e.preventDefault();
                return false;
            }
        }

        // If all validations pass, submit the form
        console.log('Form validation passed, submitting...');
        return true;
    });
    */

    // Add click handler for the Add Question button
    $('#add-question-btn').on('click', function() {
        console.log('Add Question button clicked');

        // Basic validation
        var questionText = $('#question_text').val().trim();
        var questionType = $('#question_type').val();
        var questionPoints = $('#question_points').val();

        if (!questionText) {
            alert('Please enter question text');
            return false;
        }

        if (!questionPoints || questionPoints < 1) {
            alert('Please enter valid points (minimum 1)');
            return false;
        }

        if (questionType === 'multiple_choice') {
            var optionA = $('#option_a').val().trim();
            var optionB = $('#option_b').val().trim();
            var correctAnswer = $('#mc_correct_answer').val();

            if (!optionA || !optionB) {
                alert('Please fill in at least options A and B');
                return false;
            }

            if (!correctAnswer) {
                alert('Please select the correct answer');
                return false;
            }
        } else if (questionType === 'true_false') {
            var tfCorrectAnswer = $('#tf_correct_answer').val();

            if (!tfCorrectAnswer) {
                alert('Please select the correct answer (True or False)');
                return false;
            }
        } else if (questionType === 'short_answer') {
            var saCorrectAnswer = $('#sa_correct_answer').val().trim();

            if (!saCorrectAnswer) {
                alert('Please enter the correct answer for the short answer question');
                return false;
            }
        }

        // If validation passes, submit the form
        $('#questionAddForm').submit();
    });





    // Make sure the tabs are properly initialized
    $('#activityTabs a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Initialize Bootstrap tabs
    $('#activityTabs a[data-toggle="tab"]').tab();

    // Check if there's a hash in the URL to activate the correct tab
    var hash = window.location.hash;
    if (hash) {
        $('#activityTabs a[href="' + hash + '"]').tab('show');
    } else if (window.location.search.includes('tab=questions')) {
        // If there's a tab parameter in the URL, activate that tab
        $('#activityTabs a[href="#questions"]').tab('show');
    }

    // Add hash to URL when tab is clicked
    $('#activityTabs a').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.hash;
    });



    // Make the "Manage Questions" button work with the tabs
    $('#manageQuestionsBtn').on('click', function(e) {
        e.preventDefault();
        $('#activityTabs a[href="#questions"]').tab('show');
    });

    // Check if the URL has a query parameter to show the questions tab
    if (window.location.href.indexOf('manage_questions=1') > -1) {
        console.log('Showing questions tab');
        // Force the questions tab to be shown
        setTimeout(function() {
            $('#activityTabs a[href="#questions"]').tab('show');
            // Show the add question form automatically
            $('#addQuestionForm').collapse('show');
        }, 100);
    }

    // Add click handler for the Add Question button
    $('#add-question-btn').on('click', function() {
        console.log('Add Question button clicked');
        // Make sure the form submits directly
        $('#questionAddForm').submit();
    });

    // Ensure questions are loaded when the page loads
    $(document).ready(function() {
        // If we're on the questions tab, load questions immediately
        if (window.location.hash === '#questions' ||
            window.location.search.includes('tab=questions') ||
            window.location.search.includes('manage_questions=1')) {
            setTimeout(function() {
                loadQuestions();
            }, 300);
        }

        // Add tab change event to load questions when the questions tab is shown
        $('#activityTabs a[href="#questions"]').on('shown.bs.tab', function (e) {
            loadQuestions();
        });
    });

    // Load questions via AJAX
    function loadQuestions() {
        console.log('Loading questions for activity ID: <?php echo $activityId; ?>');
        $('#questionsList').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div><p class="mt-2">Loading questions...</p></div>');

        // Set a timeout to handle cases where the request hangs
        var questionLoadTimeout = setTimeout(function() {
            $('#questionsList').html('<div class="alert alert-warning m-3">Loading questions is taking longer than expected. <button class="btn btn-sm btn-primary ml-2" onclick="loadQuestions()">Try Again</button></div>');
        }, 10000); // 10 seconds timeout

        $.ajax({
            url: 'get_activity_questions.php',
            type: 'GET',
            data: { id: <?php echo $activityId; ?> },
            timeout: 15000, // 15 seconds timeout
            success: function(response) {
                clearTimeout(questionLoadTimeout);
                $('#questionsList').html(response);
                console.log('Questions loaded successfully');
            },
            error: function(xhr, status, error) {
                clearTimeout(questionLoadTimeout);
                if (status === 'timeout') {
                    $('#questionsList').html('<div class="alert alert-danger m-3">Request timed out. Please try again. <button class="btn btn-sm btn-primary ml-2" onclick="loadQuestions()">Try Again</button></div>');
                } else {
                    $('#questionsList').html('<div class="alert alert-danger m-3">Error loading questions: ' + error + ' <button class="btn btn-sm btn-primary ml-2" onclick="loadQuestions()">Try Again</button></div>');
                }
                console.error('Error loading questions:', status, error);
            }
        });
    }

    // Load questions when the questions tab is shown
    $('#questions-tab').on('shown.bs.tab', function (e) {
        loadQuestions();
    });

    // Always load questions when the questions tab is active
    if ($('#questions-tab').hasClass('active') || window.location.hash === '#questions' || window.location.search.includes('manage_questions=1')) {
        loadQuestions();
    } else {
        // Force load questions when the questions tab becomes visible
        setTimeout(function() {
            loadQuestions();
        }, 500);
    }
});
</script>
