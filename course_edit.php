<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    // Course not found or error occurred
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this course
if (!isAdmin() && !(isTeacher() && $course['created_by'] == $_SESSION['user_id'])) {
    $_SESSION['error'] = "You are not authorized to edit this course.";
    header("location: index.php");
    exit;
}

// Get all teachers for admin
$teachers = [];
if (isAdmin()) {
    $teachers = getUsersByRole('teacher');
    if (is_string($teachers)) {
        $teacherError = $teachers;
        $teachers = [];
    }
}

// Get course instructors
$instructors = getCourseInstructors($courseId);
if (is_string($instructors)) {
    $instructorError = $instructors;
    $instructors = [];
}

// Initialize variables
$title = $course['title'];
$description = $course['description'];
$classCode = $course['class_code'];
$capacity = isset($course['capacity']) ? $course['capacity'] : null;
$semester = isset($course['semester']) ? $course['semester'] : null;
$title_err = $description_err = $class_code_err = $capacity_err = $semester_err = "";
$success = "";
$isActive = $course['is_active'];

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title for the course.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description for the course.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Get active status if admin
    if (isAdmin() && isset($_POST['is_active'])) {
        $isActive = $_POST['is_active'] ? 1 : 0;
    }

    // Get class code if provided
    $classCode = null;
    if (isset($_POST["class_code"]) && !empty(trim($_POST["class_code"]))) {
        $classCode = strtoupper(trim($_POST["class_code"]));
        // Validate class code format (alphanumeric, 6 characters)
        if (!preg_match('/^[A-Z0-9]{6}$/', $classCode)) {
            $class_code_err = "Class code must be 6 alphanumeric characters.";
        }
    }

    // Get capacity if provided
    $capacity = null;
    if (isset($_POST["capacity"]) && !empty(trim($_POST["capacity"]))) {
        $capacity = (int)trim($_POST["capacity"]);
        // Validate capacity (must be a positive integer)
        if ($capacity <= 0) {
            $capacity_err = "Capacity must be a positive number.";
        }
    }

    // Get semester if provided
    $semester = null;
    if (isset($_POST["semester"]) && !empty(trim($_POST["semester"]))) {
        $semester = trim($_POST["semester"]);
        // Validate semester (must be 'first' or 'second')
        if (!in_array($semester, ['first', 'second'])) {
            $semester_err = "Semester must be either 'First Semester' or 'Second Semester'.";
        }
    }

    // Check input errors before updating the course
    if (empty($title_err) && empty($description_err) && empty($class_code_err) && empty($capacity_err) && empty($semester_err)) {
        // Update the course
        $result = updateCourse($courseId, $title, $description, $classCode, $isActive, $capacity, $semester);

        if ($result === true) {
            // Course updated successfully
            $_SESSION['success'] = "Course updated successfully!";

            // Redirect to course view page
            header("location: course_view_full.php?id=$courseId");
            exit;
        } else {
            // Error occurred
            $error = $result;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Course - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/classroom.css">
</head>
<body>
    <!-- Header -->
    <header class="classroom-header">
        <a href="index.php" class="classroom-logo">
            <i class="fas fa-graduation-cap"></i>
            <?php echo APP_NAME; ?>
        </a>

        <div class="classroom-header-right">
            <button class="btn-icon" title="Help">
                <i class="fas fa-question-circle"></i>
            </button>
            <div class="user-avatar" title="<?php echo htmlspecialchars($_SESSION["username"]); ?>">
                <?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?>
            </div>
        </div>
    </header>

    <div class="classroom-container">
        <!-- Sidebar -->
        <aside class="classroom-sidebar">
            <ul class="classroom-sidebar-nav">
                <li class="classroom-sidebar-item">
                    <a href="index.php" class="classroom-sidebar-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="calendar.php" class="classroom-sidebar-link">
                        <i class="fas fa-calendar-alt"></i> Calendar
                    </a>
                </li>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="courses.php" class="classroom-sidebar-link active">
                        <i class="fas fa-book"></i> Courses
                    </a>
                </li>
                <?php endif; ?>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="users.php" class="classroom-sidebar-link">
                        <i class="fas fa-users"></i> Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="classroom-sidebar-item">
                    <a href="profile.php" class="classroom-sidebar-link">
                        <i class="fas fa-user"></i> Profile
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="logout.php" class="classroom-sidebar-link">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main content -->
        <main class="classroom-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Course</h1>
            </div>

            <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
                <div class="mt-2">
                    <a href="course_view.php?id=<?php echo $courseId; ?>" class="btn-classroom-outline btn-sm">View Course</a>
                    <a href="index.php" class="btn-classroom-outline btn-sm ml-2">Go to Home</a>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $courseId); ?>" method="post">
                        <div class="form-group">
                            <label for="title">Course Name <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                            <span class="invalid-feedback"><?php echo $title_err; ?></span>
                        </div>
                        <div class="form-group">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea name="description" id="description" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                            <span class="invalid-feedback"><?php echo $description_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label for="class_code">Class Code</label>
                            <div class="input-group">
                                <input type="text" name="class_code" id="class_code" class="form-control <?php echo (!empty($class_code_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($course['class_code']); ?>" maxlength="6">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="generateCode">
                                        <i class="fas fa-sync-alt"></i> Generate New
                                    </button>
                                </div>
                            </div>
                            <span class="invalid-feedback"><?php echo $class_code_err; ?></span>
                            <small class="form-text text-muted">Students can use this code to join your course. Changing this code will not affect students who have already joined.</small>
                        </div>

                        <div class="form-group">
                            <label for="capacity">Slot/Capacity</label>
                            <input type="number" name="capacity" id="capacity" class="form-control <?php echo (!empty($capacity_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($capacity ?? ''); ?>" placeholder="Maximum number of students" min="1">
                            <span class="invalid-feedback"><?php echo $capacity_err; ?></span>
                            <small class="form-text text-muted">Maximum number of students who can enroll in this course. Leave blank for unlimited.</small>
                        </div>

                        <div class="form-group">
                            <label for="semester">Semester</label>
                            <select name="semester" id="semester" class="form-control <?php echo (!empty($semester_err)) ? 'is-invalid' : ''; ?>">
                                <option value="">Select Semester</option>
                                <option value="first" <?php echo ($semester == 'first') ? 'selected' : ''; ?>>First Semester</option>
                                <option value="second" <?php echo ($semester == 'second') ? 'selected' : ''; ?>>Second Semester</option>
                            </select>
                            <span class="invalid-feedback"><?php echo $semester_err; ?></span>
                        </div>

                        <?php if (isAdmin()): ?>
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" <?php echo $isActive ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Inactive courses are not visible to students.</small>
                        </div>
                        <?php endif; ?>

                        <div class="form-group text-right">
                            <a href="course_view_full.php?id=<?php echo $courseId; ?>" class="btn-classroom-outline mr-2">Cancel</a>
                            <button type="submit" class="btn-classroom">Update</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
    // Function to generate a random class code
    function generateRandomCode(length = 6) {
        const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let code = '';

        for (let i = 0; i < length; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
        }

        return code;
    }

    // Handle generate code button click
    document.getElementById('generateCode').addEventListener('click', function() {
        document.getElementById('class_code').value = generateRandomCode();
    });
    </script>
</body>
</html>
