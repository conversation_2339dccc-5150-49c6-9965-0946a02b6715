-- Normalized E-Learning Database Schema
-- Follows 1NF, 2NF, and 3NF normalization principles
-- Eliminates redundancy and ensures data integrity

-- =====================================================
-- LOOKUP TABLES (3NF - Separate reference data)
-- =====================================================

-- Roles lookup table
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_description TEXT,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User status lookup table
CREATE TABLE IF NOT EXISTS user_statuses (
    status_id INT AUTO_INCREMENT PRIMARY KEY,
    status_name VARCHAR(30) NOT NULL UNIQUE,
    status_description VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE
);

-- Course categories lookup table
CREATE TABLE IF NOT EXISTS course_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    category_description TEXT,
    parent_category_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_category_id) REFERENCES course_categories(category_id) ON DELETE SET NULL
);

-- Semesters lookup table
CREATE TABLE IF NOT EXISTS semesters (
    semester_id INT AUTO_INCREMENT PRIMARY KEY,
    semester_name VARCHAR(50) NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_semester_year (semester_name, academic_year)
);

-- Activity types lookup table
CREATE TABLE IF NOT EXISTS activity_types (
    type_id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    type_description TEXT,
    default_points INT DEFAULT 0,
    allows_submissions BOOLEAN DEFAULT TRUE,
    allows_grading BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Question types lookup table
CREATE TABLE IF NOT EXISTS question_types (
    type_id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    type_description TEXT,
    has_options BOOLEAN DEFAULT FALSE,
    max_options INT DEFAULT NULL,
    allows_multiple_answers BOOLEAN DEFAULT FALSE
);

-- File types lookup table
CREATE TABLE IF NOT EXISTS file_types (
    type_id INT AUTO_INCREMENT PRIMARY KEY,
    mime_type VARCHAR(100) NOT NULL UNIQUE,
    extension VARCHAR(10) NOT NULL,
    category VARCHAR(50) NOT NULL,
    max_size_mb INT DEFAULT 10,
    is_allowed BOOLEAN DEFAULT TRUE
);

-- =====================================================
-- CORE ENTITY TABLES (2NF - Proper primary keys)
-- =====================================================

-- Normalized users table
CREATE TABLE IF NOT EXISTS users_normalized (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    status_id INT NOT NULL DEFAULT 1,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(role_id),
    FOREIGN KEY (status_id) REFERENCES user_statuses(status_id),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role_id),
    INDEX idx_last_login (last_login)
);

-- User profiles table (1NF - Separate repeating groups)
CREATE TABLE IF NOT EXISTS user_profiles (
    profile_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50) NULL,
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') NULL,
    date_of_birth DATE NULL,
    phone_number VARCHAR(20) NULL,
    bio TEXT NULL,
    profile_picture_url VARCHAR(255) NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language_preference VARCHAR(10) DEFAULT 'en',
    notification_preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    INDEX idx_full_name (first_name, last_name)
);

-- User addresses table (3NF - Separate address data)
CREATE TABLE IF NOT EXISTS user_addresses (
    address_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    address_type ENUM('home', 'work', 'billing', 'shipping') DEFAULT 'home',
    street_address VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    INDEX idx_user_address (user_id, address_type)
);

-- Normalized courses table
CREATE TABLE IF NOT EXISTS courses_normalized (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INT NULL,
    semester_id INT NULL,
    instructor_id INT NOT NULL,
    capacity_limit INT DEFAULT NULL,
    enrollment_start_date TIMESTAMP NULL,
    enrollment_end_date TIMESTAMP NULL,
    course_start_date DATE NULL,
    course_end_date DATE NULL,
    is_published BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    requires_approval BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES course_categories(category_id) ON DELETE SET NULL,
    FOREIGN KEY (semester_id) REFERENCES semesters(semester_id) ON DELETE SET NULL,
    FOREIGN KEY (instructor_id) REFERENCES users_normalized(user_id),
    INDEX idx_course_code (course_code),
    INDEX idx_instructor (instructor_id),
    INDEX idx_category (category_id),
    INDEX idx_semester (semester_id),
    INDEX idx_published (is_published),
    INDEX idx_archived (is_archived)
);

-- Course instructors table (Many-to-Many relationship)
CREATE TABLE IF NOT EXISTS course_instructors (
    course_id INT NOT NULL,
    instructor_id INT NOT NULL,
    role_type ENUM('primary', 'assistant', 'guest') DEFAULT 'primary',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT NOT NULL,
    PRIMARY KEY (course_id, instructor_id),
    FOREIGN KEY (course_id) REFERENCES courses_normalized(course_id) ON DELETE CASCADE,
    FOREIGN KEY (instructor_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users_normalized(user_id),
    INDEX idx_instructor_courses (instructor_id)
);

-- Normalized enrollments table
CREATE TABLE IF NOT EXISTS enrollments_normalized (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_status ENUM('pending', 'active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_date TIMESTAMP NULL,
    final_grade DECIMAL(5,2) NULL,
    grade_letter VARCHAR(2) NULL,
    is_archived BOOLEAN DEFAULT FALSE,
    notes TEXT NULL,
    enrolled_by INT NULL,
    UNIQUE KEY unique_enrollment (student_id, course_id),
    FOREIGN KEY (student_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses_normalized(course_id) ON DELETE CASCADE,
    FOREIGN KEY (enrolled_by) REFERENCES users_normalized(user_id) ON DELETE SET NULL,
    INDEX idx_student_enrollments (student_id),
    INDEX idx_course_enrollments (course_id),
    INDEX idx_enrollment_status (enrollment_status),
    INDEX idx_enrollment_date (enrollment_date)
);

-- =====================================================
-- CONTENT AND ACTIVITY TABLES
-- =====================================================

-- Normalized activities table
CREATE TABLE IF NOT EXISTS activities_normalized (
    activity_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    type_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    points_possible INT DEFAULT 0,
    due_date TIMESTAMP NULL,
    available_from TIMESTAMP NULL,
    available_until TIMESTAMP NULL,
    time_limit_minutes INT NULL,
    attempts_allowed INT DEFAULT 1,
    show_correct_answers BOOLEAN DEFAULT TRUE,
    randomize_questions BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,
    is_archived BOOLEAN DEFAULT FALSE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses_normalized(course_id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES activity_types(type_id),
    FOREIGN KEY (created_by) REFERENCES users_normalized(user_id),
    INDEX idx_course_activities (course_id),
    INDEX idx_activity_type (type_id),
    INDEX idx_due_date (due_date),
    INDEX idx_published (is_published),
    INDEX idx_created_by (created_by)
);

-- Activity files table (1NF - Separate file attachments)
CREATE TABLE IF NOT EXISTS activity_files (
    file_id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (activity_id) REFERENCES activities_normalized(activity_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users_normalized(user_id),
    INDEX idx_activity_files (activity_id),
    INDEX idx_file_hash (file_hash)
);

-- Insert default lookup data
INSERT IGNORE INTO roles (role_name, role_description) VALUES
('admin', 'System administrator with full access'),
('instructor', 'Course instructor who can create and manage courses'),
('student', 'Student who can enroll in courses and submit assignments');

INSERT IGNORE INTO user_statuses (status_name, status_description) VALUES
('active', 'Active user account'),
('inactive', 'Temporarily disabled account'),
('suspended', 'Suspended user account'),
('pending', 'Pending email verification');

INSERT IGNORE INTO activity_types (type_name, type_description, allows_submissions, allows_grading) VALUES
('material', 'Course material or resource', FALSE, FALSE),
('assignment', 'Assignment requiring submission', TRUE, TRUE),
('quiz', 'Quiz or test with questions', TRUE, TRUE),
('discussion', 'Discussion forum topic', TRUE, FALSE),
('announcement', 'Course announcement', FALSE, FALSE);

INSERT IGNORE INTO question_types (type_name, type_description, has_options, max_options, allows_multiple_answers) VALUES
('multiple_choice', 'Multiple choice question', TRUE, 10, FALSE),
('multiple_select', 'Multiple selection question', TRUE, 10, TRUE),
('true_false', 'True or false question', TRUE, 2, FALSE),
('short_answer', 'Short text answer', FALSE, NULL, FALSE),
('essay', 'Long text answer', FALSE, NULL, FALSE),
('fill_blank', 'Fill in the blank', FALSE, NULL, FALSE);

INSERT IGNORE INTO file_types (mime_type, extension, category, max_size_mb, is_allowed) VALUES
('image/jpeg', 'jpg', 'image', 5, TRUE),
('image/png', 'png', 'image', 5, TRUE),
('image/gif', 'gif', 'image', 5, TRUE),
('application/pdf', 'pdf', 'document', 25, TRUE),
('application/msword', 'doc', 'document', 25, TRUE),
('application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'docx', 'document', 25, TRUE),
('text/plain', 'txt', 'text', 5, TRUE),
('application/zip', 'zip', 'archive', 50, TRUE);

-- =====================================================
-- QUESTIONS AND ASSESSMENTS TABLES
-- =====================================================

-- Normalized questions table
CREATE TABLE IF NOT EXISTS questions_normalized (
    question_id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    type_id INT NOT NULL,
    question_text TEXT NOT NULL,
    question_explanation TEXT NULL,
    points_possible DECIMAL(8,2) DEFAULT 1.00,
    position_order INT DEFAULT 0,
    is_required BOOLEAN DEFAULT TRUE,
    correct_answer_text TEXT NULL,
    case_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities_normalized(activity_id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES question_types(type_id),
    INDEX idx_activity_questions (activity_id),
    INDEX idx_question_type (type_id),
    INDEX idx_position (position_order)
);

-- Question options table (for multiple choice, etc.)
CREATE TABLE IF NOT EXISTS question_options (
    option_id INT AUTO_INCREMENT PRIMARY KEY,
    question_id INT NOT NULL,
    option_text TEXT NOT NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    position_order INT DEFAULT 0,
    explanation TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions_normalized(question_id) ON DELETE CASCADE,
    INDEX idx_question_options (question_id),
    INDEX idx_position (position_order)
);

-- =====================================================
-- SUBMISSIONS AND GRADING TABLES
-- =====================================================

-- Normalized submissions table
CREATE TABLE IF NOT EXISTS submissions_normalized (
    submission_id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    student_id INT NOT NULL,
    attempt_number INT DEFAULT 1,
    submission_status ENUM('draft', 'submitted', 'graded', 'returned') DEFAULT 'draft',
    submitted_at TIMESTAMP NULL,
    graded_at TIMESTAMP NULL,
    graded_by INT NULL,
    points_earned DECIMAL(8,2) NULL,
    points_possible DECIMAL(8,2) NULL,
    percentage_score DECIMAL(5,2) NULL,
    letter_grade VARCHAR(2) NULL,
    feedback TEXT NULL,
    is_late BOOLEAN DEFAULT FALSE,
    time_spent_minutes INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_submission_attempt (activity_id, student_id, attempt_number),
    FOREIGN KEY (activity_id) REFERENCES activities_normalized(activity_id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users_normalized(user_id) ON DELETE SET NULL,
    INDEX idx_activity_submissions (activity_id),
    INDEX idx_student_submissions (student_id),
    INDEX idx_submission_status (submission_status),
    INDEX idx_submitted_at (submitted_at)
);

-- Submission files table
CREATE TABLE IF NOT EXISTS submission_files (
    file_id INT AUTO_INCREMENT PRIMARY KEY,
    submission_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (submission_id) REFERENCES submissions_normalized(submission_id) ON DELETE CASCADE,
    INDEX idx_submission_files (submission_id),
    INDEX idx_file_hash (file_hash)
);

-- Question answers table
CREATE TABLE IF NOT EXISTS question_answers (
    answer_id INT AUTO_INCREMENT PRIMARY KEY,
    submission_id INT NOT NULL,
    question_id INT NOT NULL,
    selected_option_id INT NULL,
    answer_text TEXT NULL,
    is_correct BOOLEAN NULL,
    points_earned DECIMAL(8,2) NULL,
    feedback TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_question_answer (submission_id, question_id),
    FOREIGN KEY (submission_id) REFERENCES submissions_normalized(submission_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions_normalized(question_id) ON DELETE CASCADE,
    FOREIGN KEY (selected_option_id) REFERENCES question_options(option_id) ON DELETE SET NULL,
    INDEX idx_submission_answers (submission_id),
    INDEX idx_question_answers (question_id)
);

-- =====================================================
-- COMMUNICATION TABLES
-- =====================================================

-- Announcements table
CREATE TABLE IF NOT EXISTS announcements_normalized (
    announcement_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,
    publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses_normalized(course_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users_normalized(user_id),
    INDEX idx_course_announcements (course_id),
    INDEX idx_published (is_published),
    INDEX idx_publish_date (publish_date)
);

-- Comments table (for activities, announcements, etc.)
CREATE TABLE IF NOT EXISTS comments_normalized (
    comment_id INT AUTO_INCREMENT PRIMARY KEY,
    commentable_type ENUM('activity', 'announcement', 'submission') NOT NULL,
    commentable_id INT NOT NULL,
    parent_comment_id INT NULL,
    user_id INT NOT NULL,
    comment_text TEXT NOT NULL,
    is_private BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_comment_id) REFERENCES comments_normalized(comment_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    INDEX idx_commentable (commentable_type, commentable_id),
    INDEX idx_parent_comment (parent_comment_id),
    INDEX idx_user_comments (user_id),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- SYSTEM TABLES
-- =====================================================

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications_normalized (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    related_type VARCHAR(50) NULL,
    related_id INT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_normalized(user_id) ON DELETE CASCADE,
    INDEX idx_user_notifications (user_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings_normalized (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    setting_description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_normalized(user_id) ON DELETE SET NULL,
    INDEX idx_user_actions (user_id),
    INDEX idx_action (action),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
);
