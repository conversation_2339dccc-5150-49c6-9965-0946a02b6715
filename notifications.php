<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/notifications.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Notifications";

// Handle mark as read
if (isset($_POST['mark_read'])) {
    $notificationId = intval($_POST['notification_id']);
    if (markNotificationAsRead($notificationId, $_SESSION['user_id'])) {
        $_SESSION['success'] = "Notification marked as read.";
    } else {
        $_SESSION['error'] = "Failed to mark notification as read.";
    }
    header("location: notifications.php");
    exit;
}

// Handle mark all as read
if (isset($_POST['mark_all_read'])) {
    if (markAllNotificationsAsRead($_SESSION['user_id'])) {
        $_SESSION['success'] = "All notifications marked as read.";
    } else {
        $_SESSION['error'] = "Failed to mark all notifications as read.";
    }
    header("location: notifications.php");
    exit;
}

// Get notifications
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$notifications = getAllNotifications($_SESSION['user_id'], $limit, $offset);
$unreadCount = countUnreadNotifications($_SESSION['user_id']);

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid pt-5 pb-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Notifications</h1>
        </div>
        <?php if (count($notifications) > 0): ?>
        <div class="col-auto">
            <form method="post" action="">
                <button type="submit" name="mark_all_read" class="btn btn-sm btn-outline-primary">Mark All as Read</button>
            </form>
        </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body p-0">
                    <?php if (count($notifications) > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="list-group-item <?php echo $notification['is_read'] ? 'bg-white' : 'bg-light'; ?> border-left-0 border-right-0">
                                    <div class="d-flex align-items-center mb-2">
                                        <?php
                                        // Default icon
                                        $iconClass = 'fa-bell';
                                        $iconColor = 'text-primary';

                                        // Set icon based on notification type
                                        switch ($notification['type']) {
                                            // Calendar notifications
                                            case 'event_creation':
                                            case 'event_update':
                                            case 'event_reminder':
                                                $iconClass = 'fa-calendar-alt';
                                                $iconColor = 'text-primary';
                                                break;

                                            // Activity notifications
                                            case 'activity_created':
                                            case 'activity_updated':
                                                $iconClass = 'fa-tasks';
                                                $iconColor = 'text-info';
                                                break;

                                            case 'assignment_created':
                                            case 'assignment_updated':
                                                $iconClass = 'fa-clipboard-list';
                                                $iconColor = 'text-success';
                                                break;

                                            case 'quiz_created':
                                            case 'quiz_updated':
                                                $iconClass = 'fa-question-circle';
                                                $iconColor = 'text-warning';
                                                break;

                                            // Enrollment notifications
                                            case 'enrollment_approved':
                                                $iconClass = 'fa-user-check';
                                                $iconColor = 'text-success';
                                                break;

                                            case 'enrollment_rejected':
                                                $iconClass = 'fa-user-times';
                                                $iconColor = 'text-danger';
                                                break;

                                            case 'enrollment_request':
                                                $iconClass = 'fa-user-plus';
                                                $iconColor = 'text-info';
                                                break;

                                            // System notifications
                                            case 'system':
                                                $iconClass = 'fa-cog';
                                                $iconColor = 'text-secondary';
                                                break;

                                            // Status change
                                            case 'status_change':
                                                $iconClass = 'fa-exchange-alt';
                                                $iconColor = 'text-info';
                                                break;
                                        }

                                        switch ($notification['type']) {
                                            case 'event_creation':
                                                $iconClass = 'fa-calendar-plus';
                                                $iconColor = 'text-success';
                                                break;
                                            case 'event_update':
                                                $iconClass = 'fa-calendar-check';
                                                $iconColor = 'text-primary';
                                                break;
                                            case 'event_reminder':
                                                $iconClass = 'fa-clock';
                                                $iconColor = 'text-warning';
                                                break;
                                            case 'status_change':
                                                $iconClass = 'fa-check-circle';
                                                $iconColor = 'text-info';
                                                break;
                                            case 'system':
                                                $iconClass = 'fa-cog';
                                                $iconColor = 'text-secondary';
                                                break;
                                        }
                                        ?>
                                        <div class="notification-icon mr-3">
                                            <i class="fas <?php echo $iconClass; ?> <?php echo $iconColor; ?> fa-lg"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1 font-weight-bold"><?php echo htmlspecialchars($notification['title']); ?></h5>
                                            <p class="mb-1"><?php echo htmlspecialchars($notification['message']); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <?php
                                                    $typeLabels = [
                                                        // Calendar notifications
                                                        'event_creation' => 'Event Created',
                                                        'event_update' => 'Event Updated',
                                                        'event_reminder' => 'Event Reminder',
                                                        'status_change' => 'Status Changed',
                                                        'system' => 'System',

                                                        // Activity notifications
                                                        'activity_created' => 'Activity Created',
                                                        'activity_updated' => 'Activity Updated',
                                                        'assignment_created' => 'Assignment Created',
                                                        'assignment_updated' => 'Assignment Updated',
                                                        'quiz_created' => 'Quiz Created',
                                                        'quiz_updated' => 'Quiz Updated',

                                                        // Enrollment notifications
                                                        'enrollment_approved' => 'Enrollment Approved',
                                                        'enrollment_rejected' => 'Enrollment Rejected',
                                                        'enrollment_request' => 'Enrollment Request'
                                                    ];
                                                    echo isset($typeLabels[$notification['type']]) ? $typeLabels[$notification['type']] : $notification['type'];
                                                    ?> • <?php echo date('M j, Y g:i a', strtotime($notification['created_at'])); ?>
                                                </small>
                                                <?php if (!$notification['is_read']): ?>
                                                <form method="post" action="">
                                                    <input type="hidden" name="notification_id" value="<?php echo $notification['notification_id']; ?>">
                                                    <button type="submit" name="mark_read" class="btn btn-sm btn-outline-secondary">Mark as Read</button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5>No Notifications</h5>
                            <p class="text-muted">You don't have any notifications yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Notification Settings</h5>
                </div>
                <div class="card-body">
                    <p>Control which notifications you receive in your <a href="settings.php" class="text-primary">settings</a>.</p>

                    <div class="list-group list-group-flush mt-3">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-calendar-plus text-success mr-2"></i>
                                    Event Creation
                                </div>
                                <?php
                                // Include notifications functions if not already included
                                if (!function_exists('shouldSendNotification')) {
                                    require_once 'includes/notifications.php';
                                }
                                $eventCreationEnabled = shouldSendNotification($_SESSION['user_id'], 'event_creation');
                                ?>
                                <span class="badge badge-<?php echo $eventCreationEnabled ? 'success' : 'secondary'; ?>">
                                    <?php echo $eventCreationEnabled ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-calendar-check text-primary mr-2"></i>
                                    Event Updates
                                </div>
                                <?php $eventUpdatesEnabled = shouldSendNotification($_SESSION['user_id'], 'event_update'); ?>
                                <span class="badge badge-<?php echo $eventUpdatesEnabled ? 'success' : 'secondary'; ?>">
                                    <?php echo $eventUpdatesEnabled ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-check-circle text-info mr-2"></i>
                                    Status Changes
                                </div>
                                <?php $statusChangesEnabled = shouldSendNotification($_SESSION['user_id'], 'status_change'); ?>
                                <span class="badge badge-<?php echo $statusChangesEnabled ? 'success' : 'secondary'; ?>">
                                    <?php echo $statusChangesEnabled ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-clock text-warning mr-2"></i>
                                    Event Reminders
                                </div>
                                <?php $eventRemindersEnabled = shouldSendNotification($_SESSION['user_id'], 'event_reminder'); ?>
                                <span class="badge badge-<?php echo $eventRemindersEnabled ? 'success' : 'secondary'; ?>">
                                    <?php echo $eventRemindersEnabled ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="settings.php" class="btn btn-outline-primary btn-block">Manage Notification Settings</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<?php
// Include footer
require_once 'includes/footer.php';
?>
