<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Fixing Activity Submissions Table</h1>";

try {
    // Check if activity_submissions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_submissions'");
    $activitySubmissionsExists = $stmt->rowCount() > 0;
    
    if (!$activitySubmissionsExists) {
        echo "<p>Activity Submissions table does not exist. Creating it now...</p>";
        
        // Create activity_submissions table with all required columns
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `activity_submissions` (
                `submission_id` int(11) NOT NULL AUTO_INCREMENT,
                `activity_id` int(11) NOT NULL,
                `user_id` int(11) NOT NULL,
                `submission_content` text DEFAULT NULL,
                `file_path` varchar(255) DEFAULT NULL,
                `file_name` varchar(255) DEFAULT NULL,
                `file_type` varchar(100) DEFAULT NULL,
                `file_size` int(11) DEFAULT NULL,
                `submission_date` datetime NOT NULL,
                `is_late` tinyint(1) DEFAULT 0,
                `grade` decimal(5,2) DEFAULT NULL,
                `feedback` text DEFAULT NULL,
                `graded_by` int(11) DEFAULT NULL,
                `graded_at` datetime DEFAULT NULL,
                PRIMARY KEY (`submission_id`),
                KEY `activity_id` (`activity_id`),
                KEY `user_id` (`user_id`),
                KEY `graded_by` (`graded_by`),
                CONSTRAINT `activity_submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
                CONSTRAINT `activity_submissions_ibfk_3` FOREIGN KEY (`graded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");
        
        echo "<p>✓ Activity Submissions table created successfully.</p>";
    } else {
        echo "<p>✓ Activity Submissions table exists. Checking for missing columns...</p>";
        
        // Check if file_path column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'file_path'");
        $filePathExists = $stmt->rowCount() > 0;
        
        if (!$filePathExists) {
            echo "<p>Adding missing file_path column...</p>";
            $pdo->exec("ALTER TABLE `activity_submissions` ADD COLUMN `file_path` varchar(255) DEFAULT NULL");
            echo "<p>✓ file_path column added successfully.</p>";
        } else {
            echo "<p>✓ file_path column already exists.</p>";
        }
        
        // Check if file_name column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'file_name'");
        $fileNameExists = $stmt->rowCount() > 0;
        
        if (!$fileNameExists) {
            echo "<p>Adding missing file_name column...</p>";
            $pdo->exec("ALTER TABLE `activity_submissions` ADD COLUMN `file_name` varchar(255) DEFAULT NULL");
            echo "<p>✓ file_name column added successfully.</p>";
        } else {
            echo "<p>✓ file_name column already exists.</p>";
        }
        
        // Check if file_type column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'file_type'");
        $fileTypeExists = $stmt->rowCount() > 0;
        
        if (!$fileTypeExists) {
            echo "<p>Adding missing file_type column...</p>";
            $pdo->exec("ALTER TABLE `activity_submissions` ADD COLUMN `file_type` varchar(100) DEFAULT NULL");
            echo "<p>✓ file_type column added successfully.</p>";
        } else {
            echo "<p>✓ file_type column already exists.</p>";
        }
        
        // Check if file_size column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'file_size'");
        $fileSizeExists = $stmt->rowCount() > 0;
        
        if (!$fileSizeExists) {
            echo "<p>Adding missing file_size column...</p>";
            $pdo->exec("ALTER TABLE `activity_submissions` ADD COLUMN `file_size` int(11) DEFAULT NULL");
            echo "<p>✓ file_size column added successfully.</p>";
        } else {
            echo "<p>✓ file_size column already exists.</p>";
        }
        
        // Check if submission_content column exists (might be named 'content' in some versions)
        $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'submission_content'");
        $submissionContentExists = $stmt->rowCount() > 0;
        
        if (!$submissionContentExists) {
            $stmt = $pdo->query("SHOW COLUMNS FROM `activity_submissions` LIKE 'content'");
            $contentExists = $stmt->rowCount() > 0;
            
            if ($contentExists) {
                echo "<p>Renaming content column to submission_content...</p>";
                $pdo->exec("ALTER TABLE `activity_submissions` CHANGE COLUMN `content` `submission_content` text DEFAULT NULL");
                echo "<p>✓ content column renamed to submission_content successfully.</p>";
            } else {
                echo "<p>Adding missing submission_content column...</p>";
                $pdo->exec("ALTER TABLE `activity_submissions` ADD COLUMN `submission_content` text DEFAULT NULL");
                echo "<p>✓ submission_content column added successfully.</p>";
            }
        } else {
            echo "<p>✓ submission_content column already exists.</p>";
        }
    }
    
    echo "<p>Table structure has been fixed. You can now <a href='activity_view.php?id=5'>return to the activity</a>.</p>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error fixing table: " . $e->getMessage() . "</div>";
}
?>
