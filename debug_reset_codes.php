<?php
// Include configuration file
require_once 'includes/config.php';

// Get the database connection
global $pdo;

// Check if the password_reset table exists and show its contents
try {
    echo "<h2>Password Reset Codes</h2>";
    
    $stmt = $pdo->query("SELECT pr.*, u.username, u.email FROM password_reset pr 
                         JOIN users u ON pr.user_id = u.user_id 
                         ORDER BY pr.expiry_time DESC");
    
    if ($stmt->rowCount() > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Reset ID</th><th>User ID</th><th>Username</th><th>Email</th><th>Reset Code</th><th>Expiry Time</th><th>Created At</th></tr>";
        
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>" . $row['reset_id'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['email'] . "</td>";
            echo "<td>" . $row['reset_code'] . "</td>";
            echo "<td>" . $row['expiry_time'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "No reset codes found in the database.";
    }
    
    // Show session variables
    echo "<h2>Session Variables</h2>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
    
    // Show email log if it exists
    echo "<h2>Email Log</h2>";
    $logFile = __DIR__ . '/logs/email_log.txt';
    if (file_exists($logFile)) {
        echo "<pre>" . htmlspecialchars(file_get_contents($logFile)) . "</pre>";
    } else {
        echo "Email log file does not exist.";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
