<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Test Button Fixes";
$current_page = "test";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Testing Button Fixes</h1>
            
            <div class="alert alert-info">
                <h4>Changes Made:</h4>
                <p>The following buttons have been fixed to make them functional:</p>
                <ul>
                    <li>Update Quiz button in quiz_edit.php</li>
                    <li>Update Activity button in activity_edit.php</li>
                    <li>Add Question button in both quiz_edit.php and activity_edit.php</li>
                </ul>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Test Cases</h5>
                </div>
                <div class="card-body">
                    <h6>1. Update Quiz Button</h6>
                    <p>To test the Update Quiz button:</p>
                    <ol>
                        <li>Go to a quiz edit page</li>
                        <li>Make changes to the quiz details</li>
                        <li>Click the "Update Quiz" button</li>
                        <li>Verify that the changes are saved successfully</li>
                    </ol>
                    
                    <h6>2. Update Activity Button</h6>
                    <p>To test the Update Activity button:</p>
                    <ol>
                        <li>Go to an activity edit page</li>
                        <li>Make changes to the activity details</li>
                        <li>Click the "Update Activity" button</li>
                        <li>Verify that the changes are saved successfully</li>
                    </ol>
                    
                    <h6>3. Add Question Button in Quiz</h6>
                    <p>To test the Add Question button in a quiz:</p>
                    <ol>
                        <li>Go to a quiz edit page</li>
                        <li>Click on the "Questions" tab</li>
                        <li>Click the "Add New Question" button</li>
                        <li>Fill in the question details</li>
                        <li>Click the "Add Question" button</li>
                        <li>Verify that the question is added successfully</li>
                    </ol>
                    
                    <h6>4. Add Question Button in Activity</h6>
                    <p>To test the Add Question button in an activity:</p>
                    <ol>
                        <li>Go to an activity edit page</li>
                        <li>Click on the "Questions" tab</li>
                        <li>Click the "Add New Question" button</li>
                        <li>Fill in the question details</li>
                        <li>Click the "Add Question" button</li>
                        <li>Verify that the question is added successfully</li>
                    </ol>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>Quick Links for Testing</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Quiz Edit Pages</h6>
                            <div id="quiz-links">
                                <p>Loading quiz links...</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Activity Edit Pages</h6>
                            <div id="activity-links">
                                <p>Loading activity links...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to load quiz links
    function loadQuizLinks() {
        fetch('get_activities.php?type=quiz')
            .then(response => response.json())
            .then(data => {
                const quizLinksDiv = document.getElementById('quiz-links');
                if (data.length > 0) {
                    let html = '<ul class="list-group">';
                    data.forEach(quiz => {
                        html += `<li class="list-group-item">
                            <a href="quiz_edit.php?id=${quiz.activity_id}">${quiz.title}</a>
                        </li>`;
                    });
                    html += '</ul>';
                    quizLinksDiv.innerHTML = html;
                } else {
                    quizLinksDiv.innerHTML = '<p>No quizzes found.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading quiz links:', error);
                document.getElementById('quiz-links').innerHTML = '<p class="text-danger">Error loading quiz links.</p>';
            });
    }

    // Function to load activity links
    function loadActivityLinks() {
        fetch('get_activities.php?type=activity')
            .then(response => response.json())
            .then(data => {
                const activityLinksDiv = document.getElementById('activity-links');
                if (data.length > 0) {
                    let html = '<ul class="list-group">';
                    data.forEach(activity => {
                        html += `<li class="list-group-item">
                            <a href="activity_edit.php?id=${activity.activity_id}">${activity.title}</a>
                        </li>`;
                    });
                    html += '</ul>';
                    activityLinksDiv.innerHTML = html;
                } else {
                    activityLinksDiv.innerHTML = '<p>No activities found.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading activity links:', error);
                document.getElementById('activity-links').innerHTML = '<p class="text-danger">Error loading activity links.</p>';
            });
    }

    // Load links when the page loads
    loadQuizLinks();
    loadActivityLinks();
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
