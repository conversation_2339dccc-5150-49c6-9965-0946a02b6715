<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/announcement_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher()) {
    $_SESSION['error'] = "Only teachers can edit announcements.";
    header("location: index.php");
    exit;
}

// Check if announcement ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Announcement ID is required.";
    header("location: index.php");
    exit;
}

$announcementId = intval($_GET['id']);

// Get announcement details
$announcement = getAnnouncementById($announcementId);

// Check if announcement exists
if (is_string($announcement)) {
    $_SESSION['error'] = $announcement;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $announcement['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this announcement
if ($announcement['created_by'] != $_SESSION['user_id'] && $course['created_by'] != $_SESSION['user_id'] && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to edit this announcement.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Get announcement files
$files = getAnnouncementFiles($announcementId);
if (is_string($files)) {
    $files = [];
}

// Initialize variables
$title = $announcement['title'];
$content = $announcement['content'];
$announcementDate = $announcement['announcement_date'];
$title_err = $content_err = $file_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate content
    if (empty(trim($_POST["content"]))) {
        $content_err = "Please enter content.";
    } else {
        $content = trim($_POST["content"]);
    }

    // Validate announcement date (optional)
    if (isset($_POST["announcement_date"]) && !empty($_POST["announcement_date"])) {
        $announcementDate = $_POST["announcement_date"];

        // Validate date format
        $date = DateTime::createFromFormat('Y-m-d', $announcementDate);
        if (!$date || $date->format('Y-m-d') !== $announcementDate) {
            $date_err = "Invalid date format.";
        }
    }

    // Validate file uploads (if any)
    if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'text/plain', 'application/zip', 'application/x-rar-compressed'];

        $maxFileSize = 5 * 1024 * 1024; // 5MB

        foreach ($_FILES['files']['name'] as $key => $name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                // Check file type
                if (!in_array($_FILES['files']['type'][$key], $allowedTypes)) {
                    $file_err = "Invalid file type. Allowed types: images, PDF, Office documents, text, zip, and rar.";
                    break;
                }

                // Check file size
                if ($_FILES['files']['size'][$key] > $maxFileSize) {
                    $file_err = "File size exceeds the maximum limit of 5MB.";
                    break;
                }
            } elseif ($_FILES['files']['error'][$key] !== UPLOAD_ERR_NO_FILE) {
                $file_err = getFileUploadError($_FILES['files']['error'][$key]);
                break;
            }
        }
    }

    // Check input errors before updating the announcement
    if (empty($title_err) && empty($content_err) && empty($file_err) && empty($date_err)) {
        // Update the announcement
        $result = updateAnnouncement($announcementId, $title, $content, $announcementDate);

        if ($result === true) {
            // Handle file uploads if any
            if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
                // Create uploads directory if it doesn't exist
                $uploadDir = 'uploads/announcements/' . $announcementId . '/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Process each uploaded file
                $fileCount = count($_FILES['files']['name']);
                for ($i = 0; $i < $fileCount; $i++) {
                    if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = basename($_FILES['files']['name'][$i]);
                        $fileType = $_FILES['files']['type'][$i];
                        $fileSize = $_FILES['files']['size'][$i];
                        $fileTmpName = $_FILES['files']['tmp_name'][$i];

                        // Generate a unique filename to prevent overwriting
                        $uniqueFileName = uniqid() . '_' . $fileName;
                        $targetFilePath = $uploadDir . $uniqueFileName;

                        // Move the uploaded file to the target directory
                        if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                            // Save file information to the database
                            $stmt = $pdo->prepare("
                                INSERT INTO announcement_files (announcement_id, file_name, file_path, file_type, file_size)
                                VALUES (:announcementId, :fileName, :filePath, :fileType, :fileSize)
                            ");

                            $relativePath = 'uploads/announcements/' . $announcementId . '/' . $uniqueFileName;

                            $stmt->bindParam(':announcementId', $announcementId);
                            $stmt->bindParam(':fileName', $fileName);
                            $stmt->bindParam(':filePath', $relativePath);
                            $stmt->bindParam(':fileType', $fileType);
                            $stmt->bindParam(':fileSize', $fileSize);

                            $stmt->execute();
                        } else {
                            $_SESSION['error'] = "Failed to upload file: " . $fileName;
                        }
                    }
                }
            }

            // Announcement updated successfully
            $_SESSION['success'] = "Announcement updated successfully.";
            header("location: announcement_view.php?id=$announcementId");
            exit;
        } else {
            // Error updating announcement
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Edit Announcement";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="announcement_view.php?id=<?php echo $announcementId; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Announcement
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Announcement</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Edit your announcement to update information for your students.</p>
        </div>
    </div>
</div>

<!-- Announcement form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcementId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>

            <div class="form-group">
                <label for="content">Content</label>
                <textarea name="content" id="content" rows="6" class="form-control <?php echo (!empty($content_err)) ? 'is-invalid' : ''; ?>"><?php echo htmlspecialchars($content); ?></textarea>
                <span class="invalid-feedback"><?php echo $content_err; ?></span>
            </div>

            <div class="form-group">
                <label for="announcement_date">Date</label>
                <input type="date" name="announcement_date" id="announcement_date" class="form-control <?php echo (!empty($date_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $announcementDate; ?>">
                <span class="invalid-feedback"><?php echo $date_err ?? ''; ?></span>
            </div>

            <?php if (!empty($files)): ?>
            <div class="form-group">
                <label>Existing Attachments</label>
                <div class="list-group">
                    <?php foreach ($files as $file): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <a href="<?php echo $file['file_path']; ?>" target="_blank">
                            <?php echo htmlspecialchars($file['file_name']); ?>
                        </a>
                        <span class="badge badge-primary badge-pill">
                            <?php echo formatFileSize($file['file_size']); ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
                <small class="form-text text-muted mt-2">
                    Note: Existing files will be preserved. Add new files below if needed.
                </small>
            </div>
            <?php endif; ?>

            <div class="form-group">
                <label for="files">Add Attachments (Optional)</label>
                <div class="custom-file">
                    <input type="file" name="files[]" id="files" class="custom-file-input <?php echo (!empty($file_err)) ? 'is-invalid' : ''; ?>" multiple>
                    <label class="custom-file-label" for="files">Choose files...</label>
                    <span class="invalid-feedback"><?php echo $file_err ?? ''; ?></span>
                </div>
                <small class="form-text text-muted">
                    Allowed file types: images, PDF, Office documents, text, zip, and rar. Maximum size: 5MB per file.
                </small>
            </div>

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Update Announcement</button>
                <a href="announcement_view.php?id=<?php echo $announcementId; ?>" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<!-- Add JavaScript for file input -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update file input label with selected file names
    document.getElementById('files').addEventListener('change', function(e) {
        var fileName = '';
        if (this.files && this.files.length > 1) {
            fileName = (this.files.length) + ' files selected';
        } else if (this.files && this.files.length === 1) {
            fileName = this.files[0].name;
        }

        // Find the label element
        var label = document.querySelector('label.custom-file-label');
        if (label) {
            if (fileName) {
                label.innerHTML = fileName;
            } else {
                label.innerHTML = 'Choose files...';
            }
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';

/**
 * Helper function to format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
