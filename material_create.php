<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';
require_once 'includes/settings_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher()) {
    $_SESSION['error'] = "Only teachers can create materials.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized for this course
if ($course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId) && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to create materials for this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Initialize variables
$title = $content = "";
$title_err = $content_err = "";
$file_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate content
    if (empty(trim($_POST["content"]))) {
        $content_err = "Please enter content.";
    } else {
        $content = trim($_POST["content"]);
    }

    // Validate file uploads (if any)
    if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
        // Get allowed file types from settings
        $allowedTypes = getAllowedFileTypes();

        // Get max file size from settings
        $maxFileSize = getMaxFileUploadSize();
        $maxFileSizeMB = $maxFileSize / (1024 * 1024);

        foreach ($_FILES['files']['name'] as $key => $name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                // Check file type - allow pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, zip
                $allowedMimeTypes = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-powerpoint',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'image/jpeg',
                    'image/png',
                    'application/zip'
                ];

                if (!in_array($_FILES['files']['type'][$key], $allowedMimeTypes)) {
                    $file_err = "Invalid file type for file '{$name}'. Please upload only pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, or zip files.";
                    break;
                }

                // Check file size - 10GB limit (10737418240 bytes)
                if ($_FILES['files']['size'][$key] > 10737418240) {
                    $file_err = "File '{$name}' exceeds the maximum limit of 10GB.";
                    break;
                }
            } elseif ($_FILES['files']['error'][$key] !== UPLOAD_ERR_NO_FILE) {
                $file_err = getFileUploadError($_FILES['files']['error'][$key]);
                break;
            }
        }
    }

    // Check input errors before creating the material
    if (empty($title_err) && empty($content_err) && empty($file_err)) {
        // Create the material
        $activityId = createMaterial($courseId, $title, $content, null, $_SESSION['user_id']);

        if (is_numeric($activityId)) {
            // Handle file uploads if any
            if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
                // Create uploads directory if it doesn't exist
                $uploadDir = 'uploads/materials/' . $activityId . '/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Process each uploaded file
                $fileCount = count($_FILES['files']['name']);
                for ($i = 0; $i < $fileCount; $i++) {
                    if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = basename($_FILES['files']['name'][$i]);
                        $fileType = $_FILES['files']['type'][$i];
                        $fileSize = $_FILES['files']['size'][$i];
                        $fileTmpName = $_FILES['files']['tmp_name'][$i];

                        // Generate a unique filename to prevent overwriting
                        $uniqueFileName = uniqid() . '_' . $fileName;
                        $targetFilePath = $uploadDir . $uniqueFileName;

                        // Move the uploaded file to the target directory
                        if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                            // Save file information to the database
                            $stmt = $pdo->prepare("
                                INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size)
                                VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize)
                            ");

                            $relativePath = 'uploads/materials/' . $activityId . '/' . $uniqueFileName;

                            $stmt->bindParam(':activityId', $activityId);
                            $stmt->bindParam(':fileName', $fileName);
                            $stmt->bindParam(':filePath', $relativePath);
                            $stmt->bindParam(':fileType', $fileType);
                            $stmt->bindParam(':fileSize', $fileSize);

                            $stmt->execute();
                        } else {
                            $_SESSION['error'] = "Failed to upload file: " . $fileName;
                        }
                    }
                }
            }

            // Material created successfully
            $_SESSION['success'] = "Material created successfully.";
            header("location: course_view_full.php?id=$courseId&tab=classwork");
            exit;
        } else {
            // Error creating material
            $_SESSION['error'] = $activityId;
        }
    }
}

// Set page title
$page_title = "Create Material";

// Include header
require_once 'includes/header.php';

// Get max file size from settings
$maxFileSizeMB = getMaxFileUploadSize() / (1024 * 1024);

// Get allowed file types description
$allowedTypesDesc = [];
$allowedTypes = getAllowedFileTypes();

if (in_array('image/jpeg', $allowedTypes) || in_array('image/png', $allowedTypes) || in_array('image/gif', $allowedTypes)) {
    $allowedTypesDesc[] = 'images';
}
if (in_array('application/pdf', $allowedTypes)) {
    $allowedTypesDesc[] = 'PDF';
}
if (in_array('application/msword', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.wordprocessingml.document', $allowedTypes) ||
    in_array('application/vnd.ms-excel', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', $allowedTypes) ||
    in_array('application/vnd.ms-powerpoint', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.presentationml.presentation', $allowedTypes)) {
    $allowedTypesDesc[] = 'Office documents';
}
if (in_array('text/plain', $allowedTypes)) {
    $allowedTypesDesc[] = 'text';
}
if (in_array('application/zip', $allowedTypes) || in_array('application/x-rar-compressed', $allowedTypes)) {
    $allowedTypesDesc[] = 'archives';
}
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Material</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create learning materials for your students.</p>
        </div>
    </div>
</div>

<!-- Material form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>
            <div class="form-group">
                <label for="content">Content</label>
                <textarea name="content" id="content" rows="10" class="form-control <?php echo (!empty($content_err)) ? 'is-invalid' : ''; ?>"><?php echo $content; ?></textarea>
                <span class="invalid-feedback"><?php echo $content_err; ?></span>
                <small class="form-text text-muted">You can use basic HTML formatting in your content.</small>
            </div>

            <div class="form-group">
                <label for="files">Attachment Files (Optional)</label>
                <div class="custom-file">
                    <input type="file" name="files[]" id="files" class="custom-file-input <?php echo (!empty($file_err) && $_SERVER["REQUEST_METHOD"] == "POST") ? 'is-invalid' : ''; ?>" multiple>
                    <label class="custom-file-label" for="files">Choose files...</label>
                    <?php if (!empty($file_err) && $_SERVER["REQUEST_METHOD"] == "POST"): ?>
                    <span class="invalid-feedback"><?php echo $file_err; ?></span>
                    <?php endif; ?>
                </div>
                <small class="form-text text-muted mt-2">
                    Allowed file types: pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, zip. Maximum size: 10GB per file.
                </small>
            </div>

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Create Material</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<!-- Add JavaScript for file input -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set max file size to 10GB
    const maxFileSize = 10737418240; // 10GB in bytes
    const maxFileSizeMB = 10240; // 10GB in MB

    // Set allowed file types
    const allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/png',
        'application/zip'
    ];

    // Update file input label with selected file names and validate file size and type
    document.getElementById('files').addEventListener('change', function(e) {
        var fileName = '';
        var fileError = false;
        var errorMessage = '';

        if (this.files && this.files.length > 1) {
            fileName = (this.files.length) + ' files selected';

            // Check each file size and type
            for (let i = 0; i < this.files.length; i++) {
                // Check file size
                if (this.files[i].size > maxFileSize) {
                    fileError = true;
                    errorMessage = 'File "' + this.files[i].name + '" exceeds the maximum limit of 10GB.';
                    break;
                }

                // Check file type
                if (!allowedMimeTypes.includes(this.files[i].type) && this.files[i].type !== '') {
                    fileError = true;
                    errorMessage = 'File "' + this.files[i].name + '" has an invalid file type.';
                    break;
                }
            }
        } else if (this.files && this.files.length === 1) {
            fileName = this.files[0].name;

            // Check file size
            if (this.files[0].size > maxFileSize) {
                fileError = true;
                errorMessage = 'File exceeds the maximum limit of 10GB.';
            }

            // Check file type
            if (!allowedMimeTypes.includes(this.files[0].type) && this.files[0].type !== '') {
                fileError = true;
                errorMessage = 'Invalid file type. Please upload only allowed file types.';
            }
        }

        // Find the label element
        var label = document.querySelector('label.custom-file-label');
        if (label) {
            if (fileName) {
                label.innerHTML = fileName;
            } else {
                label.innerHTML = 'Choose files...';
            }
        }

        // Show error message if file is too large or has invalid type
        if (fileError) {
            // Add is-invalid class to input
            this.classList.add('is-invalid');

            // Create or update error message
            let errorSpan = this.parentNode.querySelector('.invalid-feedback');
            if (!errorSpan) {
                errorSpan = document.createElement('span');
                errorSpan.className = 'invalid-feedback';
                this.parentNode.appendChild(errorSpan);
            }
            errorSpan.innerHTML = errorMessage;
            errorSpan.style.display = 'block';

            // Clear the file input
            this.value = '';
            if (label) {
                label.innerHTML = 'Choose files...';
            }
        } else {
            // Remove is-invalid class and hide error message
            this.classList.remove('is-invalid');
            let errorSpan = this.parentNode.querySelector('.invalid-feedback');
            if (errorSpan) {
                errorSpan.style.display = 'none';
            }
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
