<?php
/**
 * Event Status Update
 *
 * This file handles updating the status of calendar events (mark as done/pending).
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/calendar_events.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['error'] = "You must be logged in to perform this action.";
    header('Location: login.php');
    exit;
}

// Check if event ID and status are provided
if (!isset($_POST['event_id']) || !isset($_POST['status'])) {
    $_SESSION['error'] = "Missing required parameters.";
    header('Location: calendar.php');
    exit;
}

$eventId = intval($_POST['event_id']);
$status = $_POST['status'];

// Validate status
if ($status !== 'done' && $status !== 'pending') {
    $_SESSION['error'] = "Invalid status value.";
    header('Location: calendar.php');
    exit;
}

// Debug information
error_log("Updating event status: Event ID = $eventId, Status = $status, User ID = " . $_SESSION['user_id']);

// Check if this is an activity or a calendar event
$source = isset($_POST['source']) ? $_POST['source'] : 'calendar';

if ($source === 'activity') {
    // This is an activity (assignment, quiz, etc.)
    $activityId = $eventId;

    if ($status === 'done') {
        // Mark activity as done
        if (markActivityAsDone($activityId, $_SESSION['user_id'])) {
            $_SESSION['success'] = "Activity marked as done.";
            error_log("Activity marked as done: ID = $activityId");
        } else {
            $_SESSION['error'] = "Failed to mark activity as done.";
            error_log("Failed to mark activity as done: ID = $activityId");
        }
    } else {
        // Mark activity as pending
        if (markActivityAsPending($activityId, $_SESSION['user_id'])) {
            $_SESSION['success'] = "Activity marked as pending.";
            error_log("Activity marked as pending: ID = $activityId");
        } else {
            $_SESSION['error'] = "Failed to mark activity as pending.";
            error_log("Failed to mark activity as pending: ID = $activityId");
        }
    }
} else {
    // This is a regular calendar event
    if (updateEventStatus($eventId, $status)) {
        $_SESSION['success'] = "Event status updated successfully.";
        error_log("Event status updated successfully");
    } else {
        $_SESSION['error'] = "Failed to update event status. Please ensure you are the owner of this event.";
        error_log("Failed to update event status");
    }
}

// Redirect back to calendar
header('Location: calendar.php');
exit;
