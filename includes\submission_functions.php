<?php
/**
 * Submission Functions
 *
 * This file contains functions related to student submissions for activities, assignments, and quizzes.
 */

require_once 'config.php';
require_once 'course_functions.php';

/**
 * Function to get all submissions for a student in a course
 *
 * @param int $courseId The course ID
 * @param int $userId The user ID
 * @return array|string Array of submissions if successful, error message otherwise
 */
function getStudentSubmissions($courseId, $userId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT s.*, a.title as assignment_title, a.activity_type, a.points as max_points
            FROM submissions s
            JOIN activities a ON s.assignment_id = a.activity_id
            WHERE a.course_id = :courseId AND s.user_id = :userId
            ORDER BY s.submitted_at DESC
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve submissions: " . $e->getMessage();
    }
}

/**
 * Function to get all submissions for an activity
 *
 * @param int $activityId The activity ID
 * @return array|string Array of submissions if successful, error message otherwise
 */
function getActivitySubmissions($activityId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT s.*, u.first_name, u.last_name, u.username
            FROM submissions s
            JOIN users u ON s.user_id = u.user_id
            WHERE s.assignment_id = :activityId
            ORDER BY s.submitted_at DESC
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve submissions: " . $e->getMessage();
    }
}

/**
 * Function to get a submission by ID with additional details
 *
 * @param int $submissionId The submission ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getSubmissionWithDetails($submissionId) {
    global $pdo;

    try {
        // First, check if the submission exists and get its basic details
        $checkStmt = $pdo->prepare("SELECT * FROM submissions WHERE submission_id = :submissionId");
        $checkStmt->bindParam(':submissionId', $submissionId);
        $checkStmt->execute();

        if ($checkStmt->rowCount() == 0) {
            return "Submission not found.";
        }

        // Now get the full details with proper joins
        $stmt = $pdo->prepare("
            SELECT s.*, a.title as assignment_title, a.description as assignment_description,
                   a.activity_type, a.points as max_points, a.due_date, a.activity_id,
                   u.first_name, u.last_name, u.username, u.email,
                   c.course_id, c.title as course_title
            FROM submissions s
            JOIN activities a ON s.assignment_id = a.activity_id
            JOIN users u ON s.user_id = u.user_id
            JOIN courses c ON a.course_id = c.course_id
            WHERE s.submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Submission not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to grade a submission (legacy version)
 * This is kept for backward compatibility with older code
 *
 * @param int $submissionId The submission ID
 * @param float $score The score
 * @param string $feedback The feedback
 * @return bool|string True if grading successful, error message otherwise
 */
function gradeSubmissionLegacy($submissionId, $score, $feedback) {
    // Call the new function to avoid code duplication
    return gradeSubmission($submissionId, $score, $feedback);
}

/**
 * Function to update a grade
 *
 * @param int $submissionId The submission ID
 * @param float $score The score (actual points, not percentage)
 * @param string $feedback The feedback
 * @return bool|string True if update successful, error message otherwise
 */
function updateGrade($submissionId, $score, $feedback) {
    global $pdo;

    try {
        // Get submission details to calculate percentage
        $submission = getSubmissionWithDetails($submissionId);
        if (is_string($submission)) {
            return $submission; // Error message
        }

        // Get max points
        $maxPoints = $submission['max_points'];
        if (!$maxPoints || $maxPoints <= 0) {
            $maxPoints = 100; // Default if not set
        }

        // Special case handling for specific activities
        // Activity ID 7 should have 3 points
        if ($submission['assignment_id'] == 7) {
            $maxPoints = 3;
        }

        // Calculate percentage score (0-100) for storage
        $percentageScore = ($score / $maxPoints) * 100;

        // Update the submission
        $stmt = $pdo->prepare("
            UPDATE submissions
            SET score = :score, feedback = :feedback, graded_at = NOW()
            WHERE submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->bindParam(':score', $percentageScore);
        $stmt->bindParam(':feedback', $feedback);
        $stmt->execute();

        // Create notification for the student
        if (function_exists('createNotification')) {
            $notificationTitle = "Grade Updated";
            $notificationMessage = "Your grade for '{$submission['assignment_title']}' has been updated. New score: $score/$maxPoints";

            createNotification($submission['user_id'], $notificationTitle, $notificationMessage, 'grade_updated', $submissionId);
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to update grade: " . $e->getMessage();
    }
}

/**
 * Function to get a submission by ID with activity details
 *
 * @param int $submissionId The submission ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getSubmissionById($submissionId) {
    global $pdo;

    try {
        // First, check if the submission exists
        $checkStmt = $pdo->prepare("SELECT * FROM submissions WHERE submission_id = :submissionId");
        $checkStmt->bindParam(':submissionId', $submissionId);
        $checkStmt->execute();

        if ($checkStmt->rowCount() == 0) {
            return "Submission not found.";
        }

        // Get the submission details
        $stmt = $pdo->prepare("
            SELECT s.*, a.title as activity_title, a.description as activity_description,
                   a.activity_type, a.points as max_points, a.due_date, a.activity_id,
                   u.username, u.first_name, u.last_name, u.email,
                   c.course_id, c.title as course_title
            FROM submissions s
            JOIN activities a ON s.assignment_id = a.activity_id
            JOIN users u ON s.user_id = u.user_id
            JOIN courses c ON a.course_id = c.course_id
            WHERE s.submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Submission not found.";
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to get a student's submission for an activity from the submissions table
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getStudentSubmission($activityId, $userId) {
    global $pdo;

    try {
        // Check if a submission exists
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM submissions
            WHERE assignment_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "No submission found.";
        }

        $submission = $stmt->fetch();

        // Get the full submission details
        return getSubmissionById($submission['submission_id']);
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to grade a submission from the submissions table
 * Admin can grade any submission, teachers can only grade submissions for their own courses
 *
 * @param int $submissionId The submission ID
 * @param float $grade The grade (actual points, not percentage)
 * @param string $feedback The feedback
 * @return bool|string True if grading successful, error message otherwise
 */
function gradeSubmission($submissionId, $grade, $feedback = null) {
    global $pdo;

    try {
        // Check if user is authorized to grade submissions
        if (!isAdmin() && !isTeacher()) {
            return "You are not authorized to grade submissions.";
        }

        // Get submission details to calculate percentage
        $submission = getSubmissionById($submissionId);
        if (is_string($submission)) {
            return $submission; // Error message
        }

        // Get max points
        $maxPoints = $submission['max_points'];
        if (!$maxPoints || $maxPoints <= 0) {
            $maxPoints = 100; // Default if not set
        }

        // Special case handling for specific activities
        // Activity ID 7 should have 3 points
        if ($submission['assignment_id'] == 7) {
            $maxPoints = 3;
        }

        // Calculate percentage score (0-100) for storage
        $percentageScore = ($grade / $maxPoints) * 100;

        // Update the submission
        $stmt = $pdo->prepare("
            UPDATE submissions
            SET score = :grade, feedback = :feedback, is_graded = 1, graded_at = NOW(), graded_by = :gradedBy
            WHERE submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->bindParam(':grade', $percentageScore);
        $stmt->bindParam(':feedback', $feedback);
        $stmt->bindParam(':gradedBy', $_SESSION['user_id']);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Submission not found.";
        }

        // Create notification for the student
        if (function_exists('createNotification')) {
            $notificationTitle = "Submission Graded";
            $notificationMessage = "Your submission for '{$submission['activity_title']}' has been graded. Score: $grade/$maxPoints";

            createNotification($submission['user_id'], $notificationTitle, $notificationMessage, 'submission_graded', $submissionId);
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to grade submission: " . $e->getMessage();
    }
}

// Function getStudentActivitySubmission is already defined in activity_functions.php

/**
 * Function to create a new activity submission
 *
 * @param array $submissionData The submission data
 * @return int|string Submission ID if successful, error message otherwise
 */
function createActivitySubmission($submissionData) {
    global $pdo;

    try {
        // Check if required fields are provided
        if (!isset($submissionData['activity_id']) || !isset($submissionData['user_id'])) {
            return "Missing required fields.";
        }

        // Check if a submission already exists
        $existingSubmission = getStudentActivitySubmission($submissionData['activity_id'], $submissionData['user_id']);
        if (!is_string($existingSubmission)) {
            return "A submission already exists for this activity.";
        }

        // Insert the submission
        $stmt = $pdo->prepare("
            INSERT INTO activity_submissions (
                activity_id, user_id, submission_date, is_late,
                grade, feedback, file_path, file_name,
                submission_link, submission_text
            ) VALUES (
                :activity_id, :user_id, :submission_date, :is_late,
                :grade, :feedback, :file_path, :file_name,
                :submission_link, :submission_text
            )
        ");

        // Bind parameters
        $stmt->bindParam(':activity_id', $submissionData['activity_id']);
        $stmt->bindParam(':user_id', $submissionData['user_id']);
        $stmt->bindParam(':submission_date', $submissionData['submission_date']);
        $stmt->bindParam(':is_late', $submissionData['is_late'], PDO::PARAM_BOOL);
        $stmt->bindParam(':grade', $submissionData['grade']);
        $stmt->bindParam(':feedback', $submissionData['feedback']);
        $stmt->bindParam(':file_path', $submissionData['file_path']);
        $stmt->bindParam(':file_name', $submissionData['file_name']);
        $stmt->bindParam(':submission_link', $submissionData['submission_link']);
        $stmt->bindParam(':submission_text', $submissionData['submission_text']);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create submission: " . $e->getMessage();
    }
}

/**
 * Function to delete an activity submission
 *
 * @param int $submissionId The submission ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteActivitySubmission($submissionId) {
    global $pdo;

    try {
        // Delete the submission
        $stmt = $pdo->prepare("
            DELETE FROM activity_submissions
            WHERE submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Submission not found.";
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to delete submission: " . $e->getMessage();
    }
}