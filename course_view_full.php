<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';
require_once 'includes/announcement_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists and user has access
if (is_string($course)) {
    // Course not found or error occurred
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this course
if (!isAdmin() &&
    !isInstructor($_SESSION['user_id'], $courseId) &&
    !(isTeacher() && $course['created_by'] == $_SESSION['user_id']) &&
    !isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You do not have access to this course.";
    header("location: index.php");
    exit;
}

// Get course instructors
$instructors = getCourseInstructors($courseId);
if (is_string($instructors)) {
    $instructorError = $instructors;
    $instructors = [];
}

// Get enrolled students
$students = getEnrolledStudents($courseId);
if (is_string($students)) {
    $studentError = $students;
    $students = [];
}

// Get course content
$modules = getCourseModules($courseId);
if (is_string($modules)) {
    $moduleError = $modules;
    $modules = [];
}

// Get course announcements
$announcements = getCourseAnnouncements($courseId);
if (is_string($announcements)) {
    $announcementError = $announcements;
    $announcements = [];
}

// Get course assignments
$assignments = getCourseAssignments($courseId);
if (is_string($assignments)) {
    $assignmentError = $assignments;
    $assignments = [];
}

// Determine active tab
$activeTab = isset($_GET['tab']) ? $_GET['tab'] : 'stream';
$validTabs = ['stream', 'classwork', 'people', 'grades'];
if (!in_array($activeTab, $validTabs)) {
    $activeTab = 'stream';
}

// Set page title
$page_title = htmlspecialchars($course['title']);

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="index.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Courses
    </a>
</div>

<!-- Course header -->
<div class="course-header mb-4">
    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['success']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['error']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="course-header-bg p-4 mb-4" style="background-color: #4285f4; color: white; border-radius: 8px; position: relative;">
        <div class="row">
            <div class="col-md-8">
                <h1 class="course-title"><?php echo htmlspecialchars($course['title']); ?></h1>
                <p class="course-description"><?php echo htmlspecialchars($course['description']); ?></p>

                <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
                <div class="d-flex align-items-center mt-3">
                    <div class="class-code-container mr-4">
                        <span class="font-weight-bold">Class Code: </span>
                        <span class="badge badge-light p-2" style="font-size: 1rem;"><?php echo htmlspecialchars($course['class_code']); ?></span>
                        <button class="btn btn-sm btn-light copy-code" data-code="<?php echo htmlspecialchars($course['class_code']); ?>" title="Copy class code" onclick="copyClassCode('<?php echo htmlspecialchars($course['class_code']); ?>')">
                            <i class="fas fa-copy"></i>
                        </button>
                        <span id="copySuccess" class="text-light ml-2" style="display: none;">
                            <i class="fas fa-check"></i> Copied!
                        </span>
                    </div>

                    <div class="course-status ml-4">
                        <span class="font-weight-bold">Status: </span>
                        <?php if ($course['is_active']): ?>
                            <span class="badge badge-light p-2" style="font-size: 1rem;">
                                <i class="fas fa-check-circle mr-1 text-success"></i> Active
                            </span>
                        <?php else: ?>
                            <span class="badge badge-light p-2" style="font-size: 1rem;">
                                <i class="fas fa-ban mr-1 text-danger"></i> Inactive
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="d-flex align-items-center mt-3">
                    <div class="class-code-container">
                        <span class="font-weight-bold">Class Code: </span>
                        <span class="badge badge-light p-2" style="font-size: 1rem;"><?php echo htmlspecialchars($course['class_code']); ?></span>
                    </div>
                </div>
                <?php endif; ?>
            </div>


        </div>

        <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
        <div class="course-actions mt-4 ml-3">
            <div class="d-flex justify-content-start">
                <a href="course_edit.php?id=<?php echo $courseId; ?>" class="btn btn-edit mr-2" style="min-width: 120px; border: 2px solid white; color: white; background-color: transparent;">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>

                <?php if ($course['is_active']): ?>
                <button type="button" class="btn btn-deactivate mr-2" data-toggle="modal" data-target="#deactivateModal" style="min-width: 120px; border: 2px solid #f4b400; color: white; background-color: transparent;">
                    <i class="fas fa-ban mr-1"></i> Deactivate
                </button>
                <?php else: ?>
                <button type="button" class="btn btn-activate mr-2" data-toggle="modal" data-target="#activateModal" style="min-width: 120px; border: 2px solid #f4b400; color: white; background-color: transparent;">
                    <i class="fas fa-check-circle mr-1"></i> Activate
                </button>
                <?php endif; ?>

                <button type="button" class="btn btn-delete" data-toggle="modal" data-target="#deleteModal" style="min-width: 120px; border: 2px solid #db4437; color: white; background-color: transparent;">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </button>
            </div>
        </div>
        <?php endif; ?>
    </div>

</div>

<!-- Course navigation tabs and actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <ul class="nav nav-tabs flex-grow-1 mb-0">
        <li class="nav-item">
            <a class="nav-link <?php echo $activeTab == 'stream' ? 'active' : ''; ?>" href="?id=<?php echo $courseId; ?>&tab=stream">
                <i class="fas fa-stream mr-2"></i> Stream
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo $activeTab == 'classwork' ? 'active' : ''; ?>" href="?id=<?php echo $courseId; ?>&tab=classwork">
                <i class="fas fa-book mr-2"></i> Classwork
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo $activeTab == 'people' ? 'active' : ''; ?>" href="?id=<?php echo $courseId; ?>&tab=people">
                <i class="fas fa-users mr-2"></i> People
            </a>
        </li>
        <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $activeTab == 'grades' ? 'active' : ''; ?>" href="?id=<?php echo $courseId; ?>&tab=grades">
                <i class="fas fa-chart-bar mr-2"></i> Grades
            </a>
        </li>
        <?php endif; ?>
    </ul>

    <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)) && $activeTab != 'stream'): ?>
    <div class="btn-group ml-3">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-plus"></i> Create
        </button>
        <div class="dropdown-menu dropdown-menu-right">
            <a class="dropdown-item" href="assignment_create.php?course_id=<?php echo $courseId; ?>">
                <i class="far fa-clipboard"></i> Assignment
            </a>
            <a class="dropdown-item" href="activity_create.php?course_id=<?php echo $courseId; ?>">
                <i class="fas fa-list"></i> Activity
            </a>
            <a class="dropdown-item" href="quiz_create.php?course_id=<?php echo $courseId; ?>">
                <i class="far fa-question-circle"></i> Quiz
            </a>
            <a class="dropdown-item" href="material_create.php?course_id=<?php echo $courseId; ?>">
                <i class="far fa-file-alt"></i> Material
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tab content -->
<div class="tab-content">
    <?php if ($activeTab == 'stream'): ?>
        <?php include 'includes/course_tabs/stream_tab.php'; ?>
    <?php elseif ($activeTab == 'classwork'): ?>
        <?php include 'includes/course_tabs/classwork_tab.php'; ?>
    <?php elseif ($activeTab == 'people'): ?>
        <?php include 'includes/course_tabs/people_tab.php'; ?>
    <?php elseif ($activeTab == 'grades' && (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)))): ?>
        <?php include 'includes/course_tabs/grades_tab.php'; ?>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
// Function to copy class code to clipboard
function copyClassCode(code) {
    // Create a temporary input element
    const tempInput = document.createElement('input');
    tempInput.value = code;
    document.body.appendChild(tempInput);

    // Select and copy the text
    tempInput.select();
    document.execCommand('copy');

    // Remove the temporary element
    document.body.removeChild(tempInput);

    // Show success message
    const successElement = document.getElementById('copySuccess');
    successElement.style.display = 'inline';

    // Hide success message after 2 seconds
    setTimeout(function() {
        successElement.style.display = 'none';
    }, 2000);
}
</script>

<!-- Activate Course Modal -->
<div class="modal fade" id="activateModal" tabindex="-1" role="dialog" aria-labelledby="activateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateModalLabel">Activate Course</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-check-circle text-success fa-4x mb-3"></i>
                    <h4>Activate this course?</h4>
                </div>
                <p>When you activate this course:</p>
                <ul>
                    <li>The course will become visible to all enrolled students</li>
                    <li>Students will be able to access all course content</li>
                    <li>Notifications will be sent to enrolled students</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="course_action.php?action=activate&id=<?php echo $courseId; ?>" class="btn btn-success">Activate Course</a>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Course Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" role="dialog" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateModalLabel">Deactivate Course</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-ban text-warning fa-4x mb-3"></i>
                    <h4>Deactivate this course?</h4>
                </div>
                <p>When you deactivate this course:</p>
                <ul>
                    <li>The course will be hidden from all enrolled students</li>
                    <li>Students will not be able to access any course content</li>
                    <li>All course data and enrollments will be preserved</li>
                    <li>You can reactivate the course at any time</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="course_action.php?action=deactivate&id=<?php echo $courseId; ?>" class="btn btn-warning">Deactivate Course</a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Course Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Delete Course</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-exclamation-triangle text-danger fa-4x mb-3"></i>
                    <h4>Delete this course permanently?</h4>
                </div>
                <div class="alert alert-danger">
                    <strong>Warning:</strong> This action cannot be undone!
                </div>
                <p>When you delete this course:</p>
                <ul>
                    <li>All course content will be permanently deleted</li>
                    <li>All student enrollments will be removed</li>
                    <li>All assignments, quizzes, and grades will be lost</li>
                    <li>All discussions and announcements will be deleted</li>
                </ul>
                <p class="font-weight-bold">Are you absolutely sure you want to delete this course?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="course_action.php?action=delete&id=<?php echo $courseId; ?>" class="btn btn-danger">Delete Permanently</a>
            </div>
        </div>
    </div>
</div>
