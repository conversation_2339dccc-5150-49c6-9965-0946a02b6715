<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Set page title
$page_title = "Debug Quiz Functions";

// Include header
require_once 'includes/header.php';

// Check if the user is logged in and is an admin or teacher
if (!isLoggedIn() || (!isAdmin() && !isTeacher())) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Function to display table structure
function displayTableStructure($tableName) {
    global $pdo;

    try {
        // Get table structure
        $stmt = $pdo->prepare("DESCRIBE $tableName");
        $stmt->execute();
        $columns = $stmt->fetchAll();

        echo "<h3>Table: $tableName</h3>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
        echo "<tbody>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }

        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

// Function to display table data
function displayTableData($tableName, $limit = 10) {
    global $pdo;

    try {
        // Get table data
        $stmt = $pdo->prepare("SELECT * FROM $tableName LIMIT :limit");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        $rows = $stmt->fetchAll();

        if (count($rows) == 0) {
            echo "<div class='alert alert-info'>No data found in $tableName table.</div>";
            return;
        }

        echo "<h3>Data in $tableName (Limited to $limit rows)</h3>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr>";

        // Get column names
        $columns = array_keys($rows[0]);
        foreach ($columns as $column) {
            if (!is_numeric($column)) {
                echo "<th>$column</th>";
            }
        }

        echo "</tr></thead>";
        echo "<tbody>";

        foreach ($rows as $row) {
            echo "<tr>";
            foreach ($columns as $column) {
                if (!is_numeric($column)) {
                    echo "<td>" . htmlspecialchars($row[$column]) . "</td>";
                }
            }
            echo "</tr>";
        }

        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

// Get a quiz ID from the URL or use a default
$activityId = isset($_GET['id']) ? intval($_GET['id']) : 7; // Default to quiz ID 7

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_question'])) {
    $questionText = trim($_POST['question_text']);
    $questionType = $_POST['question_type'];
    $points = intval($_POST['points']);

    if (!empty($questionText) && !empty($questionType) && $points > 0) {
        // Use the appropriate function for quiz questions
        $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);

        if (is_numeric($result)) {
            $questionId = $result;
            echo "<div class='alert alert-success'>Question added successfully with ID: $questionId</div>";

            // Handle different question types
            if ($questionType == 'multiple_choice') {
                // Add multiple choice options
                $correctAnswer = trim($_POST['mc_correct_answer']);

                // Add options A, B, C, D if they exist
                if (!empty($_POST['option_a'])) {
                    $optionA = trim($_POST['option_a']);
                    $isCorrect = ($correctAnswer == 'A');
                    $optionResult = addQuizOption($questionId, $optionA, $isCorrect);
                    echo "<div class='alert alert-info'>Option A Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }

                if (!empty($_POST['option_b'])) {
                    $optionB = trim($_POST['option_b']);
                    $isCorrect = ($correctAnswer == 'B');
                    $optionResult = addQuizOption($questionId, $optionB, $isCorrect);
                    echo "<div class='alert alert-info'>Option B Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }
            }
            elseif ($questionType == 'true_false') {
                // Add true/false options
                $correctAnswer = $_POST['tf_correct_answer'];

                // Add True option
                $isCorrect = ($correctAnswer == 'true');
                $optionResult = addQuizOption($questionId, 'True', $isCorrect);
                echo "<div class='alert alert-info'>True Option Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";

                // Add False option
                $isCorrect = ($correctAnswer == 'false');
                $optionResult = addQuizOption($questionId, 'False', $isCorrect);
                echo "<div class='alert alert-info'>False Option Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>Error: $result</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>Please fill in all question fields.</div>";
    }
}

// Get the SQL for the addQuizActivityQuestion function
$addQuizActivityQuestionSQL = "
INSERT INTO quiz_questions (activity_id, question_text, question_type, points, position)
VALUES (:activityId, :questionText, :questionType, :points, :position)
";

// Get the SQL for the addQuizOption function
$addQuizOptionSQL = "
INSERT INTO quiz_options (question_id, option_text, is_correct, position)
VALUES (:questionId, :optionText, :isCorrect, :position)
";
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Debug Quiz Functions</h1>
            <p>This page helps debug the quiz functions and database structure.</p>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Database Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display table structure
                    displayTableStructure('quiz_questions');
                    displayTableStructure('quiz_options');
                    ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Database Data</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display table data
                    displayTableData('quiz_questions');
                    displayTableData('quiz_options');
                    ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">SQL Statements</h5>
                </div>
                <div class="card-body">
                    <h3>addQuizActivityQuestion SQL</h3>
                    <pre class="bg-light p-3"><?php echo htmlspecialchars($addQuizActivityQuestionSQL); ?></pre>

                    <h3>addQuizOption SQL</h3>
                    <pre class="bg-light p-3"><?php echo htmlspecialchars($addQuizOptionSQL); ?></pre>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Test Add Question</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post">
                        <div class="form-group">
                            <label for="question_text">Question Text</label>
                            <input type="text" name="question_text" id="question_text" class="form-control" value="Test Question" required>
                        </div>

                        <div class="form-group">
                            <label for="question_type">Question Type</label>
                            <select name="question_type" id="question_type" class="form-control" required>
                                <option value="multiple_choice">Multiple Choice</option>
                                <option value="true_false">True/False</option>
                                <option value="short_answer">Short Answer</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="points">Points</label>
                            <input type="number" name="points" id="points" class="form-control" value="1" min="1" required>
                        </div>

                        <div class="form-group">
                            <label for="option_a">Option A</label>
                            <input type="text" name="option_a" id="option_a" class="form-control" value="Option A">
                        </div>

                        <div class="form-group">
                            <label for="option_b">Option B</label>
                            <input type="text" name="option_b" id="option_b" class="form-control" value="Option B">
                        </div>

                        <div class="form-group">
                            <label for="mc_correct_answer">Correct Answer</label>
                            <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                <option value="A">Option A</option>
                                <option value="B">Option B</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tf_correct_answer">True/False Correct Answer</label>
                            <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                <option value="true">True</option>
                                <option value="false">False</option>
                            </select>
                        </div>

                        <input type="hidden" name="add_question" value="1">
                        <button type="submit" class="btn btn-warning">Test Add Question</button>
                    </form>
                </div>
            </div>

            <div class="text-center mb-4">
                <a href="fix_quiz_db.php" class="btn btn-danger mr-2">Fix Database</a>
                <a href="quiz_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-primary">Return to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
