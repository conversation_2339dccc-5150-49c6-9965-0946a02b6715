<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

echo "<h1>Setting up Google Classroom-like Tables</h1>";

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Create modules table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS modules (
            module_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Modules table created or already exists.</p>";
    
    // Create materials table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS materials (
            material_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            module_id INT,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    echo "<p>✓ Materials table created or already exists.</p>";
    
    // Create assignments table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS assignments (
            assignment_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            module_id INT,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            points INT NOT NULL DEFAULT 100,
            due_date DATETIME,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    echo "<p>✓ Assignments table created or already exists.</p>";
    
    // Create submissions table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS submissions (
            submission_id INT AUTO_INCREMENT PRIMARY KEY,
            assignment_id INT NOT NULL,
            user_id INT NOT NULL,
            content TEXT NOT NULL,
            file_path VARCHAR(255),
            submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_graded BOOLEAN DEFAULT FALSE,
            score FLOAT,
            feedback TEXT,
            graded_by INT,
            graded_date DATETIME,
            FOREIGN KEY (assignment_id) REFERENCES assignments(assignment_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id),
            FOREIGN KEY (graded_by) REFERENCES users(user_id)
        )
    ");
    echo "<p>✓ Submissions table created or already exists.</p>";
    
    // Create announcements table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS announcements (
            announcement_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    echo "<p>✓ Announcements table created or already exists.</p>";
    
    // Create quizzes table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS quizzes (
            quiz_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            module_id INT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            points INT NOT NULL DEFAULT 100,
            time_limit INT,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    echo "<p>✓ Quizzes table created or already exists.</p>";
    
    // Create course_enrollments table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS course_enrollments (
            enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            user_id INT NOT NULL,
            enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id),
            UNIQUE KEY (course_id, user_id)
        )
    ");
    echo "<p>✓ Course enrollments table created or already exists.</p>";
    
    // Create course_instructors table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS course_instructors (
            instructor_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            user_id INT NOT NULL,
            assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id),
            UNIQUE KEY (course_id, user_id)
        )
    ");
    echo "<p>✓ Course instructors table created or already exists.</p>";
    
    // Commit transaction
    $pdo->commit();
    
    echo "<div style='margin-top: 20px; padding: 10px; background-color: #d4edda; color: #155724; border-radius: 5px;'>";
    echo "<h3>✓ All tables created successfully!</h3>";
    echo "<p>Your database is now set up with all the necessary tables for Google Classroom-like features.</p>";
    echo "</div>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='index.php' class='btn btn-primary'>Return to Dashboard</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    
    echo "<div style='margin-top: 20px; padding: 10px; background-color: #f8d7da; color: #721c24; border-radius: 5px;'>";
    echo "<h3>❌ Error setting up tables</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
