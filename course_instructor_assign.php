<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is authorized (admin or course creator)
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You are not authorized to assign instructors.";
    header("location: index.php");
    exit;
}

// Check if form was submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate input
    if (!isset($_POST['course_id']) || empty($_POST['course_id']) || 
        !isset($_POST['instructor_id']) || empty($_POST['instructor_id'])) {
        $_SESSION['error'] = "Missing required fields.";
        header("location: index.php");
        exit;
    }
    
    $courseId = intval($_POST['course_id']);
    $instructorId = intval($_POST['instructor_id']);
    
    // Check if user is the course creator (if not admin)
    if (!isAdmin()) {
        $course = getCourseById($courseId);
        if (is_string($course) || $course['created_by'] != $_SESSION['user_id']) {
            $_SESSION['error'] = "You are not authorized to assign instructors to this course.";
            header("location: course_view_full.php?id=$courseId&tab=people");
            exit;
        }
    }
    
    // Assign the instructor
    $result = assignInstructor($courseId, $instructorId);
    
    if ($result === true) {
        $_SESSION['success'] = "Instructor assigned successfully.";
    } else {
        $_SESSION['error'] = $result;
    }
    
    // Redirect back to the course people tab
    header("location: course_view_full.php?id=$courseId&tab=people");
    exit;
} else {
    // Not a POST request, redirect to home
    header("location: index.php");
    exit;
}
?>
