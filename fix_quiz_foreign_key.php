<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth_functions.php';

// Include header
require_once 'includes/header.php';

// Check if the user is logged in and is an admin or teacher
if (!isLoggedIn() || (!isAdmin() && !isTeacher())) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Function to execute SQL with error handling
function executeSQL($sql, $description) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute();
        
        if ($result) {
            echo "<div class='alert alert-success'>$description: Success</div>";
        } else {
            echo "<div class='alert alert-danger'>$description: Failed</div>";
        }
        
        return $result;
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>$description: " . $e->getMessage() . "</div>";
        return false;
    }
}

// Function to check if a table exists
function tableExists($tableName) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE :tableName");
        $stmt->bindParam(':tableName', $tableName);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to display table structure
function displayTableStructure($tableName) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DESCRIBE $tableName");
        $stmt->execute();
        
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table: $tableName</h3>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

// Function to display foreign keys
function displayForeignKeys($tableName) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                CONSTRAINT_NAME, 
                COLUMN_NAME, 
                REFERENCED_TABLE_NAME, 
                REFERENCED_COLUMN_NAME
            FROM 
                information_schema.KEY_COLUMN_USAGE
            WHERE 
                TABLE_SCHEMA = DATABASE() AND
                TABLE_NAME = :tableName AND
                REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $stmt->bindParam(':tableName', $tableName);
        $stmt->execute();
        
        $foreignKeys = $stmt->fetchAll();
        
        echo "<h3>Foreign Keys for Table: $tableName</h3>";
        
        if (count($foreignKeys) > 0) {
            echo "<table class='table table-bordered table-striped'>";
            echo "<thead><tr><th>Constraint Name</th><th>Column</th><th>Referenced Table</th><th>Referenced Column</th></tr></thead>";
            echo "<tbody>";
            
            foreach ($foreignKeys as $fk) {
                echo "<tr>";
                echo "<td>" . $fk['CONSTRAINT_NAME'] . "</td>";
                echo "<td>" . $fk['COLUMN_NAME'] . "</td>";
                echo "<td>" . $fk['REFERENCED_TABLE_NAME'] . "</td>";
                echo "<td>" . $fk['REFERENCED_COLUMN_NAME'] . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-info'>No foreign keys found for this table.</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Fix Quiz Foreign Key Constraints</h1>
            <p>This page fixes the foreign key constraints for quiz tables.</p>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Foreign Key Constraints</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display current foreign keys
                    if (tableExists('quiz_questions')) {
                        displayForeignKeys('quiz_questions');
                    } else {
                        echo "<div class='alert alert-warning'>quiz_questions table does not exist.</div>";
                    }
                    
                    if (tableExists('quiz_options')) {
                        displayForeignKeys('quiz_options');
                    } else {
                        echo "<div class='alert alert-warning'>quiz_options table does not exist.</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Fix Foreign Key Constraints</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Fix foreign key constraints
                    if (tableExists('quiz_questions')) {
                        // Check if the foreign key constraint exists
                        $stmt = $pdo->prepare("
                            SELECT * FROM information_schema.TABLE_CONSTRAINTS 
                            WHERE CONSTRAINT_SCHEMA = DATABASE() 
                            AND TABLE_NAME = 'quiz_questions' 
                            AND CONSTRAINT_NAME = 'quiz_questions_ibfk_1'
                        ");
                        $stmt->execute();
                        
                        if ($stmt->rowCount() > 0) {
                            // Drop the existing foreign key constraint
                            $sql = "ALTER TABLE quiz_questions DROP FOREIGN KEY quiz_questions_ibfk_1;";
                            executeSQL($sql, "Drop existing foreign key constraint from quiz_questions table");
                        }
                        
                        // Check if the activities table exists
                        if (tableExists('activities')) {
                            // Add the correct foreign key constraint
                            $sql = "ALTER TABLE quiz_questions ADD CONSTRAINT quiz_questions_activity_fk FOREIGN KEY (activity_id) REFERENCES activities (activity_id) ON DELETE CASCADE;";
                            executeSQL($sql, "Add correct foreign key constraint to quiz_questions table");
                        } else {
                            echo "<div class='alert alert-danger'>activities table does not exist. Cannot create foreign key constraint.</div>";
                        }
                    }
                    
                    // Fix foreign key constraint for quiz_options
                    if (tableExists('quiz_options')) {
                        // Check if the foreign key constraint exists
                        $stmt = $pdo->prepare("
                            SELECT * FROM information_schema.TABLE_CONSTRAINTS 
                            WHERE CONSTRAINT_SCHEMA = DATABASE() 
                            AND TABLE_NAME = 'quiz_options' 
                            AND CONSTRAINT_NAME = 'quiz_options_ibfk_1'
                        ");
                        $stmt->execute();
                        
                        if ($stmt->rowCount() > 0) {
                            // Drop the existing foreign key constraint
                            $sql = "ALTER TABLE quiz_options DROP FOREIGN KEY quiz_options_ibfk_1;";
                            executeSQL($sql, "Drop existing foreign key constraint from quiz_options table");
                        }
                        
                        // Add the correct foreign key constraint
                        $sql = "ALTER TABLE quiz_options ADD CONSTRAINT quiz_options_question_fk FOREIGN KEY (question_id) REFERENCES quiz_questions (question_id) ON DELETE CASCADE;";
                        executeSQL($sql, "Add correct foreign key constraint to quiz_options table");
                    }
                    ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Updated Foreign Key Constraints</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display updated foreign keys
                    if (tableExists('quiz_questions')) {
                        displayForeignKeys('quiz_questions');
                    }
                    
                    if (tableExists('quiz_options')) {
                        displayForeignKeys('quiz_options');
                    }
                    ?>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <a href="quiz_edit.php?id=7#questions" class="btn btn-primary">Return to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
