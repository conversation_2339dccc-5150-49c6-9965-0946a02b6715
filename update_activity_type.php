<?php
// Include configuration file
require_once 'includes/config.php';

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "No activity ID provided.";
    exit;
}

$activityId = intval($_GET['id']);

// Connect to the database
$conn = getConnection();

// Get current activity type
$sql = "SELECT activity_id, title, activity_type FROM activities WHERE activity_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $activityId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "Activity not found.";
    $stmt->close();
    $conn->close();
    exit;
}

$activity = $result->fetch_assoc();
$currentType = $activity['activity_type'];

// Display activity details
echo "<h2>Activity Details</h2>";
echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
echo "<p><strong>Current Type:</strong> " . htmlspecialchars($currentType) . "</p>";

// Check if we should update the activity type
if (isset($_GET['new_type']) && !empty($_GET['new_type'])) {
    $newType = $_GET['new_type'];
    
    // Update the activity type
    $updateSql = "UPDATE activities SET activity_type = ? WHERE activity_id = ?";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param("si", $newType, $activityId);
    
    if ($updateStmt->execute()) {
        echo "<p style='color: green;'>Activity type updated from '{$currentType}' to '{$newType}'.</p>";
        echo "<p>Please <a href='activity_edit.php?id=$activityId'>click here</a> to go to the edit page.</p>";
    } else {
        echo "<p style='color: red;'>Error updating activity type: " . $conn->error . "</p>";
    }
    
    $updateStmt->close();
} else {
    // Show form to update activity type
    echo "<form method='get'>";
    echo "<input type='hidden' name='id' value='$activityId'>";
    echo "<div>";
    echo "<label for='new_type'>New Activity Type:</label>";
    echo "<select name='new_type' id='new_type'>";
    echo "<option value='activity'" . ($currentType == 'activity' ? " selected" : "") . ">Activity</option>";
    echo "<option value='assignment'" . ($currentType == 'assignment' ? " selected" : "") . ">Assignment</option>";
    echo "<option value='quiz'" . ($currentType == 'quiz' ? " selected" : "") . ">Quiz</option>";
    echo "<option value='announcement'" . ($currentType == 'announcement' ? " selected" : "") . ">Announcement</option>";
    echo "</select>";
    echo "</div>";
    echo "<div style='margin-top: 10px;'>";
    echo "<button type='submit'>Update Activity Type</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<p><a href='activity_edit.php?id=$activityId'>Go to Edit Page</a></p>";
    echo "<p><a href='activity_edit_replacement.php?id=$activityId'>Use New Edit Page</a></p>";
    echo "</div>";
}

// Close database connection
$stmt->close();
$conn->close();
?>
