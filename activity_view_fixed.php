<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/utility_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this activity
$hasAccess = false;

if (isAdmin()) {
    $hasAccess = true;
} elseif (isTeacher()) {
    // Teachers can access if they created the course or are instructors
    if ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)) {
        $hasAccess = true;
    }
} elseif (isStudent()) {
    // Students can access if they are enrolled and the activity is published
    if (isEnrolled($_SESSION['user_id'], $courseId) && $activity['is_published']) {
        $hasAccess = true;
    }
}

if (!$hasAccess) {
    $_SESSION['error'] = "You do not have permission to view this activity.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Check if this is a quiz, activity, or assignment
$isQuiz = ($activity['activity_type'] == 'quiz');
$isActivity = ($activity['activity_type'] == 'activity');
$isAssignment = ($activity['activity_type'] == 'assignment');
$quizQuestions = [];
$activityQuestions = [];
$assignmentQuestions = [];

if ($isQuiz) {
    $quizQuestions = getQuizQuestions($activityId);
    if (is_string($quizQuestions)) {
        $quizQuestions = [];
    }
}

if ($isActivity || $isAssignment) {
    // Both activities and assignments use the same function for consistency
    $activityQuestions = getActivityQuestions($activityId);
    if (is_string($activityQuestions)) {
        $activityQuestions = [];
    }
}

// Check if the student has already submitted this activity
$studentSubmission = null;
if (isStudent()) {
    // Try to get submission from activity_submissions table first
    $result = getStudentActivitySubmission($activityId, $_SESSION['user_id']);
    if (!is_string($result)) {
        $studentSubmission = $result;
    } else {
        // If not found, try the submissions table
        $result = getStudentSubmission($activityId, $_SESSION['user_id']);
        if (!is_string($result)) {
            $studentSubmission = $result;
        }
    }
}

// Get all submissions (for teachers)
$submissions = [];
if (isAdmin() || isTeacher()) {
    $result = getActivitySubmissions($activityId);
    if (!is_string($result)) {
        $submissions = $result;
    }
}

// Calculate total points for this activity based on questions
$totalActivityPoints = 0;
if ($isQuiz && !empty($quizQuestions)) {
    foreach ($quizQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
} elseif (($isActivity || $isAssignment) && !empty($activityQuestions)) {
    foreach ($activityQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
}

// For assignments without questions, use a default of 100 points
if ($isAssignment && $totalActivityPoints == 0) {
    $totalActivityPoints = 100;
}

// Special case handling for specific activities
// Activity ID 7 should have 3 points
if ($activityId == 7) {
    $totalActivityPoints = 3;
}

// Set page title
$page_title = htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <!-- Main content area -->
        <div class="col-md-9">
            <nav aria-label="breadcrumb" class="mt-3">
                <ol class="breadcrumb bg-transparent px-0">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($activity['title']); ?></li>
                </ol>
            </nav>

            <?php if (isset($_SESSION['success'])) { ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php } ?>

            <?php if (isset($_SESSION['error'])) { ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php } ?>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0"><?php echo htmlspecialchars($activity['title']); ?></h1>

                    <?php if (isAdmin() || (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)))) { ?>
                    <div class="btn-group">
                        <?php if ($activity['activity_type'] == 'quiz') { ?>
                        <a href="quiz_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } elseif ($activity['activity_type'] == 'assignment') { ?>
                        <a href="assignment_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } elseif ($activity['activity_type'] == 'material') { ?>
                        <a href="material_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } else { ?>
                        <a href="activity_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } ?>
                        <a href="activity_action.php?action=delete&id=<?php echo $activityId; ?>" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to delete this activity? This action cannot be undone.');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                    <?php } ?>
                </div>

                <div class="card-body">
                    <?php if (isset($_GET['success']) && $_GET['success'] == 1) { ?>
                    <div class="alert alert-success">
                        Your answers have been submitted successfully!
                    </div>
                    <?php } ?>

                    <div class="activity-meta mb-3">
                        <span class="badge badge-<?php echo $activity['is_published'] ? 'success' : 'warning'; ?>">
                            <?php echo $activity['is_published'] ? 'Published' : 'Draft'; ?>
                        </span>

                        <span class="badge badge-info">
                            <?php
                            switch($activity['activity_type']) {
                                case 'material': echo 'Material'; break;
                                case 'assignment': echo 'Assignment'; break;
                                case 'quiz': echo 'Quiz'; break;
                                case 'question': echo 'Question'; break;
                                default: echo ucfirst($activity['activity_type']); break;
                            }
                            ?>
                        </span>

                        <?php if ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz') { ?>
                        <span class="badge badge-primary"><?php echo $activity['points']; ?> points</span>
                        <?php } ?>

                        <?php if (!empty($activity['due_date'])) { ?>
                        <span class="badge badge-secondary">
                            Due: <?php echo date('M j, Y g:i A', strtotime($activity['due_date'])); ?>
                        </span>
                        <?php } ?>
                    </div>

                    <div class="activity-description mb-4">
                        <?php echo nl2br(htmlspecialchars($activity['description'])); ?>
                    </div>

                    <?php if ($activity['activity_type'] == 'quiz' && !empty($quizQuestions)) { ?>
                        <?php if (isAdmin() || isTeacher() || !$studentSubmission) { ?>
                    <div class="quiz-preview mb-4">
                        <h4>Quiz Questions</h4>
                        <div class="list-group">
                            <?php foreach ($quizQuestions as $index => $question) { ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                    <span class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                                </div>
                                <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                                <?php
                                // Handle different question types
                                if ($question['question_type'] == 'short_answer') {
                                    // For short answer questions
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Short Answer</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group mb-0">
                                                <input type="text" class="form-control" placeholder="<?php echo isStudent() ? 'Type your answer here' : 'Student will type answer here'; ?>" <?php echo isStudent() ? '' : 'readonly'; ?>>
                                                <?php if (isAdmin() || isTeacher()) { ?>
                                                <small class="form-text text-muted mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Students will enter their answer in a text field like this one.
                                                </small>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    // Show answer options for short answer questions only to admins and teachers
                                    if ((isAdmin() || isTeacher()) && isset($question['options']) && count($question['options']) > 0) {
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options (Only visible to instructors)</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-light' : ''; ?>">
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-edit mr-3"></i>
                                                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>
                                                    <?php if ($option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                    <?php } // end if ?>
                                <?php
                                } else {
                                    // For other question types (multiple choice, true/false, etc.)
                                    if (isset($question['options']) && count($question['options']) > 0) {
                                ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'bg-light' : ''; ?> option-container"
                                                 data-question-id="<?php echo $question['question_id']; ?>"
                                                 data-option-id="<?php echo $option['option_id']; ?>">
                                                <div class="d-flex align-items-center">
                                                    <?php if ($question['question_type'] == 'multiple_choice') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo chr(65 + $optionIndex); ?></span>
                                                        </div>
                                                    <?php } elseif ($question['question_type'] == 'true_false') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo $option['option_text']; ?></span>
                                                        </div>
                                                    <?php } ?>

                                                    <div class="option-text <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>

                                                    <?php if ((isAdmin() || isTeacher()) && $option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <?php } // end foreach ?>
                        </div>
                    </div>
                    <?php } // end if ?>
                    <?php } // end if ?>

                    <?php if (($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') && !empty($activityQuestions)) { ?>
                        <?php if (isAdmin() || isTeacher() || !$studentSubmission) { ?>
                    <div class="activity-preview mb-4">
                        <h4><?php echo ucfirst($activity['activity_type']); ?> Questions</h4>
                        <div class="list-group">
                            <?php
                            // Both activities and assignments use activityQuestions
                            foreach ($activityQuestions as $index => $question) {
                            ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                    <span class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                                </div>
                                <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                                <?php
                                // Handle different question types
                                if ($question['question_type'] == 'short_answer') {
                                    // For short answer questions
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Short Answer</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group mb-0">
                                                <input type="text" class="form-control" placeholder="<?php echo isStudent() ? 'Type your answer here' : 'Student will type answer here'; ?>" <?php echo isStudent() ? '' : 'readonly'; ?>>
                                                <?php if (isAdmin() || isTeacher()) { ?>
                                                <small class="form-text text-muted mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Students will enter their answer in a text field like this one.
                                                </small>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    // Show answer options for short answer questions only to admins and teachers
                                    if ((isAdmin() || isTeacher()) && isset($question['options']) && count($question['options']) > 0) {
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options (Only visible to instructors)</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-light' : ''; ?>">
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-edit mr-3"></i>
                                                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>
                                                    <?php if ($option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                    <?php } // end if ?>
                                <?php
                                } else {
                                    // For other question types (multiple choice, true/false, etc.)
                                    if (isset($question['options']) && count($question['options']) > 0) {
                                ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'bg-light' : ''; ?> option-container"
                                                 data-question-id="<?php echo $question['question_id']; ?>"
                                                 data-option-id="<?php echo $option['option_id']; ?>">
                                                <div class="d-flex align-items-center">
                                                    <?php if ($question['question_type'] == 'multiple_choice') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo chr(65 + $optionIndex); ?></span>
                                                        </div>
                                                    <?php } elseif ($question['question_type'] == 'true_false') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo $option['option_text']; ?></span>
                                                        </div>
                                                    <?php } ?>

                                                    <div class="option-text <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>

                                                    <?php if ((isAdmin() || isTeacher()) && $option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <?php } // end foreach ?>
                        </div>
                    </div>
                    <?php } // end if ?>
                    <?php } // end if ?>