<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/assessment_functions.php';
require_once 'includes/quiz_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can take quizzes.";
    header("location: index.php");
    exit;
}

// Check if quiz ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Quiz ID is required.";
    header("location: index.php");
    exit;
}

$quizId = intval($_GET['id']);

// Get quiz details
$quiz = getActivityById($quizId);
if (is_string($quiz)) {
    $_SESSION['error'] = $quiz;
    header("location: index.php");
    exit;
}

// Check if the quiz is a quiz type
if ($quiz['activity_type'] != 'quiz') {
    $_SESSION['error'] = "This is not a quiz.";
    header("location: index.php");
    exit;
}

// Check if the quiz is published
if (!$quiz['is_published']) {
    $_SESSION['error'] = "This quiz is not available yet.";
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $quiz['course_id'];
$course = getCourseById($courseId);
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if student is enrolled in the course
if (!isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You are not enrolled in this course.";
    header("location: index.php");
    exit;
}

// Check if the student has already submitted this quiz
$studentSubmission = null;
// Try to get submission from activity_submissions table first
$result = getStudentActivitySubmission($quizId, $_SESSION['user_id']);
if (!is_string($result)) {
    $studentSubmission = $result;

    // If already submitted, redirect to the quiz view page
    $_SESSION['info'] = "You have already submitted this quiz.";
    header("location: activity_view.php?id=$quizId");
    exit;
} else {
    // If not found, try the submissions table
    $result = getStudentSubmission($quizId, $_SESSION['user_id']);
    if (!is_string($result)) {
        $studentSubmission = $result;

        // If already submitted, redirect to the quiz view page
        $_SESSION['info'] = "You have already submitted this quiz.";
        header("location: activity_view.php?id=$quizId");
        exit;
    }
}

// Check if the due date has passed and late submissions are not allowed
$dueDatePassed = false;
if (!empty($quiz['due_date'])) {
    $dueDate = new DateTime($quiz['due_date']);
    $now = new DateTime();
    $dueDatePassed = ($now > $dueDate);

    if ($dueDatePassed && !$quiz['allow_late_submissions']) {
        $_SESSION['error'] = "The due date for this quiz has passed and late submissions are not allowed.";
        header("location: activity_view.php?id=$quizId");
        exit;
    }
}

// Get quiz questions
$questions = getQuizQuestions($quizId);
if (is_string($questions)) {
    $_SESSION['error'] = $questions;
    header("location: activity_view.php?id=$quizId");
    exit;
}

// Debug
error_log("Quiz questions: " . print_r($questions, true));

// Initialize variables
$answers = [];
$success = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_quiz'])) {
    // Process answers
    foreach ($questions as $question) {
        $questionId = $question['question_id'];
        $questionType = $question['question_type'];

        if ($questionType == 'multiple_choice' || $questionType == 'true_false') {
            if (isset($_POST["answer_$questionId"])) {
                $answers[$questionId] = $_POST["answer_$questionId"];
            }
        } elseif ($questionType == 'short_answer') {
            if (isset($_POST["answer_$questionId"])) {
                $answers[$questionId] = trim($_POST["answer_$questionId"]);
            }
        }
    }

    // Check if all questions are answered
    $allAnswered = true;
    foreach ($questions as $question) {
        $questionId = $question['question_id'];
        if (!isset($answers[$questionId]) || (is_string($answers[$questionId]) && trim($answers[$questionId]) === '')) {
            $allAnswered = false;
            break;
        }
    }

    if (!$allAnswered) {
        $_SESSION['error'] = "Please answer all questions before submitting.";
    } else {
        // Submit the quiz
        // Note: is_late is handled internally in submitActivity based on due date
        $result = submitActivity($quizId, $_SESSION['user_id'], null, $answers, null, null, null, null);

        if (is_numeric($result)) {
            // Submission successful
            $_SESSION['success'] = "Quiz submitted successfully!";
            header("location: activity_view.php?id=$quizId");
            exit;
        } else {
            // Error submitting quiz
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Take Quiz: " . htmlspecialchars($quiz['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-9">
            <!-- Back button -->
            <div class="mb-3">
                <a href="activity_view.php?id=<?php echo $quizId; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Quiz
                </a>
            </div>

            <h1 class="mb-3">Quiz: <?php echo htmlspecialchars($quiz['title']); ?></h1>

            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Quiz Information</h5>
                        <?php if (!empty($quiz['due_date'])): ?>
                            <span class="badge <?php echo $dueDatePassed ? 'badge-danger' : 'badge-info'; ?>">
                                Due: <?php echo date('F j, Y, g:i a', strtotime($quiz['due_date'])); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <p><?php echo nl2br(htmlspecialchars($quiz['description'])); ?></p>
                    <?php if (!empty($quiz['time_limit'])): ?>
                        <p><strong>Time Limit:</strong> <?php echo $quiz['time_limit']; ?> minutes</p>
                    <?php endif; ?>
                    <p><strong>Total Questions:</strong> <?php echo count($questions); ?></p>
                </div>
            </div>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $quizId); ?>" method="post" id="quizForm">
                <?php if (count($questions) > 0): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quiz Questions</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($questions as $index => $question): ?>
                                <div class="question-container mb-4 pb-4 <?php echo $index < count($questions) - 1 ? 'border-bottom' : ''; ?>">
                                    <h5 class="question-text mb-3">
                                        <span class="badge badge-primary mr-2"><?php echo $index + 1; ?></span>
                                        <?php echo htmlspecialchars($question['question_text']); ?>
                                        <span class="badge badge-secondary ml-2"><?php echo $question['points']; ?> points</span>
                                    </h5>

                                    <?php
                                    // Use options from the question if available, otherwise get them
                                    if (isset($question['options'])) {
                                        $options = $question['options'];
                                    } else {
                                        $options = getQuestionOptions($question['question_id']);
                                        error_log("Options for question " . $question['question_id'] . ": " . print_r($options, true));
                                        if (is_string($options)) {
                                            $options = [];
                                        }
                                    }

                                    // Display different input types based on question type
                                    if ($question['question_type'] == 'multiple_choice'):
                                    ?>
                                        <div class="options-container">
                                            <?php foreach ($options as $option): ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_<?php echo $option['option_id']; ?>" value="<?php echo $option['option_id']; ?>" <?php echo isset($answers[$question['question_id']]) && $answers[$question['question_id']] == $option['option_id'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="option_<?php echo $option['option_id']; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php elseif ($question['question_type'] == 'true_false'): ?>
                                        <div class="options-container">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_true_<?php echo $question['question_id']; ?>" value="true" <?php echo isset($answers[$question['question_id']]) && $answers[$question['question_id']] == 'true' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="option_true_<?php echo $question['question_id']; ?>">
                                                    True
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_false_<?php echo $question['question_id']; ?>" value="false" <?php echo isset($answers[$question['question_id']]) && $answers[$question['question_id']] == 'false' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="option_false_<?php echo $question['question_id']; ?>">
                                                    False
                                                </label>
                                            </div>
                                        </div>
                                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                        <div class="form-group">
                                            <input type="text" class="form-control" name="answer_<?php echo $question['question_id']; ?>" placeholder="Your answer" value="<?php echo isset($answers[$question['question_id']]) ? htmlspecialchars($answers[$question['question_id']]) : ''; ?>">
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" name="submit_quiz" class="btn btn-primary btn-lg">Submit Quiz</button>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <p>This quiz has no questions yet.</p>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quiz Navigation</h5>
                </div>
                <div class="card-body">
                    <div class="question-nav">
                        <?php foreach ($questions as $index => $question): ?>
                            <a href="#question_<?php echo $question['question_id']; ?>" class="btn btn-outline-primary mb-2 question-nav-btn" data-question="<?php echo $index + 1; ?>">
                                Question <?php echo $index + 1; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
