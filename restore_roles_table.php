<?php
/**
 * Restore Roles Table Script
 * 
 * This script recreates the roles table and updates the users table to use role_id again.
 */

// Include configuration file
require_once 'includes/config.php';

// Create a connection using mysqli
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Restoring Roles Table</h1>";

// Step 1: Check if the roles table exists
$result = $conn->query("SHOW TABLES LIKE 'roles'");
$roles_exist = $result->num_rows > 0;

if ($roles_exist) {
    echo "Roles table already exists.<br>";
} else {
    // Create the roles table
    $sql = "CREATE TABLE roles (
        role_id INT AUTO_INCREMENT PRIMARY KEY,
        role_name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Roles table created successfully.<br>";
        
        // Insert default roles
        $sql = "INSERT INTO roles (role_name, description) VALUES 
            ('admin', 'Administrator with full system access'),
            ('teacher', 'Teacher who can create and manage courses'),
            ('student', 'Student who can enroll in courses')";
        
        if ($conn->query($sql) === TRUE) {
            echo "Default roles inserted successfully.<br>";
        } else {
            echo "Error inserting default roles: " . $conn->error . "<br>";
        }
    } else {
        echo "Error creating roles table: " . $conn->error . "<br>";
    }
}

// Step 2: Check if users table has role_id column
$result = $conn->query("SHOW COLUMNS FROM users LIKE 'role_id'");
$has_role_id = $result->num_rows > 0;

if ($has_role_id) {
    echo "Users table already has role_id column.<br>";
} else {
    // Add role_id column to users table
    $sql = "ALTER TABLE users ADD COLUMN role_id INT AFTER last_name";
    
    if ($conn->query($sql) === TRUE) {
        echo "Added role_id column to users table.<br>";
    } else {
        echo "Error adding role_id column: " . $conn->error . "<br>";
    }
    
    // Add foreign key constraint
    $sql = "ALTER TABLE users ADD CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES roles(role_id)";
    
    if ($conn->query($sql) === TRUE) {
        echo "Added foreign key constraint to role_id column.<br>";
    } else {
        echo "Error adding foreign key constraint: " . $conn->error . "<br>";
    }
}

// Step 3: Check if users table has role column
$result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
$has_role = $result->num_rows > 0;

if ($has_role) {
    // Update role_id based on role
    $sql = "UPDATE users u JOIN roles r ON u.role = r.role_name SET u.role_id = r.role_id";
    
    if ($conn->query($sql) === TRUE) {
        echo "Updated role_id values based on role column.<br>";
        
        // Drop the role column
        $sql = "ALTER TABLE users DROP COLUMN role";
        
        if ($conn->query($sql) === TRUE) {
            echo "Dropped role column from users table.<br>";
        } else {
            echo "Error dropping role column: " . $conn->error . "<br>";
        }
    } else {
        echo "Error updating role_id values: " . $conn->error . "<br>";
    }
} else {
    echo "Users table does not have role column. No update needed.<br>";
}

echo "<h2>Database Structure Restored</h2>";
echo "<p>The roles table has been recreated and the users table has been updated to use role_id again.</p>";
echo "<p><a href='login.php'>Go to Login Page</a></p>";

// Close the connection
$conn->close();
?>
