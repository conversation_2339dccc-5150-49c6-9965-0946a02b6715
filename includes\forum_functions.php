<?php
/**
 * Forum Functions
 * 
 * This file contains functions related to forums, topics, and replies.
 */

require_once 'config.php';
require_once 'course_functions.php';

/**
 * Function to create a new forum
 * Ad<PERSON> can create forums for any course, teachers can only create forums for their own courses
 * 
 * @param int $courseId The course ID
 * @param string $title The forum title
 * @param string $description The forum description
 * @return bool|string Forum ID if creation successful, error message otherwise
 */
function createForum($courseId, $title, $description) {
    global $pdo;
    
    try {
        // Check if user is authorized to create forums for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $course = $stmt->fetch();
                
                if (!isTeacher() || $course['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to create forums for this course.";
                }
            } else {
                return "Course not found.";
            }
        }
        
        // Create the forum
        $stmt = $pdo->prepare("
            INSERT INTO forums (course_id, title, description) 
            VALUES (:courseId, :title, :description)
        ");
        
        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        
        $stmt->execute();
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create forum: " . $e->getMessage();
    }
}

/**
 * Function to update a forum
 * Admin can update any forum, teachers can only update forums in their own courses
 * 
 * @param int $forumId The forum ID
 * @param string $title The forum title
 * @param string $description The forum description
 * @return bool|string True if update successful, error message otherwise
 */
function updateForum($forumId, $title, $description) {
    global $pdo;
    
    try {
        // Check if user is authorized to update this forum
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by 
                FROM forums f
                JOIN courses c ON f.course_id = c.course_id
                WHERE f.forum_id = :forumId
            ");
            $stmt->bindParam(':forumId', $forumId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $forum = $stmt->fetch();
                
                if (!isTeacher() || $forum['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this forum.";
                }
            } else {
                return "Forum not found.";
            }
        }
        
        // Update the forum
        $stmt = $pdo->prepare("
            UPDATE forums 
            SET title = :title, description = :description 
            WHERE forum_id = :forumId
        ");
        
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':forumId', $forumId);
        
        $stmt->execute();
        
        return true;
    } catch (PDOException $e) {
        return "Failed to update forum: " . $e->getMessage();
    }
}

/**
 * Function to delete a forum
 * Admin can delete any forum, teachers can only delete forums in their own courses
 * 
 * @param int $forumId The forum ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteForum($forumId) {
    global $pdo;
    
    try {
        // Check if user is authorized to delete this forum
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by 
                FROM forums f
                JOIN courses c ON f.course_id = c.course_id
                WHERE f.forum_id = :forumId
            ");
            $stmt->bindParam(':forumId', $forumId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $forum = $stmt->fetch();
                
                if (!isTeacher() || $forum['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this forum.";
                }
            } else {
                return "Forum not found.";
            }
        }
        
        // Delete the forum
        $stmt = $pdo->prepare("DELETE FROM forums WHERE forum_id = :forumId");
        $stmt->bindParam(':forumId', $forumId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Forum not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete forum: " . $e->getMessage();
    }
}

/**
 * Function to get all forums for a course
 * 
 * @param int $courseId The course ID
 * @return array|string Array of forums if successful, error message otherwise
 */
function getForumsByCourse($courseId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT forum_id, title, description, created_at
            FROM forums
            WHERE course_id = :courseId
            ORDER BY created_at
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve forums: " . $e->getMessage();
    }
}

/**
 * Function to get a forum by ID
 * 
 * @param int $forumId The forum ID
 * @return array|string Forum data if successful, error message otherwise
 */
function getForumById($forumId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT forum_id, course_id, title, description, created_at
            FROM forums
            WHERE forum_id = :forumId
        ");
        $stmt->bindParam(':forumId', $forumId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Forum not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve forum: " . $e->getMessage();
    }
}

/**
 * Function to create a new topic
 * 
 * @param int $forumId The forum ID
 * @param int $userId The user ID
 * @param string $title The topic title
 * @param string $content The topic content
 * @return bool|string Topic ID if creation successful, error message otherwise
 */
function createTopic($forumId, $userId, $title, $content) {
    global $pdo;
    
    try {
        // Check if the forum exists
        $stmt = $pdo->prepare("SELECT forum_id FROM forums WHERE forum_id = :forumId");
        $stmt->bindParam(':forumId', $forumId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return "Forum not found.";
        }
        
        // Create the topic
        $stmt = $pdo->prepare("
            INSERT INTO forum_topics (forum_id, user_id, title, content) 
            VALUES (:forumId, :userId, :title, :content)
        ");
        
        $stmt->bindParam(':forumId', $forumId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        
        $stmt->execute();
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create topic: " . $e->getMessage();
    }
}

/**
 * Function to update a topic
 * Admin can update any topic, users can only update their own topics
 * 
 * @param int $topicId The topic ID
 * @param string $title The topic title
 * @param string $content The topic content
 * @return bool|string True if update successful, error message otherwise
 */
function updateTopic($topicId, $title, $content) {
    global $pdo;
    
    try {
        // Check if user is authorized to update this topic
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT user_id FROM forum_topics WHERE topic_id = :topicId");
            $stmt->bindParam(':topicId', $topicId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $topic = $stmt->fetch();
                
                if ($topic['user_id'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this topic.";
                }
            } else {
                return "Topic not found.";
            }
        }
        
        // Update the topic
        $stmt = $pdo->prepare("
            UPDATE forum_topics 
            SET title = :title, content = :content 
            WHERE topic_id = :topicId
        ");
        
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':topicId', $topicId);
        
        $stmt->execute();
        
        return true;
    } catch (PDOException $e) {
        return "Failed to update topic: " . $e->getMessage();
    }
}

/**
 * Function to delete a topic
 * Admin can delete any topic, teachers can delete any topic in their courses, users can only delete their own topics
 * 
 * @param int $topicId The topic ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteTopic($topicId) {
    global $pdo;
    
    try {
        // Check if user is authorized to delete this topic
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT t.user_id, c.created_by 
                FROM forum_topics t
                JOIN forums f ON t.forum_id = f.forum_id
                JOIN courses c ON f.course_id = c.course_id
                WHERE t.topic_id = :topicId
            ");
            $stmt->bindParam(':topicId', $topicId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $topic = $stmt->fetch();
                
                if (!isTeacher() || (isTeacher() && $topic['created_by'] != $_SESSION['user_id'])) {
                    if ($topic['user_id'] != $_SESSION['user_id']) {
                        return "You are not authorized to delete this topic.";
                    }
                }
            } else {
                return "Topic not found.";
            }
        }
        
        // Delete the topic
        $stmt = $pdo->prepare("DELETE FROM forum_topics WHERE topic_id = :topicId");
        $stmt->bindParam(':topicId', $topicId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Topic not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete topic: " . $e->getMessage();
    }
}

/**
 * Function to get all topics for a forum
 * 
 * @param int $forumId The forum ID
 * @return array|string Array of topics if successful, error message otherwise
 */
function getTopicsByForum($forumId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT t.topic_id, t.user_id, u.username, u.first_name, u.last_name, 
                   t.title, t.content, t.created_at,
                   COUNT(r.reply_id) as reply_count
            FROM forum_topics t
            JOIN users u ON t.user_id = u.user_id
            LEFT JOIN forum_replies r ON t.topic_id = r.topic_id
            WHERE t.forum_id = :forumId
            GROUP BY t.topic_id, t.user_id, u.username, u.first_name, u.last_name, t.title, t.content, t.created_at
            ORDER BY t.created_at DESC
        ");
        $stmt->bindParam(':forumId', $forumId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve topics: " . $e->getMessage();
    }
}

/**
 * Function to get a topic by ID
 * 
 * @param int $topicId The topic ID
 * @return array|string Topic data if successful, error message otherwise
 */
function getTopicById($topicId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT t.topic_id, t.forum_id, t.user_id, u.username, u.first_name, u.last_name, 
                   t.title, t.content, t.created_at
            FROM forum_topics t
            JOIN users u ON t.user_id = u.user_id
            WHERE t.topic_id = :topicId
        ");
        $stmt->bindParam(':topicId', $topicId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Topic not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve topic: " . $e->getMessage();
    }
}

/**
 * Function to create a new reply
 * 
 * @param int $topicId The topic ID
 * @param int $userId The user ID
 * @param string $content The reply content
 * @return bool|string Reply ID if creation successful, error message otherwise
 */
function createReply($topicId, $userId, $content) {
    global $pdo;
    
    try {
        // Check if the topic exists
        $stmt = $pdo->prepare("SELECT topic_id FROM forum_topics WHERE topic_id = :topicId");
        $stmt->bindParam(':topicId', $topicId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return "Topic not found.";
        }
        
        // Create the reply
        $stmt = $pdo->prepare("
            INSERT INTO forum_replies (topic_id, user_id, content) 
            VALUES (:topicId, :userId, :content)
        ");
        
        $stmt->bindParam(':topicId', $topicId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':content', $content);
        
        $stmt->execute();
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create reply: " . $e->getMessage();
    }
}

/**
 * Function to update a reply
 * Admin can update any reply, users can only update their own replies
 * 
 * @param int $replyId The reply ID
 * @param string $content The reply content
 * @return bool|string True if update successful, error message otherwise
 */
function updateReply($replyId, $content) {
    global $pdo;
    
    try {
        // Check if user is authorized to update this reply
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT user_id FROM forum_replies WHERE reply_id = :replyId");
            $stmt->bindParam(':replyId', $replyId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $reply = $stmt->fetch();
                
                if ($reply['user_id'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this reply.";
                }
            } else {
                return "Reply not found.";
            }
        }
        
        // Update the reply
        $stmt = $pdo->prepare("
            UPDATE forum_replies 
            SET content = :content 
            WHERE reply_id = :replyId
        ");
        
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':replyId', $replyId);
        
        $stmt->execute();
        
        return true;
    } catch (PDOException $e) {
        return "Failed to update reply: " . $e->getMessage();
    }
}

/**
 * Function to delete a reply
 * Admin can delete any reply, teachers can delete any reply in their courses, users can only delete their own replies
 * 
 * @param int $replyId The reply ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteReply($replyId) {
    global $pdo;
    
    try {
        // Check if user is authorized to delete this reply
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT r.user_id, c.created_by 
                FROM forum_replies r
                JOIN forum_topics t ON r.topic_id = t.topic_id
                JOIN forums f ON t.forum_id = f.forum_id
                JOIN courses c ON f.course_id = c.course_id
                WHERE r.reply_id = :replyId
            ");
            $stmt->bindParam(':replyId', $replyId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $reply = $stmt->fetch();
                
                if (!isTeacher() || (isTeacher() && $reply['created_by'] != $_SESSION['user_id'])) {
                    if ($reply['user_id'] != $_SESSION['user_id']) {
                        return "You are not authorized to delete this reply.";
                    }
                }
            } else {
                return "Reply not found.";
            }
        }
        
        // Delete the reply
        $stmt = $pdo->prepare("DELETE FROM forum_replies WHERE reply_id = :replyId");
        $stmt->bindParam(':replyId', $replyId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Reply not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete reply: " . $e->getMessage();
    }
}

/**
 * Function to get all replies for a topic
 * 
 * @param int $topicId The topic ID
 * @return array|string Array of replies if successful, error message otherwise
 */
function getRepliesByTopic($topicId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT r.reply_id, r.user_id, u.username, u.first_name, u.last_name, 
                   r.content, r.created_at
            FROM forum_replies r
            JOIN users u ON r.user_id = u.user_id
            WHERE r.topic_id = :topicId
            ORDER BY r.created_at
        ");
        $stmt->bindParam(':topicId', $topicId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve replies: " . $e->getMessage();
    }
}

/**
 * Function to get a reply by ID
 * 
 * @param int $replyId The reply ID
 * @return array|string Reply data if successful, error message otherwise
 */
function getReplyById($replyId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT r.reply_id, r.topic_id, r.user_id, u.username, u.first_name, u.last_name, 
                   r.content, r.created_at
            FROM forum_replies r
            JOIN users u ON r.user_id = u.user_id
            WHERE r.reply_id = :replyId
        ");
        $stmt->bindParam(':replyId', $replyId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Reply not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve reply: " . $e->getMessage();
    }
}
?>
