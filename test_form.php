<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Initialize variables
$message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received in test_form.php: " . print_r($_POST, true));
    
    // Display success message
    $message = "Form submitted successfully! Check the error log for details.";
}

// Set page title
$page_title = "Test Form";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Test Form</h1>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="title">Title</label>
                            <input type="text" name="title" id="title" class="form-control" value="Test Title">
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea name="description" id="description" class="form-control" rows="4">Test Description</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="due_date">Due Date</label>
                            <input type="datetime-local" name="due_date" id="due_date" class="form-control" value="<?php echo date('Y-m-d\TH:i'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" checked>
                                <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" checked>
                                <label class="custom-control-label" for="is_published">Publish</label>
                            </div>
                        </div>
                        
                        <div class="form-group text-right">
                            <input type="hidden" name="update_activity" value="1">
                            <button type="submit" class="btn btn-primary" id="test-submit-btn">Submit Test Form</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form submission handler
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Test form submitted');
            
            // Log form data
            const formData = new FormData(this);
            for (const pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }
            
            // Continue with form submission
            return true;
        });
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
