<?php
// Include configuration file
require_once 'includes/config.php';

// Check if quiz ID is provided
if (!isset($_GET['id'])) {
    echo "<h2>Please provide a quiz ID</h2>";
    echo "<p>Example: fix_quiz.php?id=7</p>";
    exit;
}

$quizId = intval($_GET['id']);

try {
    // Get current activity details
    $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :quizId");
    $stmt->bindParam(':quizId', $quizId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        echo "<h2>Quiz not found</h2>";
        echo "<p>No quiz found with ID: $quizId</p>";
        exit;
    }
    
    $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentType = $quiz['activity_type'];
    
    echo "<h2>Quiz Details</h2>";
    echo "<p><strong>ID:</strong> " . $quiz['activity_id'] . "</p>";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($quiz['title']) . "</p>";
    echo "<p><strong>Current Type:</strong> " . htmlspecialchars($currentType) . "</p>";
    
    // Update the activity type to 'quiz'
    $newType = 'quiz';
    $updateStmt = $pdo->prepare("UPDATE activities SET activity_type = :newType WHERE activity_id = :quizId");
    $updateStmt->bindParam(':newType', $newType);
    $updateStmt->bindParam(':quizId', $quizId);
    
    if ($updateStmt->execute()) {
        echo "<p style='color: green;'>Quiz type updated from '{$currentType}' to '{$newType}'.</p>";
        
        // Check if the activity_type enum includes 'quiz'
        $stmt = $pdo->query("SHOW COLUMNS FROM activities LIKE 'activity_type'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column) {
            $type = $column['Type'];
            echo "<p>Current activity_type definition: " . htmlspecialchars($type) . "</p>";
            
            // Check if 'quiz' is already in the enum
            if (strpos($type, "'quiz'") === false) {
                // Add 'quiz' to the enum
                $newTypeEnum = str_replace("enum('material','assignment','question','activity')", 
                                         "enum('material','assignment','question','activity','quiz')", $type);
                
                // Alter the table
                $pdo->exec("ALTER TABLE activities MODIFY COLUMN activity_type " . $newTypeEnum);
                
                echo "<p>✓ Successfully added 'quiz' to the activity_type enum.</p>";
            } else {
                echo "<p>The 'quiz' type is already included in the activity_type enum.</p>";
            }
        }
        
        echo "<p>Please <a href='activity_edit.php?id=$quizId#questions'>click here</a> to go to the edit page with the Questions tab active.</p>";
    } else {
        echo "<p style='color: red;'>Error updating quiz type.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
