<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/quiz_functions.php';

// Set page title
$page_title = "Test Quiz Question";

// Include header
require_once 'includes/header.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Get a quiz ID from the URL or use a default
$activityId = isset($_GET['id']) ? intval($_GET['id']) : 7; // Default to quiz ID 7

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists and is a quiz
if (is_string($activity)) {
    echo "<div class='alert alert-danger'>$activity</div>";
    require_once 'includes/footer.php';
    exit;
}

if ($activity['activity_type'] != 'quiz') {
    echo "<div class='alert alert-danger'>This activity is not a quiz.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_question'])) {
    $questionText = trim($_POST['question_text']);
    $questionType = $_POST['question_type'];
    $points = intval($_POST['points']);
    
    if (!empty($questionText) && !empty($questionType) && $points > 0) {
        // Use the appropriate function for quiz questions
        $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);
        
        if (is_numeric($result)) {
            $questionId = $result;
            echo "<div class='alert alert-success'>Question added successfully with ID: $questionId</div>";
            
            // Handle different question types
            if ($questionType == 'multiple_choice') {
                // Add multiple choice options
                $correctAnswer = trim($_POST['mc_correct_answer']);
                
                // Add options A, B, C, D if they exist
                if (!empty($_POST['option_a'])) {
                    $optionA = trim($_POST['option_a']);
                    $isCorrect = ($correctAnswer == 'A');
                    $optionResult = addQuizOption($questionId, $optionA, $isCorrect);
                    echo "<div class='alert alert-info'>Option A Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }
                
                if (!empty($_POST['option_b'])) {
                    $optionB = trim($_POST['option_b']);
                    $isCorrect = ($correctAnswer == 'B');
                    $optionResult = addQuizOption($questionId, $optionB, $isCorrect);
                    echo "<div class='alert alert-info'>Option B Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }
                
                if (!empty($_POST['option_c'])) {
                    $optionC = trim($_POST['option_c']);
                    $isCorrect = ($correctAnswer == 'C');
                    $optionResult = addQuizOption($questionId, $optionC, $isCorrect);
                    echo "<div class='alert alert-info'>Option C Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }
                
                if (!empty($_POST['option_d'])) {
                    $optionD = trim($_POST['option_d']);
                    $isCorrect = ($correctAnswer == 'D');
                    $optionResult = addQuizOption($questionId, $optionD, $isCorrect);
                    echo "<div class='alert alert-info'>Option D Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                }
            }
            elseif ($questionType == 'true_false') {
                // Add true/false options
                $correctAnswer = $_POST['tf_correct_answer'];
                
                // Add True option
                $isCorrect = ($correctAnswer == 'true');
                $optionResult = addQuizOption($questionId, 'True', $isCorrect);
                echo "<div class='alert alert-info'>True Option Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
                
                // Add False option
                $isCorrect = ($correctAnswer == 'false');
                $optionResult = addQuizOption($questionId, 'False', $isCorrect);
                echo "<div class='alert alert-info'>False Option Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
            }
            elseif ($questionType == 'short_answer' && isset($_POST['sa_correct_answer'])) {
                // Add short answer correct answer
                $correctAnswer = trim($_POST['sa_correct_answer']);
                $optionResult = addQuizOption($questionId, $correctAnswer, true);
                echo "<div class='alert alert-info'>Short Answer Result: " . (is_numeric($optionResult) ? "Success (ID: $optionResult)" : $optionResult) . "</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>Error: $result</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>Please fill in all question fields.</div>";
    }
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Test Quiz Question</h1>
            <p>This page tests the functionality of adding questions to a quiz.</p>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add New Question to Quiz ID: <?php echo $activityId; ?></h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post" id="testQuestionForm">
                        <div class="form-group">
                            <label for="question_text">Question Text <span class="text-danger">*</span></label>
                            <textarea name="question_text" id="question_text" class="form-control" rows="3" required>Test Question</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="question_type">Type:</label>
                            <select name="question_type" id="question_type" class="form-control" onchange="toggleQuestionOptions()">
                                <option value="multiple_choice">Multiple Choice</option>
                                <option value="true_false">True/False</option>
                                <option value="short_answer">Short Answer</option>
                            </select>
                        </div>
                        
                        <!-- Multiple Choice Options -->
                        <div id="multiple_choice_options" class="question-options">
                            <div class="form-group">
                                <label for="option_a">Option A:</label>
                                <input type="text" name="option_a" id="option_a" class="form-control" value="Option A">
                            </div>
                            <div class="form-group">
                                <label for="option_b">Option B:</label>
                                <input type="text" name="option_b" id="option_b" class="form-control" value="Option B">
                            </div>
                            <div class="form-group">
                                <label for="option_c">Option C:</label>
                                <input type="text" name="option_c" id="option_c" class="form-control" value="Option C">
                            </div>
                            <div class="form-group">
                                <label for="option_d">Option D:</label>
                                <input type="text" name="option_d" id="option_d" class="form-control" value="Option D">
                            </div>
                            <div class="form-group">
                                <label for="mc_correct_answer">Correct Answer:</label>
                                <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                    <option value="A">Option A</option>
                                    <option value="B">Option B</option>
                                    <option value="C">Option C</option>
                                    <option value="D">Option D</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- True/False Options -->
                        <div id="true_false_options" class="question-options" style="display: none;">
                            <div class="form-group">
                                <label for="tf_correct_answer">Correct Answer:</label>
                                <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                    <option value="true">True</option>
                                    <option value="false">False</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Short Answer Options -->
                        <div id="short_answer_options" class="question-options" style="display: none;">
                            <div class="form-group">
                                <label for="sa_correct_answer">Correct Answer:</label>
                                <input type="text" name="sa_correct_answer" id="sa_correct_answer" class="form-control" value="Correct Answer">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="points">Points <span class="text-danger">*</span></label>
                            <input type="number" name="points" id="points" class="form-control" value="1" min="1" required>
                        </div>
                        
                        <div class="form-group text-right">
                            <input type="hidden" name="add_question" value="1">
                            <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                            <button type="submit" class="btn btn-success" id="testAddQuestionBtn">Test Add Question</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="quiz_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-primary">Go to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<script>
function toggleQuestionOptions() {
    var questionType = document.getElementById('question_type').value;
    var multipleChoiceOptions = document.getElementById('multiple_choice_options');
    var trueFalseOptions = document.getElementById('true_false_options');
    var shortAnswerOptions = document.getElementById('short_answer_options');
    
    // Hide all options first
    multipleChoiceOptions.style.display = 'none';
    trueFalseOptions.style.display = 'none';
    shortAnswerOptions.style.display = 'none';
    
    // Show the appropriate options based on question type
    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
    } else if (questionType === 'true_false') {
        trueFalseOptions.style.display = 'block';
    } else if (questionType === 'short_answer') {
        shortAnswerOptions.style.display = 'block';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleQuestionOptions();
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
