<?php
// Include configuration file
require_once 'includes/config.php';

// Set page title
$page_title = "Fix Quiz Database";

// Include header
require_once 'includes/header.php';

// Check if the user is logged in and is an admin or teacher
if (!isLoggedIn() || (!isAdmin() && !isTeacher())) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Function to execute SQL with error handling
function executeSQL($sql, $description) {
    global $pdo;

    try {
        $pdo->exec($sql);
        echo "<div class='alert alert-success'>$description: Success</div>";
        return true;
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>$description: Error - " . $e->getMessage() . "</div>";
        return false;
    }
}

// Function to check if a table exists
function tableExists($tableName) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE :tableName");
        $stmt->bindParam(':tableName', $tableName);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to check if a column exists in a table
function columnExists($tableName, $columnName) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM $tableName LIKE :columnName");
        $stmt->bindParam(':columnName', $columnName);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to display table structure
function displayTableStructure($tableName) {
    global $pdo;

    try {
        // Get table structure
        $stmt = $pdo->prepare("DESCRIBE $tableName");
        $stmt->execute();
        $columns = $stmt->fetchAll();

        echo "<h3>Table: $tableName</h3>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
        echo "<tbody>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }

        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
}

// Check if tables exist
$quizQuestionsExists = tableExists('quiz_questions');
$quizOptionsExists = tableExists('quiz_options');

// Create tables if they don't exist
if (!$quizQuestionsExists) {
    $sql = "CREATE TABLE quiz_questions (
        question_id INT(11) NOT NULL AUTO_INCREMENT,
        activity_id INT(11) NOT NULL,
        question_text TEXT NOT NULL,
        question_type VARCHAR(50) NOT NULL,
        points INT(11) NOT NULL DEFAULT 1,
        position INT(11) NOT NULL DEFAULT 0,
        PRIMARY KEY (question_id)
    )";
    executeSQL($sql, "Create quiz_questions table");
    $quizQuestionsExists = true;
}

if (!$quizOptionsExists) {
    $sql = "CREATE TABLE quiz_options (
        option_id INT(11) NOT NULL AUTO_INCREMENT,
        question_id INT(11) NOT NULL,
        option_text TEXT NOT NULL,
        is_correct TINYINT(1) NOT NULL DEFAULT 0,
        position INT(11) NOT NULL DEFAULT 0,
        PRIMARY KEY (option_id)
    )";
    executeSQL($sql, "Create quiz_options table");
    $quizOptionsExists = true;
}

// Check if columns exist
$hasActivityId = false;
$hasPosition = false;
$hasOptionPosition = false;

if ($quizQuestionsExists) {
    $hasQuizId = columnExists('quiz_questions', 'quiz_id');
    $hasActivityId = columnExists('quiz_questions', 'activity_id');
    $hasPosition = columnExists('quiz_questions', 'position');
}

if ($quizOptionsExists) {
    $hasOptionPosition = columnExists('quiz_options', 'position');
}

?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Fix Quiz Database</h1>
            <p>This page fixes the database structure for quiz tables.</p>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Table Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display current structure
                    if ($quizQuestionsExists) {
                        displayTableStructure('quiz_questions');
                    } else {
                        echo "<div class='alert alert-warning'>quiz_questions table does not exist.</div>";
                    }

                    if ($quizOptionsExists) {
                        displayTableStructure('quiz_options');
                    } else {
                        echo "<div class='alert alert-warning'>quiz_options table does not exist.</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Update Database Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Update quiz_questions table
                    if ($quizQuestionsExists) {
                        if ($hasQuizId && !$hasActivityId) {
                            // Rename quiz_id to activity_id
                            $sql = "ALTER TABLE quiz_questions CHANGE quiz_id activity_id INT(11) NOT NULL;";
                            executeSQL($sql, "Rename quiz_id to activity_id in quiz_questions table");
                            $hasActivityId = true;
                        } elseif (!$hasQuizId && !$hasActivityId) {
                            // Add activity_id column
                            $sql = "ALTER TABLE quiz_questions ADD COLUMN activity_id INT(11) NOT NULL AFTER question_id;";
                            executeSQL($sql, "Add activity_id column to quiz_questions table");
                            $hasActivityId = true;
                        } elseif ($hasQuizId && $hasActivityId) {
                            // Both columns exist, drop quiz_id
                            $sql = "ALTER TABLE quiz_questions DROP COLUMN quiz_id;";
                            executeSQL($sql, "Drop quiz_id column from quiz_questions table");
                        } else {
                            echo "<div class='alert alert-info'>quiz_questions table already has activity_id column.</div>";
                        }

                        // Add position column to quiz_questions if it doesn't exist
                        if (!$hasPosition) {
                            $sql = "ALTER TABLE quiz_questions ADD COLUMN position INT(11) NOT NULL DEFAULT 0 AFTER points;";
                            executeSQL($sql, "Add position column to quiz_questions table");
                        } else {
                            echo "<div class='alert alert-info'>quiz_questions table already has position column.</div>";
                        }
                    }

                    // Add position column to quiz_options if it doesn't exist
                    if ($quizOptionsExists && !$hasOptionPosition) {
                        $sql = "ALTER TABLE quiz_options ADD COLUMN position INT(11) NOT NULL DEFAULT 0 AFTER is_correct;";
                        executeSQL($sql, "Add position column to quiz_options table");
                    } elseif ($quizOptionsExists) {
                        echo "<div class='alert alert-info'>quiz_options table already has position column.</div>";
                    }

                    // Check and fix foreign key constraints
                    try {
                        // Check if the foreign key constraint exists
                        $stmt = $pdo->prepare("
                            SELECT * FROM information_schema.TABLE_CONSTRAINTS
                            WHERE CONSTRAINT_SCHEMA = DATABASE()
                            AND TABLE_NAME = 'quiz_questions'
                            AND CONSTRAINT_NAME = 'quiz_questions_ibfk_1'
                        ");
                        $stmt->execute();

                        if ($stmt->rowCount() > 0) {
                            // Drop the existing foreign key constraint
                            $sql = "ALTER TABLE quiz_questions DROP FOREIGN KEY quiz_questions_ibfk_1;";
                            executeSQL($sql, "Drop existing foreign key constraint from quiz_questions table");

                            // Add the correct foreign key constraint
                            $sql = "ALTER TABLE quiz_questions ADD CONSTRAINT quiz_questions_activity_fk FOREIGN KEY (activity_id) REFERENCES activities (activity_id) ON DELETE CASCADE;";
                            executeSQL($sql, "Add correct foreign key constraint to quiz_questions table");
                        } else {
                            // Add the correct foreign key constraint if it doesn't exist
                            $sql = "ALTER TABLE quiz_questions ADD CONSTRAINT quiz_questions_activity_fk FOREIGN KEY (activity_id) REFERENCES activities (activity_id) ON DELETE CASCADE;";
                            executeSQL($sql, "Add foreign key constraint to quiz_questions table");
                        }
                    } catch (PDOException $e) {
                        echo "<div class='alert alert-warning'>Foreign key operation: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Updated Table Structure</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Display updated structure
                    if ($quizQuestionsExists) {
                        displayTableStructure('quiz_questions');
                    }

                    if ($quizOptionsExists) {
                        displayTableStructure('quiz_options');
                    }
                    ?>
                </div>
            </div>

            <div class="text-center mb-4">
                <a href="quiz_edit.php?id=7#questions" class="btn btn-primary">Return to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
