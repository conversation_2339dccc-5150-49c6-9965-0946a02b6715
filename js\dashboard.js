// Dashboard Charts and Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts if the elements exist
    if (document.getElementById('enrollmentChart')) {
        initEnrollmentChart();
    }

    if (document.getElementById('userDistributionChart')) {
        initUserDistributionChart();
    }

    // Print dashboard functionality
    const printButton = document.getElementById('printDashboard');
    if (printButton) {
        printButton.addEventListener('click', function() {
            window.print();
        });
    }

    // Chart tabs functionality
    const chartTabs = document.querySelectorAll('.chart-tabs button');
    if (chartTabs.length > 0) {
        chartTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                chartTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Get chart type from data attribute
                const chartType = this.getAttribute('data-chart-type');

                // Log which tab was clicked
                console.log('Chart tab clicked:', chartType);

                // Show visual feedback
                const tabText = this.textContent.trim();
                document.querySelector('.card-footer small:first-child').innerHTML =
                    `<i class="fas fa-info-circle"></i> Showing <strong>${tabText}</strong> data for the last 6 months`;

                // Update chart based on selected tab
                updateChartView(chartType);

                // Add a small animation to the chart
                const chartCanvas = document.getElementById('enrollmentChart');
                if (chartCanvas) {
                    chartCanvas.style.transition = 'opacity 0.3s ease';
                    chartCanvas.style.opacity = '0.7';
                    setTimeout(() => {
                        chartCanvas.style.opacity = '1';
                    }, 300);
                }
            });
        });
    }

    // Course filter dropdown functionality
    const courseFilters = document.querySelectorAll('[data-filter]');
    if (courseFilters.length > 0) {
        courseFilters.forEach(filter => {
            filter.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all filters
                courseFilters.forEach(f => f.classList.remove('active'));

                // Add active class to clicked filter
                this.classList.add('active');

                // Get filter type from data attribute
                const filterType = this.getAttribute('data-filter');

                // Update dropdown button text
                const dropdownButton = document.getElementById('courseFilterDropdown');
                if (dropdownButton) {
                    dropdownButton.textContent = 'Filter: ' + this.textContent.trim();
                }

                // Filter courses based on selected filter
                filterCourses(filterType);
            });
        });
    }

    // Time range dropdown functionality
    const timeRangeItems = document.querySelectorAll('[data-time-range]');
    if (timeRangeItems.length > 0) {
        timeRangeItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                timeRangeItems.forEach(i => i.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Get time range from data attribute
                const timeRange = this.getAttribute('data-time-range');

                // Update dropdown button text
                const dropdownButton = this.closest('.btn-group').querySelector('button');
                if (dropdownButton) {
                    dropdownButton.innerHTML = '<i class="fas fa-calendar-alt"></i> ' + this.textContent.trim();
                }

                // Update dashboard data based on selected time range
                updateDashboardByTimeRange(timeRange);
            });
        });
    }

    // Semester filter dropdown functionality
    const semesterFilters = document.querySelectorAll('[data-semester-filter]');
    if (semesterFilters.length > 0) {
        semesterFilters.forEach(filter => {
            filter.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all filters
                semesterFilters.forEach(f => f.classList.remove('active'));

                // Add active class to clicked filter
                this.classList.add('active');

                // Get semester from data attribute
                const semester = this.getAttribute('data-semester-filter');

                console.log('Semester filter clicked:', semester); // Debug log

                // Update dropdown button text and style
                const dropdownButton = this.closest('.btn-group').querySelector('button');
                if (dropdownButton) {
                    dropdownButton.innerHTML = '<i class="fas fa-graduation-cap"></i> ' + this.textContent.trim();

                    // Change button style based on filter
                    dropdownButton.classList.remove('btn-outline-info', 'btn-info');
                    if (semester === 'all') {
                        dropdownButton.classList.add('btn-outline-info');
                    } else {
                        dropdownButton.classList.add('btn-info');
                    }
                }

                // Close the dropdown menu
                const dropdownMenu = this.closest('.dropdown-menu');
                if (dropdownMenu && dropdownButton) {
                    // Try Bootstrap's dropdown API first
                    if (typeof $ !== 'undefined' && $(dropdownButton).dropdown) {
                        $(dropdownButton).dropdown('hide');
                    } else {
                        // Fallback: manually close dropdown
                        dropdownButton.setAttribute('aria-expanded', 'false');
                        dropdownMenu.classList.remove('show');
                        dropdownButton.classList.remove('show');
                    }
                }

                // Update dashboard data based on selected semester
                updateDashboardBySemester(semester);
            });
        });
    }

    // Time range filter dropdown functionality
    const timeRangeFilters = document.querySelectorAll('[data-time-range]');
    if (timeRangeFilters.length > 0) {
        timeRangeFilters.forEach(filter => {
            filter.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all filters
                timeRangeFilters.forEach(f => f.classList.remove('active'));

                // Add active class to clicked filter
                this.classList.add('active');

                // Get time range from data attribute
                const timeRange = this.getAttribute('data-time-range');

                console.log('Time range filter clicked:', timeRange); // Debug log

                // Update dropdown button text and style
                const dropdownButton = this.closest('.btn-group').querySelector('button');
                if (dropdownButton) {
                    dropdownButton.innerHTML = '<i class="fas fa-calendar-alt"></i> ' + this.textContent.trim();

                    // Change button style based on filter
                    dropdownButton.classList.remove('btn-outline-secondary', 'btn-secondary');
                    if (timeRange === 'month') {
                        dropdownButton.classList.add('btn-outline-secondary');
                    } else {
                        dropdownButton.classList.add('btn-secondary');
                    }
                }

                // Close the dropdown menu
                const dropdownMenu = this.closest('.dropdown-menu');
                if (dropdownMenu && dropdownButton) {
                    // Try Bootstrap's dropdown API first
                    if (typeof $ !== 'undefined' && $(dropdownButton).dropdown) {
                        $(dropdownButton).dropdown('hide');
                    } else {
                        // Fallback: manually close dropdown
                        dropdownButton.setAttribute('aria-expanded', 'false');
                        dropdownMenu.classList.remove('show');
                        dropdownButton.classList.remove('show');
                    }
                }

                // Update dashboard data based on selected time range
                updateDashboardByTimeRange(timeRange);
            });
        });
    }

    // Course filter dropdown functionality
    const courseFilters = document.querySelectorAll('[data-filter]');
    if (courseFilters.length > 0) {
        courseFilters.forEach(filter => {
            filter.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all filters
                courseFilters.forEach(f => f.classList.remove('active'));

                // Add active class to clicked filter
                this.classList.add('active');

                // Get filter type from data attribute
                const filterType = this.getAttribute('data-filter');

                console.log('Course filter clicked:', filterType); // Debug log

                // Update dropdown button text
                const dropdownButton = this.closest('.dropdown').querySelector('button');
                if (dropdownButton) {
                    dropdownButton.textContent = this.textContent.trim();
                }

                // Close the dropdown menu
                const dropdownMenu = this.closest('.dropdown-menu');
                if (dropdownMenu && dropdownButton) {
                    // Try Bootstrap's dropdown API first
                    if (typeof $ !== 'undefined' && $(dropdownButton).dropdown) {
                        $(dropdownButton).dropdown('hide');
                    } else {
                        // Fallback: manually close dropdown
                        dropdownButton.setAttribute('aria-expanded', 'false');
                        dropdownMenu.classList.remove('show');
                        dropdownButton.classList.remove('show');
                    }
                }

                // Filter courses based on selected filter
                filterCourses(filterType);
            });
        });
    }
});

// Course Enrollment Trends Chart
function initEnrollmentChart() {
    const ctx = document.getElementById('enrollmentChart').getContext('2d');

    // Sample data - in a real application, this would come from the server
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();
    const lastSixMonths = months.slice(currentMonth - 5, currentMonth + 1);

    // Create chart and make it globally accessible
    window.enrollmentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: lastSixMonths,
            datasets: [
                {
                    label: 'New Enrollments',
                    data: [12, 19, 15, 25, 32, 28],
                    borderColor: '#4285F4',
                    backgroundColor: 'rgba(66, 133, 244, 0.1)',
                    borderWidth: 2,
                    pointBackgroundColor: '#4285F4',
                    pointRadius: 4,
                    tension: 0.3,
                    fill: true
                },
                {
                    label: 'Course Completions',
                    data: [5, 12, 8, 15, 18, 20],
                    borderColor: '#0F9D58',
                    backgroundColor: 'rgba(15, 157, 88, 0.1)',
                    borderWidth: 2,
                    pointBackgroundColor: '#0F9D58',
                    pointRadius: 4,
                    tension: 0.3,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Enrollment Trends',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 20
                    }
                },
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#202124',
                    bodyColor: '#5f6368',
                    borderColor: '#DADCE0',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    callbacks: {
                        labelPointStyle: function(context) {
                            return {
                                pointStyle: 'circle',
                                rotation: 0
                            };
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        borderDash: [2, 4],
                        color: '#DADCE0'
                    },
                    ticks: {
                        stepSize: 10
                    }
                }
            }
        }
    });
}

// User Distribution Chart
function initUserDistributionChart() {
    const ctx = document.getElementById('userDistributionChart').getContext('2d');

    // Create chart and make it globally accessible
    window.userDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Students', 'Teachers', 'Admins'],
            datasets: [{
                data: [48, 8, 2],
                backgroundColor: [
                    '#4285F4',  // Blue for students
                    '#0F9D58',  // Green for teachers
                    '#F4B400'   // Yellow for admins
                ],
                borderColor: [
                    '#ffffff',
                    '#ffffff',
                    '#ffffff'
                ],
                borderWidth: 2,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '65%',  // Slightly less cutout to make the chart more visible
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 14  // Larger font for better readability
                        },
                        generateLabels: function(chart) {
                            const data = chart.data;
                            if (data.labels.length && data.datasets.length) {
                                return data.labels.map(function(label, i) {
                                    const meta = chart.getDatasetMeta(0);
                                    const style = meta.controller.getStyle(i);
                                    const value = data.datasets[0].data[i];

                                    // Create more descriptive labels with counts
                                    return {
                                        text: `${label}: ${value}`,
                                        fillStyle: data.datasets[0].backgroundColor[i],
                                        strokeStyle: data.datasets[0].borderColor[i],
                                        lineWidth: data.datasets[0].borderWidth,
                                        hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
                                        index: i
                                    };
                                });
                            }
                            return [];
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#202124',
                    bodyColor: '#5f6368',
                    borderColor: '#DADCE0',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                },
                // Add title to make the chart purpose clearer
                title: {
                    display: true,
                    text: 'User Distribution by Role',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 20
                    }
                }
            }
        }
    });
}

// Function to update chart view based on selected tab
function updateChartView(chartType) {
    const chart = window.enrollmentChart;
    if (!chart) return;

    // Update chart title and data based on selected tab
    switch(chartType) {
        case 'enrollments':
            // Show both datasets
            chart.data.datasets[0].hidden = false;
            chart.data.datasets[1].hidden = false;

            // Update colors to make it more distinct
            chart.data.datasets[0].borderColor = '#4285F4';
            chart.data.datasets[0].backgroundColor = 'rgba(66, 133, 244, 0.1)';
            chart.data.datasets[1].borderColor = '#0F9D58';
            chart.data.datasets[1].backgroundColor = 'rgba(15, 157, 88, 0.1)';

            // Update chart title
            chart.options.plugins.title = {
                display: true,
                text: 'Enrollment Trends',
                font: {
                    size: 16,
                    weight: 'bold'
                },
                padding: {
                    top: 10,
                    bottom: 20
                }
            };
            break;

        case 'completions':
            // Hide enrollments, show only completions
            chart.data.datasets[0].hidden = true;
            chart.data.datasets[1].hidden = false;

            // Make completions more prominent
            chart.data.datasets[1].borderColor = '#0F9D58';
            chart.data.datasets[1].backgroundColor = 'rgba(15, 157, 88, 0.2)';
            chart.data.datasets[1].borderWidth = 3;

            // Update chart title
            chart.options.plugins.title = {
                display: true,
                text: 'Course Completion Trends',
                font: {
                    size: 16,
                    weight: 'bold'
                },
                padding: {
                    top: 10,
                    bottom: 20
                }
            };
            break;

        case 'activity':
            // Hide completions, show only enrollments
            chart.data.datasets[0].hidden = false;
            chart.data.datasets[1].hidden = true;

            // Make activity more prominent
            chart.data.datasets[0].borderColor = '#F4B400';
            chart.data.datasets[0].backgroundColor = 'rgba(244, 180, 0, 0.2)';
            chart.data.datasets[0].borderWidth = 3;

            // Update chart title
            chart.options.plugins.title = {
                display: true,
                text: 'User Activity Trends',
                font: {
                    size: 16,
                    weight: 'bold'
                },
                padding: {
                    top: 10,
                    bottom: 20
                }
            };
            break;
    }

    // Update the chart
    chart.update();

    // Log the current state
    console.log('Chart updated to show:', chartType);

    // Return the chart type for other functions to use
    return chartType;
}

// Function to filter courses based on selected filter
function filterCourses(filterType) {
    // In a real application, this would make an AJAX request to get filtered data
    // For this demo, we'll simulate different sorting/filtering

    const tableBody = document.querySelector('.table tbody');
    if (!tableBody) return;

    // Get all rows
    const rows = Array.from(tableBody.querySelectorAll('tr'));
    if (rows.length <= 1) return; // No data or only one row

    // Sort rows based on filter type
    rows.sort((a, b) => {
        const aData = getCourseData(a, filterType);
        const bData = getCourseData(b, filterType);

        return bData - aData; // Descending order
    });

    // Clear table body
    tableBody.innerHTML = '';

    // Append sorted rows
    rows.forEach(row => {
        tableBody.appendChild(row);
    });
}

// Helper function to get course data for sorting
function getCourseData(row, filterType) {
    switch(filterType) {
        case 'enrollment':
            // Get student count from second column
            const studentCell = row.cells[1];
            return studentCell ? parseInt(studentCell.textContent) || 0 : 0;

        case 'completion':
            // Get completion percentage from third column
            const completionCell = row.cells[2];
            if (completionCell) {
                const smallText = completionCell.querySelector('small');
                if (smallText) {
                    return parseInt(smallText.textContent) || 0;
                }
            }
            return 0;

        case 'activity':
            // For activity, we'll use a combination of enrollment and completion
            // In a real app, this would be actual activity data
            const students = row.cells[1] ? parseInt(row.cells[1].textContent) || 0 : 0;
            const completion = row.cells[2] && row.cells[2].querySelector('small') ?
                parseInt(row.cells[2].querySelector('small').textContent) || 0 : 0;

            return students * (completion / 100);

        default:
            return 0;
    }
}

// Function to update dashboard data based on selected time range
function updateDashboardByTimeRange(timeRange) {
    console.log('Updating dashboard for time range:', timeRange);

    // Show loading state with enhanced visual effects
    const dashboardContent = document.getElementById('dashboardContent');
    if (dashboardContent) {
        dashboardContent.classList.add('updating');
        dashboardContent.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        dashboardContent.style.opacity = '0.7';
        dashboardContent.style.transform = 'translateY(10px)';
    }

    // Make AJAX request to get filtered data
    fetch(`ajax/dashboard_time_filter.php?timeRange=${timeRange}`, {
        method: 'GET',
        credentials: 'same-origin', // Include cookies for session
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received time range data:', data); // Debug log

            if (data.success) {
                // Update dashboard stats with real data
                updateDashboardStatsWithTimeRange(data);

                // Update charts with new data
                updateChartsWithTimeRange(data);

                // Show success message
                console.log('Dashboard updated successfully for time range:', timeRange);
            } else {
                console.error('Error updating dashboard:', data.error);
                showErrorMessage('Failed to update dashboard: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error fetching time range data:', error);
            showErrorMessage('Failed to fetch dashboard data. Please try again.');
        })
        .finally(() => {
            // Remove loading state
            if (dashboardContent) {
                dashboardContent.classList.remove('updating');
                dashboardContent.style.opacity = '1';
                dashboardContent.style.transform = 'translateY(0)';
            }
        });
}

// Function to update dashboard stats with time range data
function updateDashboardStatsWithTimeRange(data) {
    // Update Active Users count
    const activeUsersElement = document.querySelector('.dashboard-card:nth-child(1) h3');
    if (activeUsersElement && data.counts && data.counts.active_users !== undefined) {
        activeUsersElement.textContent = data.counts.active_users;
    }

    // Update growth percentages
    if (data.growth) {
        // Update Active Users growth
        const activeUsersGrowth = document.querySelector('.dashboard-card:nth-child(1) .text-success');
        if (activeUsersGrowth && data.growth.active_users) {
            activeUsersGrowth.innerHTML = '<i class="fas fa-arrow-up"></i> ' + data.growth.active_users;
        }

        // Update Students growth
        const studentsGrowth = document.querySelector('.dashboard-card:nth-child(2) .text-success');
        if (studentsGrowth && data.growth.students) {
            studentsGrowth.innerHTML = '<i class="fas fa-arrow-up"></i> ' + data.growth.students;
        }

        // Update Teachers growth
        const teachersGrowth = document.querySelector('.dashboard-card:nth-child(3) .text-success');
        if (teachersGrowth && data.growth.teachers) {
            teachersGrowth.innerHTML = '<i class="fas fa-arrow-up"></i> ' + data.growth.teachers;
        }

        // Update Courses growth
        const coursesGrowth = document.querySelector('.dashboard-card:nth-child(4) .text-success');
        if (coursesGrowth && data.growth.courses) {
            coursesGrowth.innerHTML = '<i class="fas fa-arrow-up"></i> ' + data.growth.courses;
        }
    }

    // Add visual feedback
    const statsCards = document.querySelectorAll('.dashboard-card');
    statsCards.forEach(card => {
        card.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
        card.style.transform = 'scale(1.02)';
        card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';

        setTimeout(() => {
            card.style.transform = 'scale(1)';
            card.style.boxShadow = '';
        }, 300);
    });
}

// Function to update charts with time range data
function updateChartsWithTimeRange(data) {
    // Update enrollment chart if trends data is available
    if (data.trends && window.enrollmentChart) {
        const chart = window.enrollmentChart;

        // Update chart data
        chart.data.labels = data.trends.labels;
        chart.data.datasets[0].data = data.trends.enrollments;
        chart.data.datasets[1].data = data.trends.completions;

        // Update chart title based on time range
        const timeRangeTitle = getChartTitleForTimeRange(data.timeRange);
        chart.options.plugins.title.text = timeRangeTitle;

        // Update the chart
        chart.update('active');
    }
}

// Helper function to get chart title based on time range
function getChartTitleForTimeRange(timeRange) {
    switch (timeRange) {
        case 'week':
            return 'Weekly Activity Trends';
        case 'month':
            return 'Monthly Activity Trends';
        case 'quarter':
            return 'Quarterly Activity Trends';
        case 'year':
            return 'Yearly Activity Trends';
        default:
            return 'Activity Trends';
    }
}

// Function to show error messages
function showErrorMessage(message) {
    // Create or update error message element
    let errorElement = document.getElementById('dashboard-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.id = 'dashboard-error';
        errorElement.className = 'alert alert-danger alert-dismissible fade show';
        errorElement.style.position = 'fixed';
        errorElement.style.top = '20px';
        errorElement.style.right = '20px';
        errorElement.style.zIndex = '9999';
        errorElement.style.maxWidth = '400px';
        document.body.appendChild(errorElement);
    }

    errorElement.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (errorElement && errorElement.parentNode) {
            errorElement.parentNode.removeChild(errorElement);
        }
    }, 5000);
}

// Function to update dashboard data based on selected semester
function updateDashboardBySemester(semester) {
    console.log('Updating dashboard for semester:', semester);

    // Show loading state with enhanced visual effects
    const dashboardContent = document.getElementById('dashboardContent');
    if (dashboardContent) {
        dashboardContent.classList.add('updating');
        dashboardContent.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        dashboardContent.style.opacity = '0.7';
        dashboardContent.style.transform = 'translateY(10px)';
    }

    // Make AJAX request to get filtered data
    fetch(`ajax/dashboard_filter.php?semester=${semester}`, {
        method: 'GET',
        credentials: 'same-origin', // Include cookies for session
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data); // Debug log

            if (data.success) {
                // Update dashboard stats
                updateDashboardStatsFromServer(data.data.counts);

                // Update charts if needed
                if (data.data.trends) {
                    updateChartDataFromServer(data.data.trends);
                }

                // Update user distribution chart
                if (data.data.distribution) {
                    updateUserDistributionChart(data.data.distribution);
                }

                console.log('Dashboard updated successfully for semester:', semester);

                // Show success feedback
                showNotification('Dashboard updated for ' + (semester === 'all' ? 'all semesters' : semester + ' semester'), 'success');
            } else {
                console.error('Error updating dashboard:', data.error);
                showNotification('Error updating dashboard: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error fetching dashboard data:', error);
            showNotification('Failed to update dashboard. Please try again.', 'error');
        })
        .finally(() => {
            // Remove loading state with smooth animation
            if (dashboardContent) {
                setTimeout(() => {
                    dashboardContent.classList.remove('updating');
                    dashboardContent.style.opacity = '1';
                    dashboardContent.style.transform = 'translateY(0)';
                }, 300);
            }
        });
}

// Function to update chart data
function updateChartData(data) {
    const chart = window.enrollmentChart;
    if (!chart) return;

    // Update chart data
    chart.data.datasets[0].data = data.enrollments;
    chart.data.datasets[1].data = data.completions;

    // Update the chart
    chart.update();
}

// Function to update dashboard stats
function updateDashboardStats(data) {
    // Update active users
    const activeUsersElement = document.querySelector('.card-body h3');
    if (activeUsersElement) {
        activeUsersElement.textContent = data.activeUsers;
    }

    // Update growth percentages
    const growthElements = document.querySelectorAll('.card-body .text-success');
    if (growthElements.length >= 4) {
        growthElements[0].innerHTML = `<i class="fas fa-arrow-up"></i> ${data.growth.users}`;
        growthElements[1].innerHTML = `<i class="fas fa-arrow-up"></i> ${data.growth.students}`;
        growthElements[2].innerHTML = `<i class="fas fa-arrow-up"></i> ${data.growth.teachers}`;
        growthElements[3].innerHTML = `<i class="fas fa-arrow-up"></i> ${data.growth.courses}`;
    }
}

// Function to update dashboard stats from server data
function updateDashboardStatsFromServer(counts) {
    // Update the dashboard cards with real data from server
    const cards = document.querySelectorAll('.dashboard-card .card-body h3');
    if (cards.length >= 4) {
        // Active Users (students + teachers)
        cards[0].textContent = counts.students + counts.teachers;

        // Total Students
        cards[1].textContent = counts.students;

        // Total Teachers
        cards[2].textContent = counts.teachers;

        // Total Courses
        cards[3].textContent = counts.courses;
    }

    // Also update the user distribution footer
    const footerCounts = document.querySelectorAll('.card-footer .font-weight-bold');
    if (footerCounts.length >= 3) {
        footerCounts[0].textContent = counts.students;
        footerCounts[1].textContent = counts.teachers;
        // Admin count stays at 1
    }
}

// Function to update chart data from server
function updateChartDataFromServer(trends) {
    const chart = window.enrollmentChart;
    if (!chart || !trends) return;

    // Update chart labels and data
    if (trends.labels) {
        chart.data.labels = trends.labels;
    }

    if (trends.enrollments) {
        chart.data.datasets[0].data = trends.enrollments;
    }

    if (trends.completions) {
        chart.data.datasets[1].data = trends.completions;
    }

    // Update the chart
    chart.update();
}

// Function to update user distribution chart
function updateUserDistributionChart(distribution) {
    const chart = window.userDistributionChart;
    if (!chart || !distribution) return;

    // Update chart data
    if (distribution.labels) {
        chart.data.labels = distribution.labels;
    }

    if (distribution.data) {
        chart.data.datasets[0].data = distribution.data;
    }

    // Update the chart
    chart.update();
}

// Function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}