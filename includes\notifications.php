<?php
/**
 * Notifications Functions
 *
 * This file contains functions for managing notifications.
 */

/**
 * Create a new notification
 *
 * @param int $userId User ID to receive the notification
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type (event_creation, event_update, event_reminder, status_change, system)
 * @param int $relatedId Related ID (e.g., event_id)
 * @return bool True if successful, false otherwise
 */
function createNotification($userId, $title, $message, $type, $relatedId = null) {
    global $pdo;

    try {
        // Check if the notifications table exists
        $tableExists = false;
        $stmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
        if ($stmt && $stmt->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, create it
        if (!$tableExists) {
            $createTable = "CREATE TABLE notifications (
                notification_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) NOT NULL,
                related_id INT,
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )";

            if (!$pdo->query($createTable)) {
                error_log("Error creating notifications table");
                return false;
            }
        }

        // Check if the user has enabled this type of notification
        if (!shouldSendNotification($userId, $type)) {
            return true; // Skip notification but return success
        }

        // Insert the notification
        $sql = "INSERT INTO notifications (user_id, title, message, type, related_id)
                VALUES (:user_id, :title, :message, :type, :related_id)";
        $stmt = $pdo->prepare($sql);

        return $stmt->execute([
            ':user_id' => $userId,
            ':title' => $title,
            ':message' => $message,
            ':type' => $type,
            ':related_id' => $relatedId
        ]);
    } catch (PDOException $e) {
        error_log("Error creating notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if a user should receive a specific type of notification
 *
 * @param int $userId User ID
 * @param string $type Notification type
 * @return bool True if the user should receive the notification, false otherwise
 */
function shouldSendNotification($userId, $type) {
    global $pdo;

    try {
        // Check if the user_notification_settings table exists
        $tableExists = false;
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
        if ($stmt && $stmt->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, create it and add default settings
        if (!$tableExists) {
            // Run the setup script to create the table
            include_once 'setup_notification_settings.php';
        }

        // Get user's notification settings
        $sql = "SELECT * FROM user_notification_settings WHERE user_id = :user_id";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':user_id' => $userId]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        // If no settings found, use default (all enabled)
        if (!$settings) {
            // Insert default settings for this user
            $insertSql = "INSERT INTO user_notification_settings
                    (user_id, email_notifications, theme,
                     assignment_reminders, course_announcements, due_date_reminders, grade_updates,
                     student_submissions, enrollment_requests, course_activity,
                     user_registrations, course_creation, account_changes, error_alerts, weekly_reports,
                     comments_on_posts, comments_mentions, private_comments,
                     event_creation, event_updates, event_reminders, status_changes, system_announcements)
                    VALUES (:user_id, 1, 'light', 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)";
            $insertStmt = $pdo->prepare($insertSql);
            $insertStmt->execute([':user_id' => $userId]);

            return true; // Default to enabled for new users
        }

        // First check if email notifications are enabled at all
        if (!$settings['email_notifications']) {
            return false;
        }

        // Check if the specific notification type is enabled
        switch ($type) {
            // Calendar notifications
            case 'event_creation':
                return (bool)$settings['event_creation'];
            case 'event_update':
                return (bool)$settings['event_updates'];
            case 'event_reminder':
                return (bool)$settings['event_reminders'];
            case 'status_change':
                return (bool)$settings['status_changes'];
            case 'system':
                return (bool)$settings['system_announcements'];

            // Student notifications
            case 'assignment_reminder':
                return (bool)$settings['assignment_reminders'];
            case 'course_announcement':
                return (bool)$settings['course_announcements'];
            case 'due_date_reminder':
                return (bool)$settings['due_date_reminders'];
            case 'grade_update':
                return (bool)$settings['grade_updates'];

            // Activity notifications
            case 'activity_created':
                return (bool)$settings['course_activity'];
            case 'activity_updated':
                return (bool)$settings['course_activity'];
            case 'assignment_created':
                return (bool)$settings['course_activity'];
            case 'assignment_updated':
                return (bool)$settings['course_activity'];
            case 'quiz_created':
                return (bool)$settings['course_activity'];
            case 'quiz_updated':
                return (bool)$settings['course_activity'];

            // Enrollment notifications
            case 'enrollment_approved':
                return (bool)$settings['enrollment_requests'];
            case 'enrollment_rejected':
                return (bool)$settings['enrollment_requests'];

            // Instructor notifications
            case 'student_submission':
                return (bool)$settings['student_submissions'];
            case 'enrollment_request':
                return (bool)$settings['enrollment_requests'];
            case 'course_activity':
                return (bool)$settings['course_activity'];

            // Admin notifications
            case 'user_registration':
                return (bool)$settings['user_registrations'];
            case 'course_creation':
                return (bool)$settings['course_creation'];
            case 'account_change':
                return (bool)$settings['account_changes'];
            case 'error_alert':
                return (bool)$settings['error_alerts'];
            case 'weekly_report':
                return (bool)$settings['weekly_reports'];

            // Comment notifications
            case 'comment_on_post':
                return (bool)$settings['comments_on_posts'];
            case 'comment_mention':
                return (bool)$settings['comments_mentions'];
            case 'private_comment':
                return (bool)$settings['private_comments'];

            default:
                return true; // Default to enabled for unknown types
        }
    } catch (PDOException $e) {
        error_log("Error checking notification settings: " . $e->getMessage());
        return true; // Default to sending notifications on error
    }
}

/**
 * Get unread notifications for a user
 *
 * @param int $userId User ID
 * @param int $limit Maximum number of notifications to return
 * @return array Array of notifications
 */
function getUnreadNotifications($userId, $limit = 10) {
    global $pdo;

    try {
        $sql = "SELECT * FROM notifications
                WHERE user_id = :user_id AND is_read = 0
                ORDER BY created_at DESC
                LIMIT :limit";
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting unread notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all notifications for a user
 *
 * @param int $userId User ID
 * @param int $limit Maximum number of notifications to return
 * @param int $offset Offset for pagination
 * @return array Array of notifications
 */
function getAllNotifications($userId, $limit = 20, $offset = 0) {
    global $pdo;

    try {
        $sql = "SELECT * FROM notifications
                WHERE user_id = :user_id
                ORDER BY created_at DESC
                LIMIT :limit OFFSET :offset";
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting all notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark a notification as read
 *
 * @param int $notificationId Notification ID
 * @param int $userId User ID (for security)
 * @return bool True if successful, false otherwise
 */
function markNotificationAsRead($notificationId, $userId) {
    global $pdo;

    try {
        $sql = "UPDATE notifications
                SET is_read = 1
                WHERE notification_id = :notification_id AND user_id = :user_id";
        $stmt = $pdo->prepare($sql);

        return $stmt->execute([
            ':notification_id' => $notificationId,
            ':user_id' => $userId
        ]);
    } catch (PDOException $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for a user
 *
 * @param int $userId User ID
 * @return bool True if successful, false otherwise
 */
function markAllNotificationsAsRead($userId) {
    global $pdo;

    try {
        $sql = "UPDATE notifications
                SET is_read = 1
                WHERE user_id = :user_id";
        $stmt = $pdo->prepare($sql);

        return $stmt->execute([':user_id' => $userId]);
    } catch (PDOException $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Count unread notifications for a user
 *
 * @param int $userId User ID
 * @return int Number of unread notifications
 */
function countUnreadNotifications($userId) {
    global $pdo;

    try {
        $sql = "SELECT COUNT(*) FROM notifications
                WHERE user_id = :user_id AND is_read = 0";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':user_id' => $userId]);

        return (int)$stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log("Error counting unread notifications: " . $e->getMessage());
        return 0;
    }
}
?>
