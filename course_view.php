<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists and user has access
if (is_string($course)) {
    // Course not found or error occurred
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Get course instructors
$instructors = getCourseInstructors($courseId);
if (is_string($instructors)) {
    $instructorError = $instructors;
    $instructors = [];
}

// Check if user has access to this course
if (!isAdmin() &&
    !isInstructor($_SESSION['user_id'], $courseId) &&
    !(isTeacher() && $course['created_by'] == $_SESSION['user_id']) &&
    !isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You do not have access to this course.";
    header("location: index.php");
    exit;
}

// Process instructor assignment if admin or course creator
if ((isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])) && $_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['assign_instructor'])) {
        $instructorId = $_POST['instructor_id'];
        $result = assignInstructor($courseId, $instructorId);

        if ($result === true) {
            $_SESSION['success'] = "Instructor assigned successfully.";
            // Refresh the instructors list
            $instructors = getCourseInstructors($courseId);
            if (is_string($instructors)) {
                $instructorError = $instructors;
                $instructors = [];
            }
        } else {
            $_SESSION['error'] = $result;
        }
    } elseif (isset($_POST['remove_instructor'])) {
        $instructorId = $_POST['instructor_id'];
        $result = removeInstructor($courseId, $instructorId);

        if ($result === true) {
            $_SESSION['success'] = "Instructor removed successfully.";
            // Refresh the instructors list
            $instructors = getCourseInstructors($courseId);
            if (is_string($instructors)) {
                $instructorError = $instructors;
                $instructors = [];
            }
        } else {
            $_SESSION['error'] = $result;
        }
    }
}

// We don't need modules, tabs, or stream items for the instructor management page
?>

<?php
// Set page title
$page_title = htmlspecialchars($course['title']);

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="index.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Courses
    </a>
</div>

<!-- Course header -->
<div class="course-header">
    <h1 class="course-title"><?php echo htmlspecialchars($course['title']); ?></h1>
    <p class="course-description"><?php echo htmlspecialchars($course['description']); ?></p>
</div>

<!-- Instructor Management Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users mr-2"></i> Instructor Management</h2>
    <?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#assignInstructorModal">
        <i class="fas fa-user-plus"></i> Assign New Instructor
    </button>
    <?php endif; ?>
</div>

<!-- Course Creator -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="mb-0">Course Creator</h3>
    </div>
    <div class="list-group list-group-flush">
        <div class="list-group-item d-flex align-items-center">
            <div class="user-avatar mr-3">
                <?php echo strtoupper(substr($course['creator_name'], 0, 1)); ?>
            </div>
            <div class="flex-grow-1">
                <h5 class="mb-0"><?php echo htmlspecialchars($course['creator_name']); ?></h5>
                <small class="text-muted">Course Creator</small>
            </div>
            <span class="badge badge-info">Creator</span>
        </div>
    </div>
</div>

<!-- Course Instructors -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="mb-0">Course Instructors</h3>
    </div>
    <div class="list-group list-group-flush">
        <?php if (count($instructors) > 0): ?>
            <?php foreach ($instructors as $instructor): ?>
            <div class="list-group-item d-flex align-items-center">
                <div class="user-avatar mr-3">
                    <?php echo strtoupper(substr($instructor['first_name'], 0, 1)); ?>
                </div>
                <div class="flex-grow-1">
                    <h5 class="mb-0"><?php echo htmlspecialchars($instructor['first_name'] . ' ' . $instructor['last_name']); ?></h5>
                    <small class="text-muted"><?php echo htmlspecialchars($instructor['email']); ?></small>
                </div>
                <span class="badge badge-success mr-2">Instructor</span>
                <?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $courseId); ?>" method="post">
                    <input type="hidden" name="instructor_id" value="<?php echo $instructor['user_id']; ?>">
                    <button type="submit" name="remove_instructor" class="btn btn-sm btn-danger"
                            onclick="return confirm('Are you sure you want to remove <?php echo htmlspecialchars($instructor['first_name'] . ' ' . $instructor['last_name']); ?> as an instructor from this course?');">
                        <i class="fas fa-user-minus"></i>
                    </button>
                </form>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="list-group-item text-center text-muted py-4">
                <p>No instructors assigned yet.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
<!-- Assign Instructor Modal -->
<div class="modal fade" id="assignInstructorModal" tabindex="-1" role="dialog" aria-labelledby="assignInstructorModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignInstructorModalLabel">Assign Instructor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $courseId); ?>" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="instructor_id">Select Instructor</label>
                        <select name="instructor_id" id="instructor_id" class="form-control" required>
                            <option value="">Select an instructor...</option>
                            <?php
                            // Get all teachers
                            $teachers = getUsersByRole('teacher');
                            if (!is_string($teachers)) {
                                foreach ($teachers as $teacher) {
                                    // Skip teachers who are already assigned
                                    $alreadyAssigned = false;
                                    foreach ($instructors as $instructor) {
                                        if ($instructor['user_id'] == $teacher['user_id']) {
                                            $alreadyAssigned = true;
                                            break;
                                        }
                                    }

                                    if (!$alreadyAssigned) {
                                        echo '<option value="' . $teacher['user_id'] . '">' .
                                            htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']) .
                                            ' (' . htmlspecialchars($teacher['email']) . ')' .
                                            '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="assign_instructor" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
