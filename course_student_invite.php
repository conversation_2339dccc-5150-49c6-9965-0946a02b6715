<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is authorized (admin, course creator, or instructor)
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You are not authorized to invite students.";
    header("location: index.php");
    exit;
}

// Check if form was submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate input
    if (!isset($_POST['course_id']) || empty($_POST['course_id']) || 
        !isset($_POST['student_ids']) || !is_array($_POST['student_ids']) || empty($_POST['student_ids'])) {
        $_SESSION['error'] = "Missing required fields.";
        header("location: index.php");
        exit;
    }
    
    $courseId = intval($_POST['course_id']);
    $studentIds = $_POST['student_ids'];
    
    // Check if user is authorized for this course
    $course = getCourseById($courseId);
    if (is_string($course)) {
        $_SESSION['error'] = $course;
        header("location: index.php");
        exit;
    }
    
    if (!isAdmin() && $course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId)) {
        $_SESSION['error'] = "You are not authorized to invite students to this course.";
        header("location: course_view_full.php?id=$courseId&tab=people");
        exit;
    }
    
    // Enroll each student
    $successCount = 0;
    $errorMessages = [];
    
    foreach ($studentIds as $studentId) {
        $result = enrollStudent(intval($studentId), $courseId);
        
        if ($result === true) {
            $successCount++;
        } else {
            $errorMessages[] = $result;
        }
    }
    
    // Set session messages
    if ($successCount > 0) {
        $_SESSION['success'] = "$successCount student(s) enrolled successfully.";
    }
    
    if (!empty($errorMessages)) {
        $_SESSION['error'] = "Some students could not be enrolled: " . implode("; ", $errorMessages);
    }
    
    // Redirect back to the course people tab
    header("location: course_view_full.php?id=$courseId&tab=people");
    exit;
} else {
    // Not a POST request, redirect to home
    header("location: index.php");
    exit;
}
?>
