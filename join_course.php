<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can join courses.";
    header("location: index.php");
    exit;
}

// Initialize variables
$classCode = "";
$error = "";
$success = "";

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate and sanitize input
    $classCode = sanitize($_POST["class_code"]);
    
    // Check if class code is provided
    if (empty($classCode)) {
        $error = "Please enter a class code.";
    } else {
        // Try to enroll the student
        $result = enrollStudentByClassCode($_SESSION['user_id'], $classCode);
        
        if ($result === true) {
            // Get course details for success message
            $course = getCourseByClassCode($classCode);
            if (!is_string($course)) {
                $success = "You have successfully enrolled in " . htmlspecialchars($course['title']) . ".";
                $classCode = ""; // Clear the form
            } else {
                $success = "You have successfully enrolled in the course.";
            }
        } else {
            $error = $result;
        }
    }
}

// Set page title
$page_title = "Join a Course";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Join a Course</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <?php echo $success; ?>
                            <div class="mt-2">
                                <a href="index.php" class="btn btn-sm btn-primary">Go to Dashboard</a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <div class="form-group">
                            <label for="class_code">Enter Class Code</label>
                            <input type="text" class="form-control form-control-lg" id="class_code" name="class_code" 
                                   placeholder="Enter the 6-digit class code" value="<?php echo htmlspecialchars($classCode); ?>" 
                                   maxlength="10" autocomplete="off" required>
                            <small class="form-text text-muted">
                                The class code is provided by your instructor. It's usually a 6-character alphanumeric code.
                            </small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg btn-block">Join Course</button>
                    </form>
                </div>
                <div class="card-footer">
                    <div class="text-center">
                        <a href="index.php" class="btn btn-link">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
