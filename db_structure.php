<?php
// Include configuration file
require_once 'includes/config.php';

// Function to get table structure
function getTableStructure($tableName) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("DESCRIBE " . $tableName);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return "Error: " . $e->getMessage();
    }
}

// Get structure for courses table
echo "<h2>Courses Table Structure</h2>";
$coursesStructure = getTableStructure("courses");
echo "<pre>";
print_r($coursesStructure);
echo "</pre>";

// Get structure for users table
echo "<h2>Users Table Structure</h2>";
$usersStructure = getTableStructure("users");
echo "<pre>";
print_r($usersStructure);
echo "</pre>";

// Get structure for enrollments table
echo "<h2>Enrollments Table Structure</h2>";
$enrollmentsStructure = getTableStructure("enrollments");
echo "<pre>";
print_r($enrollmentsStructure);
echo "</pre>";

// Get structure for submissions table
echo "<h2>Submissions Table Structure</h2>";
$submissionsStructure = getTableStructure("submissions");
echo "<pre>";
print_r($submissionsStructure);
echo "</pre>";

// Get structure for activities table
echo "<h2>Activities Table Structure</h2>";
$activitiesStructure = getTableStructure("activities");
echo "<pre>";
print_r($activitiesStructure);
echo "</pre>";
?>
