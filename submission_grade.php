<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/assessment_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "Only teachers and administrators can grade submissions.";
    header("location: index.php");
    exit;
}

// Check if submission ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Submission ID is required.";
    header("location: index.php");
    exit;
}

$submissionId = intval($_GET['id']);

// Get submission details
$submission = getSubmissionWithDetails($submissionId);

// Check if submission exists
if (is_string($submission)) {
    $_SESSION['error'] = $submission;
    header("location: index.php");
    exit;
}

// Get assignment details
$assignment = getAssignmentById($submission['assignment_id']);
if (is_string($assignment)) {
    $_SESSION['error'] = $assignment;
    header("location: index.php");
    exit;
}

// Get course details
$course = getCourseById($assignment['course_id']);
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Get student details
$student = getUserById($submission['user_id']);
if (is_string($student)) {
    $_SESSION['error'] = $student;
    header("location: index.php");
    exit;
}

// Check if user is authorized to grade this submission
$isAuthorized = false;

// Teacher can grade if they are the course creator or an instructor
if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $course['course_id']))) {
    $isAuthorized = true;
}

// Admin can grade any submission
if (isAdmin()) {
    $isAuthorized = true;
}

if (!$isAuthorized) {
    $_SESSION['error'] = "You are not authorized to grade this submission.";
    header("location: index.php");
    exit;
}

// Check if submission is already graded
if ($submission['is_graded']) {
    // Redirect to view page
    header("location: submission_view.php?id=$submissionId");
    exit;
}

// Initialize variables
$score = $feedback = "";
$score_err = $feedback_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate score
    if (empty(trim($_POST["score"]))) {
        $score_err = "Please enter a score.";
    } elseif (!is_numeric($_POST["score"]) || intval($_POST["score"]) < 0 || intval($_POST["score"]) > $assignment['points']) {
        $score_err = "Score must be between 0 and " . $assignment['points'] . ".";
    } else {
        $score = intval($_POST["score"]); // Use integer value for points
    }

    // Validate feedback (optional)
    $feedback = trim($_POST["feedback"]);

    // Check input errors before grading the submission
    if (empty($score_err)) {
        // Grade the submission
        $result = gradeSubmission($submissionId, $score, $feedback);

        if ($result === true) {
            // Submission graded successfully
            $_SESSION['success'] = "Submission graded successfully.";
            header("location: submission_view.php?id=$submissionId");
            exit;
        } else {
            // Error grading submission
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Assign Points";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $course['course_id']; ?>&tab=grades" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Assign Points</h1>
</div>

<!-- Submission info -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Assignment Information</h5>
            </div>
            <div class="card-body">
                <h6 class="card-title"><?php echo htmlspecialchars($assignment['title']); ?></h6>
                <p class="card-text"><?php echo nl2br(htmlspecialchars($assignment['description'])); ?></p>
                <div class="d-flex justify-content-between">
                    <span><strong>Points:</strong> <?php echo $assignment['points']; ?></span>
                    <?php if (!empty($assignment['due_date'])): ?>
                    <span><strong>Due:</strong> <?php echo date('M j, Y g:i A', strtotime($assignment['due_date'])); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Student Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="user-avatar mr-3">
                        <?php echo strtoupper(substr($student['first_name'], 0, 1)); ?>
                    </div>
                    <div>
                        <h6 class="mb-0"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                    </div>
                </div>
                <div class="d-flex justify-content-between">
                    <span><strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($submission['submission_date'])); ?></span>
                    <?php if (!empty($assignment['due_date'])): ?>
                        <?php if (strtotime($submission['submission_date']) <= strtotime($assignment['due_date'])): ?>
                        <span class="text-success"><i class="fas fa-check-circle"></i> On time</span>
                        <?php else: ?>
                        <span class="text-danger"><i class="fas fa-exclamation-circle"></i> Late</span>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Submission content -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Submission</h5>
    </div>
    <div class="card-body">
        <?php echo nl2br(htmlspecialchars($submission['content'])); ?>

        <?php if (!empty($submission['file_path'])): ?>
        <div class="mt-3">
            <h6>Attached File:</h6>
            <a href="<?php echo $submission['file_path']; ?>" class="btn btn-outline-primary" target="_blank">
                <i class="fas fa-file-download mr-1"></i> Download Attachment
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Grading form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Assign Points</h5>
    </div>
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $submissionId); ?>" method="post">
            <div class="form-group">
                <label for="score">Score (out of <?php echo $assignment['points']; ?>)</label>
                <input type="number" name="score" id="score" class="form-control <?php echo (!empty($score_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $score; ?>" min="0" max="<?php echo $assignment['points']; ?>" step="1">
                <span class="invalid-feedback"><?php echo $score_err; ?></span>
            </div>
            <div class="form-group">
                <label for="feedback">Feedback (optional)</label>
                <textarea name="feedback" id="feedback" rows="4" class="form-control <?php echo (!empty($feedback_err)) ? 'is-invalid' : ''; ?>"><?php echo $feedback; ?></textarea>
                <span class="invalid-feedback"><?php echo $feedback_err; ?></span>
            </div>
            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Submit Points</button>
                <a href="course_view_full.php?id=<?php echo $course['course_id']; ?>&tab=grades" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
