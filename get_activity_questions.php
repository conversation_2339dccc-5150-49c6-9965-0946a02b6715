<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not return error
if (!isLoggedIn()) {
    echo '<div class="list-group-item text-center py-4"><p class="text-danger">You must be logged in to view questions.</p></div>';
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    echo '<div class="list-group-item text-center py-4"><p class="text-danger">You do not have permission to view questions.</p></div>';
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo '<div class="list-group-item text-center py-4"><p class="text-danger">Activity ID is required.</p></div>';
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    echo '<div class="list-group-item text-center py-4"><p class="text-danger">Activity not found: ' . htmlspecialchars($activity) . '</p></div>';
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    echo '<div class="list-group-item text-center py-4"><p class="text-danger">Course not found: ' . htmlspecialchars($course) . '</p></div>';
    exit;
}

// Check if user is authorized to view this activity
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        echo '<div class="list-group-item text-center py-4"><p class="text-danger">You are not authorized to view questions for this activity.</p></div>';
        exit;
    }
}

// Get activity questions based on activity type
if ($activity['activity_type'] == 'quiz') {
    // For quiz activities, use getQuizQuestions
    $questions = getQuizQuestions($activityId);
} else {
    // For regular activities, use getActivityQuestions
    $questions = getActivityQuestions($activityId);
}

// Handle error or empty results
if (is_string($questions)) {
    error_log("Error retrieving questions in get_activity_questions.php: " . $questions);
    echo '<div class="list-group-item text-center py-4">';
    echo '<p class="text-danger">Error retrieving questions: ' . htmlspecialchars($questions) . '</p>';
    echo '<p class="text-muted">Activity ID: ' . $activityId . ', Activity Type: ' . $activity['activity_type'] . '</p>';
    echo '</div>';
    exit;
}

// If no questions found
if (empty($questions) || count($questions) == 0) {
    echo '<div class="list-group-item text-center py-4"><p class="text-muted mb-0">No questions added yet. Click "Add New Question" to get started.</p></div>';
    exit;
}

// Display questions
foreach ($questions as $index => $question):
?>
<div class="list-group-item">
    <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
        <span class="badge badge-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'warning'); ?> p-2"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
    </div>
    <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

    <?php
    // Find the correct answer for display in the main view
    if (isset($question['options']) && count($question['options']) > 0):
        $correctAnswer = null;
        $correctIndex = null;
        foreach ($question['options'] as $optIndex => $opt) {
            if ($opt['is_correct']) {
                $correctAnswer = $opt['option_text'];
                $correctIndex = $optIndex;
                break;
            }
        }

        if ($correctAnswer):
            if ($question['question_type'] == 'true_false'):
    ?>
    <div class="d-flex align-items-center mt-2 mb-2">
        <div class="mr-2">
            <span class="badge badge-info p-2">
                <i class="fas fa-<?php echo $correctAnswer == 'True' ? 'check' : 'times'; ?> mr-1"></i>
                <?php echo $correctAnswer; ?>
            </span>
        </div>
        <div>
            <span class="badge badge-success">Correct Answer</span>
        </div>
    </div>
    <?php elseif ($question['question_type'] == 'short_answer'): ?>
    <div class="alert alert-success mt-2 mb-2">
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle text-success mr-2"></i>
            <strong>Correct Answer:</strong>
            <span class="ml-2 p-1 bg-light rounded"><?php echo htmlspecialchars($correctAnswer); ?></span>
        </div>
    </div>
    <?php elseif ($question['question_type'] == 'multiple_choice'): ?>
    <div class="alert alert-success mt-2 mb-2">
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle text-success mr-2"></i>
            <strong>Correct Answer:</strong>
            <span class="ml-2 p-1 bg-light rounded"><?php echo htmlspecialchars($correctAnswer); ?></span>
        </div>
    </div>
    <?php
            endif;
        endif;
    endif;
    ?>

    <?php if (isset($question['options']) && count($question['options']) > 0): ?>
    <div class="card mb-3 ml-4 border-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'primary'); ?>">
        <div class="card-header bg-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'primary'); ?> text-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <?php if ($question['question_type'] == 'true_false'): ?>
                    True/False Options
                <?php else: ?>
                    Answer Options
                <?php endif; ?>
            </h6>
            <?php
            // Find the correct option
            $correctOption = null;
            foreach ($question['options'] as $optIndex => $opt) {
                if ($opt['is_correct']) {
                    $correctOption = $opt;
                    $correctIndex = $optIndex;
                    break;
                }
            }

            if ($correctOption && $question['question_type'] == 'multiple_choice'):
            ?>
            <div class="correct-answer-summary">
                <span class="badge badge-light">Correct: Option <?php echo chr(65 + $correctIndex); ?></span>
            </div>
            <?php elseif ($correctOption && $question['question_type'] == 'true_false'): ?>
            <div class="correct-answer-summary">
                <span class="badge badge-light">Correct Answer: <?php echo $correctOption['option_text']; ?></span>
            </div>
            <?php endif; ?>
        </div>
        <div class="list-group list-group-flush">
            <?php foreach ($question['options'] as $optionIndex => $option): ?>
            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-success-light' : ''; ?>">
                <div class="d-flex align-items-center">
                    <?php if ($question['question_type'] == 'multiple_choice'): ?>
                        <div class="option-indicator mr-3">
                            <span class="badge badge-pill badge-<?php echo $option['is_correct'] ? 'success' : 'secondary'; ?> option-badge"><?php echo chr(65 + $optionIndex); ?></span>
                        </div>
                    <?php elseif ($question['question_type'] == 'true_false'): ?>
                        <div class="option-indicator mr-3">
                            <span class="badge badge-pill badge-<?php echo $option['is_correct'] ? 'success' : 'secondary'; ?> option-badge"><?php echo $option['option_text'] == 'True' ? 'T' : 'F'; ?></span>
                        </div>
                        <div class="tf-icon mr-2">
                            <i class="fas fa-<?php echo $option['option_text'] == 'True' ? 'check text-success' : 'times text-danger'; ?>"></i>
                        </div>
                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                        <i class="far fa-edit mr-3"></i>
                    <?php endif; ?>

                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold text-success' : ''; ?>">
                        <?php echo htmlspecialchars($option['option_text']); ?>
                    </div>

                    <?php if ($option['is_correct']): ?>
                    <div class="ml-auto">
                        <span class="badge badge-success"><i class="fas fa-check mr-1"></i> Correct Answer</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="btn-group">
        <?php if ($activity['activity_type'] == 'quiz'): ?>
        <a href="quiz_question_edit.php?id=<?php echo $question['question_id']; ?>" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-edit"></i> Edit
        </a>
        <?php else: ?>
        <a href="activity_question_edit.php?id=<?php echo $question['question_id']; ?>" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-edit"></i> Edit
        </a>
        <?php endif; ?>
        <a href="#" class="btn btn-sm btn-outline-danger delete-question" data-question-id="<?php echo $question['question_id']; ?>">
            <i class="fas fa-trash"></i> Delete
        </a>
    </div>
</div>
<?php endforeach; ?>

<script>
// Add confirmation and functionality for delete buttons
document.querySelectorAll('.delete-question').forEach(function(button) {
    button.addEventListener('click', function(e) {
        e.preventDefault(); // Prevent the default link behavior

        if (confirm('Are you sure you want to delete this question?')) {
            // Get the question ID from the data attribute
            var questionId = this.getAttribute('data-question-id');
            var activityId = <?php echo $activityId; ?>;

            // Create a form and submit it programmatically
            var form = document.createElement('form');
            form.method = 'POST';

            // Get the current URL to redirect back to the same page
            var currentUrl = window.location.href;
            var baseUrl = currentUrl.split('?')[0]; // Get the base URL without parameters

            // Set the form action based on the activity type
            var activityType = '<?php echo $activity['activity_type']; ?>';
            var formAction = activityType === 'quiz' ? 'quiz_edit.php?id=' + activityId : 'activity_edit.php?id=' + activityId;
            form.action = formAction;

            // Add question ID as a hidden field
            var questionIdInput = document.createElement('input');
            questionIdInput.type = 'hidden';
            questionIdInput.name = 'delete_question';
            questionIdInput.value = questionId;
            form.appendChild(questionIdInput);

            // Add activity ID as a hidden field
            var activityIdInput = document.createElement('input');
            activityIdInput.type = 'hidden';
            activityIdInput.name = 'activity_id';
            activityIdInput.value = activityId;
            form.appendChild(activityIdInput);

            // Append the form to the body and submit it
            document.body.appendChild(form);
            form.submit();
        }
    });
});
</script>
