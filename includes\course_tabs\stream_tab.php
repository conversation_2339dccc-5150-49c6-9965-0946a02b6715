<?php
// Stream tab content - shows announcements and recent activities
?>
<div id="stream-tab" class="course-tab-content">
    <!-- Teacher/Instructor actions -->
    <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex">
                <?php
                // Get the current user's profile picture
                $userId = $_SESSION['user_id'];
                $profilePic = '';
                try {
                    $profileStmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                    $profileStmt->bindParam(':userId', $userId);
                    $profileStmt->execute();
                    if ($profileStmt->rowCount() > 0) {
                        $userData = $profileStmt->fetch();
                        $profilePic = $userData['profile_picture'];
                    }
                } catch (PDOException $e) {
                    // Silently fail and use default avatar
                }

                if (!empty($profilePic)): ?>
                    <img src="<?php echo htmlspecialchars($profilePic); ?>" class="rounded-circle mr-3" width="40" height="40" alt="User avatar" style="object-fit: cover;">
                <?php else: ?>
                    <div class="user-avatar mr-3">
                        <?php echo strtoupper(substr($_SESSION['first_name'], 0, 1)); ?>
                    </div>
                <?php endif; ?>
                <div class="flex-grow-1">
                    <div class="form-group mb-0">
                        <a href="announcement_create.php?course_id=<?php echo $courseId; ?>" class="form-control text-left text-muted py-2">
                            Announce something to your class...
                        </a>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-end mt-3">
                <a href="announcement_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-secondary mr-2">
                    <i class="fas fa-bullhorn mr-1"></i> Announcement
                </a>
                <a href="assignment_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-primary mr-2">
                    <i class="fas fa-clipboard-list mr-1"></i> Assignment
                </a>
                <a href="activity_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-success mr-2">
                    <i class="fas fa-tasks mr-1"></i> Activity
                </a>
                <a href="quiz_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-danger mr-2">
                    <i class="fas fa-question-circle mr-1"></i> Quiz
                </a>
                <a href="material_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-info">
                    <i class="fas fa-file-alt mr-1"></i> Material
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Announcements and activities -->
    <div class="stream-items">
        <?php if (count($announcements) > 0): ?>
            <?php foreach ($announcements as $announcement): ?>
            <div class="card mb-3 stream-item">
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <?php
                        // Get the creator's profile picture
                        $creatorId = $announcement['user_id'];
                        $profilePic = '';
                        try {
                            $profileStmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                            $profileStmt->bindParam(':userId', $creatorId);
                            $profileStmt->execute();
                            if ($profileStmt->rowCount() > 0) {
                                $userData = $profileStmt->fetch();
                                $profilePic = $userData['profile_picture'];
                            }
                        } catch (PDOException $e) {
                            // Silently fail and use default avatar
                        }

                        if (!empty($profilePic)): ?>
                            <img src="<?php echo htmlspecialchars($profilePic); ?>" class="rounded-circle mr-3" width="40" height="40" alt="User avatar" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="user-avatar mr-3">
                                <?php echo strtoupper(substr($announcement['creator_name'], 0, 1)); ?>
                            </div>
                        <?php endif; ?>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="mb-0"><?php echo htmlspecialchars($announcement['creator_name']); ?></h5>
                                <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($announcement['created_at'])); ?></small>
                            </div>
                            <h6 class="card-title">
                                <a href="announcement_view.php?id=<?php echo $announcement['announcement_id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($announcement['title']); ?>
                                </a>
                            </h6>
                            <div class="card-text">
                                <?php
                                // Limit content to 200 characters with ellipsis if longer
                                $content = htmlspecialchars($announcement['content']);
                                if (strlen($content) > 200) {
                                    echo nl2br(substr($content, 0, 200) . '...');
                                    echo '<div class="mt-2"><a href="announcement_view.php?id=' . $announcement['announcement_id'] . '" class="btn btn-sm btn-outline-primary">Read more</a></div>';
                                } else {
                                    echo nl2br($content);
                                }

                                // Get and display announcement files
                                $files = getAnnouncementFiles($announcement['announcement_id']);
                                if (!is_string($files) && !empty($files)):
                                ?>
                                <div class="announcement-files mt-3">
                                    <h6 class="small"><i class="fas fa-paperclip mr-2"></i>Attachments</h6>
                                    <div class="list-group">
                                        <?php foreach ($files as $file): ?>
                                        <?php
                                        // Determine file icon based on file extension
                                        $fileExtension = strtolower(pathinfo($file['file_name'], PATHINFO_EXTENSION));
                                        $fileIcon = 'file';

                                        // Set icon based on file type
                                        switch ($fileExtension) {
                                            case 'pdf':
                                                $fileIcon = 'file-pdf';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $fileIcon = 'file-word';
                                                break;
                                            case 'xls':
                                            case 'xlsx':
                                                $fileIcon = 'file-excel';
                                                break;
                                            case 'ppt':
                                            case 'pptx':
                                                $fileIcon = 'file-powerpoint';
                                                break;
                                            case 'jpg':
                                            case 'jpeg':
                                            case 'png':
                                            case 'gif':
                                                $fileIcon = 'file-image';
                                                break;
                                            case 'zip':
                                            case 'rar':
                                                $fileIcon = 'file-archive';
                                                break;
                                            case 'txt':
                                                $fileIcon = 'file-alt';
                                                break;
                                        }
                                        ?>
                                        <a href="universal_file_viewer.php?file=<?php echo urlencode($file['file_path']); ?>" class="list-group-item list-group-item-action py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-<?php echo $fileIcon; ?> mr-2"></i>
                                                <div>
                                                    <div class="small"><?php echo htmlspecialchars($file['file_name']); ?></div>
                                                </div>
                                            </div>
                                        </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mt-3 d-flex justify-content-between align-items-center">
                                <a href="announcement_view.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-comments"></i> View & Comment
                                </a>

                                <?php if (isTeacher() && ($announcement['created_by'] == $_SESSION['user_id'] || $course['created_by'] == $_SESSION['user_id'])): ?>
                                <div>
                                    <a href="announcement_edit.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm" style="min-width: 80px; border: 2px solid white; color: #333; background-color: transparent;">
                                        <i class="fas fa-edit mr-1"></i> Edit
                                    </a>
                                    <a href="announcement_delete.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm" style="min-width: 80px; border: 2px solid #db4437; color: #333; background-color: transparent;"
                                       onclick="return confirm('Are you sure you want to delete this announcement?');">
                                        <i class="fas fa-trash-alt mr-1"></i> Delete
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-stream"></i>
                </div>
                <h3>No announcements yet</h3>
                <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
                <p class="empty-state-text">Create an announcement to get started</p>
                <a href="announcement_create.php?course_id=<?php echo $courseId; ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Announcement
                </a>
                <?php else: ?>
                <p class="empty-state-text">Your instructor hasn't posted any announcements yet</p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
