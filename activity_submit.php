<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/utility_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can submit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if student is enrolled in the course
if (!isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You are not enrolled in this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Check if the activity is published
if (!$activity['is_published']) {
    $_SESSION['error'] = "This activity is not available yet.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Check if the due date has passed and late submissions are not allowed
$dueDatePassed = false;
if (!empty($activity['due_date'])) {
    $dueDate = new DateTime($activity['due_date']);
    $now = new DateTime();
    $dueDatePassed = ($now > $dueDate);

    if ($dueDatePassed && !$activity['allow_late_submissions']) {
        $_SESSION['error'] = "The due date for this activity has passed and late submissions are not allowed.";
        header("location: activity_view.php?id=$activityId");
        exit;
    }
}

// Check if the student has already submitted this activity
$studentSubmission = null;
// Try to get submission from activity_submissions table first
$result = getStudentActivitySubmission($activityId, $_SESSION['user_id']);
if (!is_string($result)) {
    $studentSubmission = $result;
} else {
    // If not found, try the submissions table
    $result = getStudentSubmission($activityId, $_SESSION['user_id']);
    if (!is_string($result)) {
        $studentSubmission = $result;
    }
}

// Get activity questions
$questions = [];
error_log("Activity type: " . $activity['activity_type'] . " for ID: " . $activityId);

if ($activity['activity_type'] == 'quiz') {
    $questions = getQuizQuestions($activityId);
    error_log("Quiz questions: " . (is_string($questions) ? $questions : json_encode($questions)));
    if (is_string($questions)) {
        $questions = [];
    }
} else if ($activity['activity_type'] == 'activity') {
    // Try to get questions from activity_questions table first
    $questions = getActivityQuestions($activityId);
    error_log("Activity questions from activity_questions: " . (is_string($questions) ? $questions : json_encode($questions)));
    if (is_string($questions) || empty($questions)) {
        // If no questions found or error, try the quiz_questions table as a fallback
        error_log("No activity questions found in activity_questions table, trying quiz_questions table");
        $questions = getQuizQuestions($activityId);
        error_log("Activity questions from quiz_questions: " . (is_string($questions) ? $questions : json_encode($questions)));
        if (is_string($questions)) {
            $questions = [];
        }
    }
} else if ($activity['activity_type'] == 'assignment') {
    // For assignments, use the same function as activities for consistency
    error_log("Using getActivityQuestions for assignment $activityId");
    $questions = getActivityQuestions($activityId);
    error_log("Assignment questions result: " . (is_string($questions) ? $questions : json_encode($questions)));
    if (is_string($questions)) {
        $questions = [];
    }
} else {
    $questions = [];
}

// Randomize questions for students
if (isStudent() && !empty($questions)) {
    // Save the original question order in a session variable if not already set
    if (!isset($_SESSION['original_question_order_'.$activityId])) {
        $_SESSION['original_question_order_'.$activityId] = [];
        foreach ($questions as $index => $question) {
            $_SESSION['original_question_order_'.$activityId][$question['question_id']] = $index;
        }
    }

    // Randomize the questions
    shuffle($questions);

    // Also randomize options for multiple choice and true/false questions
    foreach ($questions as &$question) {
        if (($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false') && isset($question['options'])) {
            shuffle($question['options']);
        }
    }
}

// Initialize variables
$content = "";
$answers = [];
$content_err = "";
$file_err = "";
$success = "";

// Check if this activity has questions (for quiz, activity, or assignment)
$hasQuestions = ($activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') && count($questions) > 0;

// Initialize $requiresFileOnly variable
$requiresFileOnly = false;
if ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity') {
    $requiresFileOnly = strpos(strtolower($activity['description']), 'file') !== false;
}

// Generate a unique form token if it doesn't exist
if (!isset($_SESSION['form_token'])) {
    $_SESSION['form_token'] = md5(uniqid(mt_rand(), true));
}

// Check if this is a page refresh after submission
if (isset($_SESSION['last_submission']) && $_SESSION['last_submission'] == $activityId) {
    // This is a page refresh after submission, redirect to prevent resubmission
    if ($activity['activity_type'] == 'quiz') {
        header("location: quiz_view.php?id=$activityId");
    } else {
        header("location: activity_view.php?id=$activityId");
    }
    exit;
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // For debugging
    error_log("POST data received: " . print_r($_POST, true));

    // Check if this is a direct submission (bypass token check)
    $isDirectSubmission = isset($_POST['direct_submission']) && $_POST['direct_submission'] == '1';
    error_log("Is direct submission: " . ($isDirectSubmission ? 'Yes' : 'No'));

    // Verify the form token to prevent duplicate submissions (unless it's a direct submission)
    if (!$isDirectSubmission && (!isset($_POST['form_token']) || $_POST['form_token'] !== $_SESSION['form_token'])) {
        // Invalid token, generate a new one
        $_SESSION['form_token'] = md5(uniqid(mt_rand(), true));
        error_log("Form token validation failed");
    } else {
        error_log("Form token validation passed");
    // Validate content for assignments
    if ($activity['activity_type'] == 'assignment') {
        if (!$requiresFileOnly) {
            if (empty(trim($_POST["content"]))) {
                $content_err = "Please enter your submission content.";
            } else {
                $content = trim($_POST["content"]);
            }
        }
    }

    // Validate answers for quizzes, activities, and assignments with questions
    if ($activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity' ||
        ($activity['activity_type'] == 'assignment' && $hasQuestions)) {

        error_log("Processing answers for activity type: " . $activity['activity_type']);
        error_log("Questions count: " . count($questions));

        $answers = [];

        foreach ($questions as $question) {
            $questionId = $question['question_id'];
            $questionType = $question['question_type'];

            error_log("Processing question ID: $questionId, Type: $questionType");

            if ($questionType == 'multiple_choice') {
                if (isset($_POST["answer_$questionId"])) {
                    $answers[$questionId] = $_POST["answer_$questionId"];
                    error_log("Multiple choice answer for question $questionId: " . $_POST["answer_$questionId"]);
                } else {
                    error_log("No answer provided for multiple choice question $questionId");
                }
            } elseif ($questionType == 'true_false') {
                if (isset($_POST["answer_$questionId"])) {
                    // For true/false questions, handle custom option IDs if needed
                    $optionId = $_POST["answer_$questionId"];
                    error_log("True/False raw answer for question $questionId: $optionId");

                    // If the option ID is a string like 'true_123' or 'false_123'
                    if (is_string($optionId) && (strpos($optionId, 'true_') === 0 || strpos($optionId, 'false_') === 0)) {
                        // Find the actual option ID from the database if available
                        if (isset($question['options']) && !empty($question['options'])) {
                            foreach ($question['options'] as $option) {
                                if ((strpos($optionId, 'true_') === 0 && strtolower($option['option_text']) == 'true') ||
                                    (strpos($optionId, 'false_') === 0 && strtolower($option['option_text']) == 'false')) {
                                    $optionId = $option['option_id'];
                                    error_log("Mapped true/false option ID for question $questionId: $optionId");
                                    break;
                                }
                            }
                        }
                    }

                    $answers[$questionId] = $optionId;
                } else {
                    error_log("No answer provided for true/false question $questionId");
                }
            } elseif ($questionType == 'short_answer') {
                if (isset($_POST["answer_$questionId"]) && !empty($_POST["answer_$questionId"])) {
                    $answers[$questionId] = $_POST["answer_$questionId"];
                    error_log("Short answer for question $questionId: " . $_POST["answer_$questionId"]);
                } else {
                    error_log("No answer provided for short answer question $questionId");
                }
            }
        }

        // Check if all questions are answered
        if (count($answers) < count($questions)) {
            $_SESSION['error'] = "Please answer all questions.";
            error_log("Not all questions answered. Answers count: " . count($answers) . ", Questions count: " . count($questions));
        } else {
            error_log("All questions answered. Answers: " . print_r($answers, true));
        }
    }

    // Handle file upload if present
    $filePath = null;
    $fileName = null;
    $fileType = null;
    $fileSize = null;

    // Validate file upload for file-only assignments
    if ($requiresFileOnly && (!isset($_FILES['submission_file']) || $_FILES['submission_file']['error'] != 0)) {
        $file_err = "Please upload a file for this assignment.";
    } elseif (isset($_FILES['submission_file']) && $_FILES['submission_file']['error'] == 0) {
        $uploadDir = 'uploads/submissions/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $fileName = $_FILES['submission_file']['name'];
        $fileType = $_FILES['submission_file']['type'];
        $fileSize = $_FILES['submission_file']['size'];
        $fileTmp = $_FILES['submission_file']['tmp_name'];

        // Generate unique filename
        $uniqueName = uniqid() . '_' . $fileName;
        $filePath = $uploadDir . $uniqueName;

        // Move the file
        if (!move_uploaded_file($fileTmp, $filePath)) {
            $file_err = "Error uploading file.";
        }
    }

    // Check input errors before submitting the activity
    if (empty($content_err) && empty($file_err) && empty($_SESSION['error'])) {
        error_log("No errors, proceeding with submission");

        // For assignments with questions, make sure we have answers
        if ($activity['activity_type'] == 'assignment' && $hasQuestions && empty($answers)) {
            error_log("Assignment has questions but no answers provided");
            $_SESSION['error'] = "Please answer all questions before submitting.";
        } else {
            // Submit the activity
            error_log("Calling submitActivity with activityId: $activityId, userId: " . $_SESSION['user_id'] . ", answers count: " . count($answers));
            error_log("Activity type: " . $activity['activity_type']);
            error_log("Answers data: " . print_r($answers, true));

            // For all activity types with questions, ensure we have the questions
            if ($hasQuestions) {
                error_log("Processing " . $activity['activity_type'] . " with questions");

                // Double check that we have the questions
                if (empty($questions)) {
                    error_log("No questions found for " . $activity['activity_type'] . ", trying to fetch them again");

                    if ($activity['activity_type'] == 'quiz') {
                        $questions = getQuizQuestions($activityId);
                    } else {
                        // Both activities and assignments use the same function
                        $questions = getActivityQuestions($activityId);
                    }

                    error_log("Questions after retry: " . (is_string($questions) ? $questions : json_encode($questions)));
                }

                // Make sure we have the correct question IDs in the answers array
                if (!empty($questions) && !empty($answers)) {
                    error_log("Checking answer keys against question IDs");
                    foreach ($questions as $question) {
                        $questionId = $question['question_id'];
                        if (!isset($answers[$questionId])) {
                            error_log("Missing answer for question ID: $questionId");
                        } else {
                            error_log("Found answer for question ID: $questionId - " . $answers[$questionId]);
                        }
                    }
                }
            }

            // Submit the activity
            try {
                $result = submitActivity($activityId, $_SESSION['user_id'], $content, $answers, $filePath, $fileName, $fileType, $fileSize);
                error_log("Submit activity result: " . print_r($result, true));
            } catch (Exception $e) {
                error_log("Exception in submitActivity: " . $e->getMessage());
                $result = "Error: " . $e->getMessage();
            }

            error_log("submitActivity result: " . print_r($result, true));

            if (is_numeric($result)) {
                // Submission successful
                $_SESSION['success'] = ucfirst($activity['activity_type']) . " submitted successfully!";

                // Set the last submission flag to prevent resubmission on refresh
                $_SESSION['last_submission'] = $activityId;

                // Generate a new token for future submissions
                $_SESSION['form_token'] = md5(uniqid(mt_rand(), true));

                // For AJAX requests, return a JSON response with detailed success information
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => ucfirst($activity['activity_type']) . " submitted successfully!",
                        'submission_id' => $result,
                        'activity_type' => $activity['activity_type'],
                        'activity_id' => $activityId,
                        'redirect_url' => ($activity['activity_type'] == 'quiz') ?
                            "quiz_view.php?id=$activityId" : "activity_view.php?id=$activityId" // Both activities and assignments use activity_view.php
                    ]);
                    exit;
                }

                // For regular form submissions, redirect
                if ($activity['activity_type'] == 'quiz') {
                    header("location: quiz_view.php?id=$activityId");
                } else {
                    // Both activities and assignments redirect to activity_view.php
                    header("location: activity_view.php?id=$activityId");
                }
                exit;
            } else {
                // Error submitting activity
                $_SESSION['error'] = $result;
                error_log("Error submitting activity: $result");

                // For AJAX requests, return a JSON response with detailed error information
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => $result,
                        'debug_info' => [
                            'activity_type' => $activity['activity_type'],
                            'activity_id' => $activityId,
                            'has_questions' => $hasQuestions,
                            'answers_count' => count($answers),
                            'questions_count' => count($questions),
                            'error_details' => is_string($result) ? $result : 'Unknown error'
                        ]
                    ]);
                    exit;
                }
            }
        }
        }
    }
}

// Set page title - use 'Submit' for all types for consistency
$action = 'Submit';
$page_title = $action . " " . ucfirst($activity['activity_type']) . " - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<style>
/* Option styling for multiple choice and true/false questions */
.option-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
}

.option-circle.selected {
    border-color: #007bff;
}

.inner-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: transparent;
    transition: background-color 0.2s ease;
}

.option-circle.selected .inner-circle {
    background-color: #007bff;
}

.selected-option {
    background-color: #e3f2fd;
    border-color: #007bff !important;
}

.form-check {
    padding: 10px;
    border-radius: 4px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-check:hover {
    background-color: #f8f9fa;
}

.option-container:hover {
    background-color: #f8f9fa;
}

.option-container {
    transition: all 0.2s ease;
}

/* Comment styles */
.comment-input-container {
    border-radius: 4px;
}

/* Style for formatted text */
.comment-item ul {
    padding-left: 20px;
    margin-bottom: 0;
}

.comment-item li {
    margin-bottom: 2px;
}
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-9">
            <!-- Back button -->
            <div class="mb-3">
                <?php if ($activity['activity_type'] == 'quiz'): ?>
                <a href="quiz_view.php?id=<?php echo $activityId; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Quiz
                </a>
                <?php else: ?>
                <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to <?php echo ucfirst($activity['activity_type']); ?>
                </a>
                <?php endif; ?>
            </div>

            <h1 class="mb-3"><?php echo ucfirst($activity['activity_type']); ?>: <?php echo htmlspecialchars($activity['title']); ?></h1>

            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><?php echo ucfirst($activity['activity_type']); ?> Information</h5>
                        <?php if (!empty($activity['due_date'])):
                            $dueDate = new DateTime($activity['due_date']);
                            $now = new DateTime();
                            $dueDatePassed = ($now > $dueDate);
                        ?>
                            <span class="badge <?php echo $dueDatePassed ? 'badge-danger' : 'badge-info'; ?>">
                                Due: <?php echo date('F j, Y, g:i a', strtotime($activity['due_date'])); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <p><?php echo nl2br(htmlspecialchars($activity['description'])); ?></p>
                    <?php if ($hasQuestions): ?>
                    <p><strong>Total Questions:</strong> <?php echo count($questions); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post" enctype="multipart/form-data" id="activity-form">
                <!-- Common submission form for all activity types -->
                <?php
                // Content and file upload sections removed as requested
                ?>

                <?php if ($hasQuestions): ?>
                <!-- Questions section for quiz or activity -->
                <?php if (count($questions) > 0): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><?php echo ucfirst($activity['activity_type']); ?> Questions</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($questions as $index => $question): ?>
                            <div class="question-container mb-4 pb-4 <?php echo $index < count($questions) - 1 ? 'border-bottom' : ''; ?>" id="question_<?php echo $question['question_id']; ?>">
                                <h5 class="question-text mb-3">
                                    <span class="badge badge-primary mr-2"><?php echo $index + 1; ?></span>
                                    <?php echo htmlspecialchars($question['question_text']); ?>
                                    <span class="badge badge-secondary ml-2"><?php echo $question['points']; ?> points</span>
                                </h5>

                                <?php if ($question['question_type'] == 'multiple_choice' && isset($question['options'])): ?>
                                <div class="options-container">
                                    <?php foreach ($question['options'] as $option): ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_<?php echo $option['option_id']; ?>" value="<?php echo $option['option_id']; ?>" <?php echo (isset($answers[$question['question_id']]) && $answers[$question['question_id']] == $option['option_id']) ? 'checked' : ''; ?> required>
                                        <label class="form-check-label" for="option_<?php echo $option['option_id']; ?>">
                                            <?php echo htmlspecialchars($option['option_text']); ?>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php elseif ($question['question_type'] == 'true_false'): ?>
                                <div class="options-container">
                                    <?php
                                    // For true/false questions, ensure we have True and False options
                                    $trueOption = null;
                                    $falseOption = null;

                                    // Check if options are already set
                                    if (isset($question['options']) && !empty($question['options'])) {
                                        foreach ($question['options'] as $option) {
                                            if (strtolower($option['option_text']) == 'true') {
                                                $trueOption = $option;
                                            } elseif (strtolower($option['option_text']) == 'false') {
                                                $falseOption = $option;
                                            }
                                        }
                                    }

                                    // If no true option, create a default one
                                    if (!$trueOption) {
                                        $trueOption = [
                                            'option_id' => 'true_' . $question['question_id'],
                                            'option_text' => 'True',
                                            'is_correct' => false
                                        ];
                                    }

                                    // If no false option, create a default one
                                    if (!$falseOption) {
                                        $falseOption = [
                                            'option_id' => 'false_' . $question['question_id'],
                                            'option_text' => 'False',
                                            'is_correct' => false
                                        ];
                                    }

                                    // Display the options
                                    $options = [$trueOption, $falseOption];
                                    foreach ($options as $option):
                                    ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_<?php echo $option['option_id']; ?>" value="<?php echo $option['option_id']; ?>" <?php echo (isset($answers[$question['question_id']]) && $answers[$question['question_id']] == $option['option_id']) ? 'checked' : ''; ?> required>
                                        <label class="form-check-label" for="option_<?php echo $option['option_id']; ?>">
                                            <?php echo htmlspecialchars($option['option_text']); ?>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                <div class="form-group">
                                    <input type="text" name="answer_<?php echo $question['question_id']; ?>" class="form-control" placeholder="Your answer" value="<?php echo isset($answers[$question['question_id']]) ? htmlspecialchars($answers[$question['question_id']]) : ''; ?>">
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">No questions found for this activity.</div>
                <?php endif; ?>
                <?php endif; ?>

                <div class="form-group text-center mt-4">
                    <button type="submit" name="submit_activity" id="submit-activity-btn" class="btn btn-primary btn-lg">
                        Submit
                    </button>
                </div>

                <!-- Add a hidden input to ensure the form is submitted even if JavaScript fails -->
                <input type="hidden" name="submit_activity" value="1">

                <!-- Add form token to prevent duplicate submissions -->
                <input type="hidden" name="form_token" value="<?php echo $_SESSION['form_token']; ?>">
            </form>
        </div>

        <!-- Sidebar section similar to Google Classroom -->
        <div class="col-md-3">
            <?php if ($hasQuestions && count($questions) > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo ucfirst($activity['activity_type']); ?> Navigation</h5>
                </div>
                <div class="card-body">
                    <div class="question-nav">
                        <?php foreach ($questions as $index => $question): ?>
                            <a href="#question_<?php echo $question['question_id']; ?>" class="btn btn-outline-primary mb-2 question-nav-btn" data-question="<?php echo $index + 1; ?>">
                                Question <?php echo $index + 1; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Private comments section -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-user-circle text-secondary mr-2"></i>
                        <h5 class="card-title mb-0" id="comments-title">Private comments</h5>
                    </div>
                    <div id="comments-container">
                        <!-- Comments will be loaded here -->
                        <?php
                        // Check if there are any saved comments for this activity
                        $comments = [];
                        $commentsFile = 'data/comments_' . $activityId . '.json';
                        if (file_exists($commentsFile)) {
                            $comments = json_decode(file_get_contents($commentsFile), true);
                        }

                        if (!empty($comments)):
                        ?>
                            <div class="comment-list">
                                <?php foreach ($comments as $index => $comment): ?>
                                <div class="comment-item mb-3" data-comment-id="<?php echo $index; ?>">
                                    <div class="d-flex align-items-start">
                                        <img src="<?php echo !empty($comment['avatar']) ? $comment['avatar'] : 'images/default-avatar.jpg'; ?>"
                                             class="rounded-circle mr-2" width="40" height="40" alt="User avatar">
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong class="d-block"><?php echo htmlspecialchars($comment['username']); ?></strong>
                                                    <small class="text-muted"><?php echo htmlspecialchars($comment['timestamp']); ?></small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <a class="dropdown-item delete-comment" href="#" data-comment-id="<?php echo $index; ?>">Delete</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="mb-0 mt-1"><?php echo formatCommentText(htmlspecialchars($comment['content'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Comment link (always visible) -->
                    <div class="mt-3">
                        <a href="#" class="text-primary" id="add-comment-link">
                            Add comment to Instructor
                        </a>
                    </div>

                    <!-- Comment input field (hidden initially) -->
                    <div id="comment-input-container" class="comment-input-container border rounded p-0 mt-2" style="display: none;">
                        <div class="d-flex align-items-center">
                            <input type="text" id="comment-input" class="form-control border-0 py-2" placeholder="Add comment...">
                            <button type="button" class="btn btn-light rounded-0 py-2 px-3 border-left" id="send-comment-btn" title="Send">
                                <i class="fas fa-paper-plane text-primary"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Comment Confirmation Modal -->
            <div class="modal fade" id="deleteCommentModal" tabindex="-1" role="dialog" aria-labelledby="deleteCommentModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteCommentModalLabel">Delete Comment</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            Are you sure you want to delete this comment?
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteComment">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add JavaScript for the sidebar functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap dropdowns
    $('.dropdown-toggle').dropdown();

    // Show comment input when link is clicked
    document.getElementById('add-comment-link').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('comment-input-container').style.display = 'block';
        document.getElementById('comment-input').focus();
    });

    // Add comment functionality
    document.getElementById('send-comment-btn').addEventListener('click', function() {
        const commentInput = document.getElementById('comment-input');
        const commentText = commentInput.value.trim();

        if (commentText !== '') {
            // Create a new comment object
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            const comment = {
                username: '<?php echo isset($_SESSION['first_name']) && isset($_SESSION['last_name']) ?
                    strtoupper($_SESSION['first_name'] . ' ' . $_SESSION['last_name']) :
                    (isset($_SESSION['username']) ? strtoupper($_SESSION['username']) : 'USER'); ?>',
                content: commentText,
                timestamp: timeString,
                avatar: '<?php echo isset($_SESSION['avatar']) ? $_SESSION['avatar'] : ''; ?>',
                date: now.toISOString()
            };

            // Save the comment to the server
            saveComment(comment);

            // Update the comments title
            updateCommentsCount();

            // Clear the input field and hide the input container
            commentInput.value = '';
            document.getElementById('comment-input-container').style.display = 'none';
        }
    });

    // Function to save a comment
    function saveComment(comment) {
        // Create a form to submit the comment
        const formData = new FormData();
        formData.append('action', 'save_comment');
        formData.append('activity_id', '<?php echo $activityId; ?>');
        formData.append('comment', JSON.stringify(comment));

        // Send the comment to the server
        fetch('save_comment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add the comment to the UI
                addCommentToUI(comment, data.comment_id);
            } else {
                alert('Failed to save comment: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving comment:', error);

            // Even if the server save fails, show the comment in the UI
            // with a note that it's not saved
            addCommentToUI(comment, 'temp_' + Date.now());
        });
    }

    // Function to add a comment to the UI
    function addCommentToUI(comment, commentId) {
        const commentsContainer = document.getElementById('comments-container');

        // Check if there's a comment list already
        let commentList = commentsContainer.querySelector('.comment-list');
        if (!commentList) {
            commentList = document.createElement('div');
            commentList.className = 'comment-list';
            commentsContainer.appendChild(commentList);
        }

        // Create the comment element
        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item mb-3';
        commentElement.dataset.commentId = commentId;

        // Create the comment HTML
        commentElement.innerHTML = `
            <div class="d-flex align-items-start">
                <img src="${comment.avatar || 'images/default-avatar.jpg'}" class="rounded-circle mr-2" width="40" height="40" alt="User avatar">
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong class="d-block">${comment.username}</strong>
                            <small class="text-muted">${comment.timestamp}</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a class="dropdown-item delete-comment" href="#" data-comment-id="${commentId}">Delete</a>
                            </div>
                        </div>
                    </div>
                    <p class="mb-0 mt-1">${formatCommentTextJS(comment.content)}</p>
                </div>
            </div>
        `;

        // Add the comment to the list
        commentList.appendChild(commentElement);

        // Add event listener for the delete button
        const deleteButton = commentElement.querySelector('.delete-comment');
        deleteButton.addEventListener('click', handleDeleteComment);
    }

    // Function to format comment text in JavaScript
    function formatCommentTextJS(text) {
        if (!text) return '';

        // Escape HTML
        text = text.replace(/&/g, '&amp;')
                   .replace(/</g, '&lt;')
                   .replace(/>/g, '&gt;')
                   .replace(/"/g, '&quot;')
                   .replace(/'/g, '&#039;');

        // Convert newlines to <br>
        text = text.replace(/\n/g, '<br>');

        return text;
    }

    // Function to update the comments count in the title
    function updateCommentsCount() {
        // We're not updating the title anymore as per the new design
        // The title remains "Private comments" regardless of count
    }

    // Handle delete comment button clicks
    function handleDeleteComment(e) {
        e.preventDefault();
        const commentId = this.dataset.commentId;

        // Store the comment ID to be deleted
        document.getElementById('confirmDeleteComment').dataset.commentId = commentId;

        // Show the confirmation modal
        $('#deleteCommentModal').modal('show');
    }

    // Add event listeners to existing delete buttons
    document.querySelectorAll('.delete-comment').forEach(button => {
        button.addEventListener('click', handleDeleteComment);
    });

    // Handle confirm delete button click
    document.getElementById('confirmDeleteComment').addEventListener('click', function() {
        const commentId = this.dataset.commentId;

        // Create a form to submit the delete request
        const formData = new FormData();
        formData.append('action', 'delete_comment');
        formData.append('activity_id', '<?php echo $activityId; ?>');
        formData.append('comment_id', commentId);

        // Send the delete request to the server
        fetch('save_comment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the comment from the UI
                const commentElement = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
                if (commentElement) {
                    commentElement.remove();

                    // Update the comments count
                    const commentsContainer = document.getElementById('comments-container');
                    const commentItems = commentsContainer.querySelectorAll('.comment-item');
                    const count = commentItems.length;

                    const commentsTitle = document.getElementById('comments-title');
                    if (count === 0) {
                        commentsTitle.innerHTML = '<i class="fas fa-user-circle mr-2"></i> Private comments';
                    } else if (count === 1) {
                        commentsTitle.innerHTML = '<i class="fas fa-user-circle mr-2"></i> 1 private comment';
                    } else {
                        commentsTitle.innerHTML = `<i class="fas fa-user-circle mr-2"></i> ${count} private comments`;
                    }
                }
            } else {
                alert('Failed to delete comment: ' + data.message);
            }

            // Hide the modal
            $('#deleteCommentModal').modal('hide');
        })
        .catch(error => {
            console.error('Error deleting comment:', error);
            alert('An error occurred while deleting the comment.');

            // Hide the modal
            $('#deleteCommentModal').modal('hide');
        });
    });

    // Allow pressing Enter to send comment
    document.getElementById('comment-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('send-comment-btn').click();
        }
    });

    // No formatting buttons anymore
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
// Make all option containers clickable
document.addEventListener('DOMContentLoaded', function() {
    // Function to handle option selection
    function handleOptionSelection() {
        // Target the form-check elements which contain the radio buttons
        const optionContainers = document.querySelectorAll('.form-check');

        optionContainers.forEach(container => {
            container.addEventListener('click', function(e) {
                // Prevent default if clicking on the label or container (not the radio itself)
                if (e.target.type !== 'radio') {
                    e.preventDefault();
                }

                // Find the radio input inside this container
                const radio = this.querySelector('input[type="radio"]');
                if (!radio) return;

                // Check the radio button
                radio.checked = true;

                // Trigger change event to ensure form validation recognizes the selection
                const changeEvent = new Event('change', { bubbles: true });
                radio.dispatchEvent(changeEvent);

                // Update styling for all options in this question group
                const questionOptions = document.querySelectorAll(`input[name="${radio.name}"]`);
                questionOptions.forEach(input => {
                    const optContainer = input.closest('.form-check');

                    if (input.checked) {
                        optContainer.classList.add('selected-option');
                    } else {
                        optContainer.classList.remove('selected-option');
                    }
                });

                // Auto-submit if all questions are answered
                const allRadios = document.querySelectorAll('input[type="radio"][required]');
                const allRadioGroups = new Set();
                allRadios.forEach(radio => allRadioGroups.add(radio.name));

                let allAnswered = true;
                allRadioGroups.forEach(name => {
                    const checkedInGroup = document.querySelector(`input[name="${name}"]:checked`);
                    if (!checkedInGroup) {
                        allAnswered = false;
                    }
                });

                // If there's only one question, auto-submit after selection
                if (allRadioGroups.size === 1 || allAnswered) {
                    // Add a slight delay before submitting
                    setTimeout(() => {
                        // Get the submit button and trigger its click event
                        const submitBtn = document.getElementById('submit-activity-btn');
                        if (submitBtn) {
                            // Show a visual indicator that submission is happening
                            const questionContainer = document.querySelector('.question-container');
                            if (questionContainer) {
                                const indicator = document.createElement('div');
                                indicator.className = 'text-center mt-3';
                                indicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">Submitting...</span></div><p class="mt-2">Submitting your answer...</p>';
                                questionContainer.appendChild(indicator);
                            }

                            // For assignments and activities, use the same approach
                            const activityType = '<?php echo $activity['activity_type']; ?>';
                            console.log('Auto-submitting ' + activityType);

                            // Click the submit button
                            submitBtn.click();
                        }
                    }, 800);
                }
            });
        });
    }

    // Initialize the option selection functionality
    handleOptionSelection();

    // Add a click handler for the submit button
    const submitBtn = document.getElementById('submit-activity-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            // Prevent the default button behavior
            e.preventDefault();

            // Get the form
            const form = document.getElementById('activity-form');
            if (form) {
                // Use AJAX to submit the form
                const formData = new FormData(form);

                // Disable the submit button to prevent multiple submissions
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                // Add X-Requested-With header to identify AJAX requests
                const headers = new Headers({
                    'X-Requested-With': 'XMLHttpRequest'
                });

                // For debugging
                console.log('Form data being submitted:');
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Submit the form using fetch API
                fetch(form.action, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    // Clone the response before reading it to avoid the "body stream already read" error
                    const responseClone = response.clone();

                    // Try to parse as JSON first
                    return response.json().catch(() => {
                        console.log('Response is not JSON, trying text instead');
                        return responseClone.text();
                    });
                })
                .then(data => {
                    console.log('Server response:', data);

                    // Check if the response is JSON
                    if (typeof data === 'object') {
                        if (data.success) {
                            console.log('Form submitted successfully:', data.message);

                            // Show success message
                            const successMessage = document.createElement('div');
                            successMessage.className = 'alert alert-success mt-3';
                            successMessage.textContent = data.message || 'Submission successful!';
                            document.querySelector('.card-body').appendChild(successMessage);

                            // Use the redirect URL from the response if available
                            const redirectUrl = data.redirect_url || '<?php echo ($activity['activity_type'] == 'quiz') ?
                                "quiz_view.php?id=$activityId" : "activity_view.php?id=$activityId"; // Both activities and assignments use activity_view.php ?>';

                            console.log('Will redirect to:', redirectUrl);

                            // Set a small delay before redirecting to ensure the server has time to process
                            setTimeout(() => {
                                window.location.href = redirectUrl;
                            }, 1500);
                        } else {
                            console.error('Submission error:', data);

                            // Show detailed error message
                            const errorMessage = document.createElement('div');
                            errorMessage.className = 'alert alert-danger mt-3';

                            // Create more detailed error message
                            let errorText = data.message || 'There was a problem submitting your answers.';
                            if (data.debug_info) {
                                errorText += '<br><small>Details: ';
                                if (data.debug_info.error_details) {
                                    errorText += data.debug_info.error_details;
                                }
                                errorText += '</small>';
                            }

                            errorMessage.innerHTML = errorText;
                            document.querySelector('.card-body').appendChild(errorMessage);

                            // Re-enable the submit button
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = 'Submit';

                            // Add a retry button
                            const retryBtn = document.createElement('button');
                            retryBtn.className = 'btn btn-warning mt-2 ml-2';
                            retryBtn.textContent = 'Retry Submission';
                            retryBtn.onclick = function() {
                                errorMessage.remove();
                                retryBtn.remove();
                                submitBtn.click();
                            };
                            document.querySelector('.card-body').appendChild(retryBtn);
                        }
                    } else {
                        // Response is not JSON, assume success and redirect
                        console.log('Form submitted successfully (non-JSON response)');

                        // Show success message
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success mt-3';
                        successMessage.textContent = 'Submission successful!';
                        document.querySelector('.card-body').appendChild(successMessage);

                        setTimeout(() => {
                            window.location.href = '<?php echo ($activity['activity_type'] == 'quiz') ?
                                "quiz_view.php?id=$activityId" : "activity_view.php?id=$activityId"; // Both activities and assignments use activity_view.php ?>';
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('Error submitting form:', error);
                    // Re-enable the submit button
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Submit';

                    // Show a more detailed error message
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'alert alert-danger mt-3';
                    errorMessage.textContent = 'Error: ' + error.message;
                    document.querySelector('.card-body').appendChild(errorMessage);

                    // Add a retry button
                    const retryBtn = document.createElement('button');
                    retryBtn.className = 'btn btn-warning mt-2';
                    retryBtn.textContent = 'Retry Submission';
                    retryBtn.onclick = function() {
                        errorMessage.remove();
                        retryBtn.remove();
                        submitBtn.click();
                    };
                    document.querySelector('.card-body').appendChild(retryBtn);

                    // Create a direct form submission button
                    const directSubmitBtn = document.createElement('button');
                    directSubmitBtn.className = 'btn btn-secondary mt-2 ml-2';
                    directSubmitBtn.textContent = 'Try Direct Submission';
                    directSubmitBtn.onclick = function() {
                        // Add a hidden input to indicate this is a direct submission
                        const directInput = document.createElement('input');
                        directInput.type = 'hidden';
                        directInput.name = 'direct_submission';
                        directInput.value = '1';
                        form.appendChild(directInput);

                        // Submit the form directly
                        console.log('Attempting direct form submission');
                        form.submit();
                    };
                    document.querySelector('.card-body').appendChild(directSubmitBtn);
                });
            }
        });
    }
});
</script>
