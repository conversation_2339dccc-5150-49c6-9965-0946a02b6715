<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can browse and request to join courses.";
    header("location: index.php");
    exit;
}

// Get all active courses
$courses = getAllCourses(true);

// Check if courses is an error message
if (is_string($courses)) {
    $error = $courses;
    $courses = [];
}

// Process enrollment request
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['course_id'])) {
    $courseId = intval($_POST['course_id']);
    
    // Create enrollment request
    $result = createEnrollmentRequest($_SESSION['user_id'], $courseId);
    
    if ($result === true) {
        $_SESSION['success'] = "Your enrollment request has been submitted. You will be notified when it is approved.";
    } else {
        $_SESSION['error'] = $result;
    }
    
    // Redirect to refresh the page
    header("location: courses_browse.php");
    exit;
}

// Set page title
$page_title = "Browse Courses";

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Browse Courses</h1>
    <div>
        <a href="index.php" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>
    </div>
</div>

<?php if (isset($_SESSION['success'])): ?>
<div class="alert alert-success">
    <?php 
    echo $_SESSION['success']; 
    unset($_SESSION['success']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
<div class="alert alert-danger">
    <?php 
    echo $_SESSION['error']; 
    unset($_SESSION['error']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (count($courses) > 0): ?>
    <div class="course-grid">
        <?php foreach ($courses as $course): ?>
        <?php 
        // Skip courses the student is already enrolled in
        if (isEnrolled($_SESSION['user_id'], $course['course_id'])) {
            continue;
        }
        ?>
        <div class="card course-card">
            <div class="course-card-header">
                <h2 class="course-card-title"><?php echo htmlspecialchars($course['title']); ?></h2>
                <?php if (isset($course['creator_name'])): ?>
                <p class="course-card-subtitle">Instructor: <?php echo htmlspecialchars($course['creator_name']); ?></p>
                <?php endif; ?>
            </div>
            <div class="course-card-body">
                <p><?php echo htmlspecialchars(substr($course['description'], 0, 100) . (strlen($course['description']) > 100 ? '...' : '')); ?></p>
                <div class="mt-3">
                    <?php if (isset($course['is_active']) && $course['is_active']): ?>
                        <span class="badge badge-success"><i class="fas fa-check-circle mr-1"></i> Active</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="course-card-footer">
                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <input type="hidden" name="course_id" value="<?php echo $course['course_id']; ?>">
                    <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to request enrollment in this course?');">
                        <i class="fas fa-user-slash"></i> Request
                    </button>
                </form>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <?php 
    // Check if all courses are filtered out because student is enrolled in all of them
    $allEnrolled = true;
    foreach ($courses as $course) {
        if (!isEnrolled($_SESSION['user_id'], $course['course_id'])) {
            $allEnrolled = false;
            break;
        }
    }
    
    if ($allEnrolled): 
    ?>
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h3>You're enrolled in all available courses</h3>
        <p class="empty-state-text">There are no additional courses available for enrollment at this time.</p>
        <a href="index.php" class="btn btn-primary">Back to Home</a>
    </div>
    <?php endif; ?>
<?php else: ?>
<div class="empty-state">
    <div class="empty-state-icon">
        <i class="fas fa-book-open"></i>
    </div>
    <h3>No courses available</h3>
    <p class="empty-state-text">There are no courses available for enrollment at this time.</p>
    <a href="index.php" class="btn btn-primary">Back to Home</a>
</div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
