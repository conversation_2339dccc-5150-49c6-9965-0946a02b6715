<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Activity ID is required.";
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if this is a material type activity
if ($activity['activity_type'] == 'material') {
    $_SESSION['error'] = "Materials do not have submissions.";
    header("location: course_view_full.php?id=$courseId&tab=classwork");
    exit;
}

// Check if user has access to view submissions
$hasAccess = false;

if (isAdmin()) {
    $hasAccess = true;
} elseif (isTeacher()) {
    // Teachers can access if they created the course or are instructors
    if ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)) {
        $hasAccess = true;
    }
}

if (!$hasAccess) {
    $_SESSION['error'] = "You do not have permission to view submissions for this activity.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Get all submissions for this activity
// First try to get submissions from activity_submissions table
$submissions = getSubmissionsByActivity($activityId);
if (is_string($submissions)) {
    // If that fails, try the submissions table
    $submissions = getActivitySubmissions($activityId);
    if (is_string($submissions)) {
        $submissions = [];
    }
}

// Set page title
$page_title = "Submissions for " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($activity['title']); ?></a></li>
                    <li class="breadcrumb-item active">Submissions</li>
                </ol>
            </nav>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">Submissions for <?php echo htmlspecialchars($activity['title']); ?></h1>
                    <div>
                        <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Activity
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <?php if (count($submissions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Student</th>
                                    <th>Submission Date</th>
                                    <th>Status</th>
                                    <th>Score</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($submissions as $submission): ?>
                                <tr>
                                    <td>
                                        <?php echo htmlspecialchars($submission['first_name'] . ' ' . $submission['last_name']); ?>
                                        <small class="text-muted d-block"><?php echo htmlspecialchars($submission['username']); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        // Handle different date field names between tables
                                        $dateField = isset($submission['submission_date']) ? 'submission_date' : 'submitted_at';
                                        echo date('M j, Y g:i A', strtotime($submission[$dateField]));
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        // Handle different grading field names between tables
                                        $isGraded = false;
                                        if (isset($submission['grade']) && $submission['grade'] !== null) {
                                            $isGraded = true;
                                        } elseif (isset($submission['score']) && $submission['score'] !== null) {
                                            $isGraded = true;
                                        } elseif (isset($submission['is_graded']) && $submission['is_graded']) {
                                            $isGraded = true;
                                        }

                                        if ($isGraded):
                                        ?>
                                        <span class="badge badge-success">Graded</span>
                                        <?php else: ?>
                                        <span class="badge badge-warning">Not Graded</span>
                                        <?php endif; ?>
                                    </td>

                                    <td>
                                        <?php
                                        // Handle different score field names between tables
                                        $score = null;
                                        if (isset($submission['grade']) && $submission['grade'] !== null) {
                                            $score = $submission['grade'];
                                        } elseif (isset($submission['score']) && $submission['score'] !== null) {
                                            $score = $submission['score'];
                                        }

                                        if ($score !== null):
                                            // Display score in the same format as student view (1/1)
                                            // This matches what students see in their view
                                        ?>
                                        <span class="font-weight-bold">
                                            <?php
                                            // Use centralized function to get total points according to instructor settings
                                            $totalActivityPoints = calculateActivityTotalPoints($activityId);

                                            // The score field now contains the actual points earned
                                            $earnedPoints = $score;

                                            // Display score according to instructor settings
                                            echo round($earnedPoints) . '/' . $totalActivityPoints;
                                            ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="text-muted">Not graded</span>
                                        <?php endif; ?>
                                    </td>


                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i> No submissions yet for this activity.
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Total Submissions: <?php echo count($submissions); ?></span>
                        </div>
                        <div>
                            <?php if (count($submissions) > 0): ?>
                            <a href="export_grades.php?activity_id=<?php echo $activityId; ?>" class="btn btn-outline-success">
                                <i class="fas fa-file-excel"></i> Export Points
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mb-4">
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Classwork
                </a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
