<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/assessment_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if assignment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$assignmentId = intval($_GET['id']);

// For demonstration purposes, we'll use a hardcoded assignment
// In a real implementation, you would fetch this from the database
$assignment = [
    'id' => 1,
    'title' => 'Introduction Assignment',
    'content' => 'Please introduce yourself to the class and share your expectations for this course.',
    'due_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
    'points' => 10,
    'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
    'course_id' => 1,
    'course_title' => 'Introduction to Programming'
];

// Check if form is submitted for assignment submission
$submission_content = "";
$submission_err = "";
$submission_success = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_assignment'])) {
    // Validate submission content
    if (empty(trim($_POST["submission_content"]))) {
        $submission_err = "Please enter your submission.";
    } else {
        $submission_content = trim($_POST["submission_content"]);
    }

    // If no errors, process submission
    if (empty($submission_err)) {
        // In a real implementation, you would save this to the database
        $submission_success = "Assignment submitted successfully!";
        $submission_content = ""; // Clear the form
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($assignment['title']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/classroom.css">
    <style>
        .assignment-header {
            background-color: var(--primary-color);
            color: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
        }
        .assignment-title {
            font-size: 24px;
            margin: 0 0 8px 0;
        }
        .assignment-meta {
            opacity: 0.8;
            margin: 0;
        }
        .assignment-content {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3);
            padding: 24px;
            margin-bottom: 24px;
        }
        .assignment-submission {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3);
            padding: 24px;
        }
        .file-upload {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            margin-bottom: 16px;
            cursor: pointer;
        }
        .file-upload:hover {
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="classroom-header">
        <a href="index.php" class="classroom-logo">
            <i class="fas fa-graduation-cap"></i>
            <?php echo APP_NAME; ?>
        </a>

        <div class="classroom-header-right">
            <button class="btn-icon" title="Help">
                <i class="fas fa-question-circle"></i>
            </button>
            <div class="user-avatar" title="<?php echo htmlspecialchars($_SESSION["username"]); ?>">
                <?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?>
            </div>
        </div>
    </header>

    <div class="classroom-container">
        <!-- Sidebar -->
        <aside class="classroom-sidebar">
            <ul class="classroom-sidebar-nav">
                <li class="classroom-sidebar-item">
                    <a href="index.php" class="classroom-sidebar-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="calendar.php" class="classroom-sidebar-link">
                        <i class="fas fa-calendar-alt"></i> Calendar
                    </a>
                </li>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="courses.php" class="classroom-sidebar-link">
                        <i class="fas fa-book"></i> Courses
                    </a>
                </li>
                <?php endif; ?>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="users.php" class="classroom-sidebar-link">
                        <i class="fas fa-users"></i> Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="classroom-sidebar-item">
                    <a href="profile.php" class="classroom-sidebar-link">
                        <i class="fas fa-user"></i> Profile
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="logout.php" class="classroom-sidebar-link">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main content -->
        <main class="classroom-content">
            <!-- Assignment header -->
            <div class="assignment-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="assignment-title"><?php echo htmlspecialchars($assignment['title']); ?></h1>
                        <p class="assignment-meta">
                            <?php echo htmlspecialchars($assignment['course_title']); ?> •
                            <?php echo $assignment['points']; ?> points •
                            Due <?php echo date('M j, Y', strtotime($assignment['due_date'])); ?>
                        </p>
                    </div>
                    <a href="course_view.php?id=<?php echo $assignment['course_id']; ?>" class="btn-classroom-outline">Back to Class</a>
                </div>
            </div>

            <?php if (!empty($submission_success)): ?>
            <div class="alert alert-success"><?php echo $submission_success; ?></div>
            <?php endif; ?>

            <!-- Assignment content -->
            <div class="assignment-content">
                <h2>Instructions</h2>
                <p><?php echo htmlspecialchars($assignment['content']); ?></p>
            </div>

            <!-- Assignment submission -->
            <?php if (isStudent()): ?>
            <div class="assignment-submission">
                <h2>Your Work</h2>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $assignmentId); ?>" method="post">
                    <div class="form-group">
                        <label for="submission_content">Your submission</label>
                        <textarea name="submission_content" id="submission_content" class="form-control <?php echo (!empty($submission_err)) ? 'is-invalid' : ''; ?>" rows="6"><?php echo $submission_content; ?></textarea>
                        <span class="invalid-feedback"><?php echo $submission_err; ?></span>
                    </div>

                    <div class="form-group">
                        <label>Attachments</label>
                        <div class="file-upload">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i>
                            <p class="mb-0">Drag files here or click to upload</p>
                        </div>
                    </div>

                    <div class="form-group text-right">
                        <button type="submit" name="submit_assignment" class="btn-classroom">Turn In</button>
                    </div>
                </form>
            </div>
            <?php elseif (isAdmin() || isTeacher()): ?>
            <div class="assignment-submission">
                <h2>Student Submissions</h2>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No submissions yet.
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
