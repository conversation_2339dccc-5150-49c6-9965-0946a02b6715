<?php
/**
 * Calendar Events Functions
 *
 * This file contains functions for managing calendar events.
 */

// Include notifications functions
require_once 'notifications.php';

/**
 * Get events for a specific month and year
 *
 * @param int $month The month (1-12)
 * @param int $year The year
 * @param bool $showCompleted Whether to show completed activities (default: false)
 * @param bool $showPastDue Whether to show past due activities (default: false)
 * @return array Array of events
 */
function getMonthEvents($month, $year, $showCompleted = false, $showPastDue = false) {
    global $pdo;

    $events = [];

    try {
        // Start and end dates for the month
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = sprintf('%04d-%02d-%02d', $year, $month, date('t', mktime(0, 0, 0, $month, 1, $year)));

        // Get user-created calendar events
        $calendarEvents = getUserCalendarEvents($startDate, $endDate);
        $events = array_merge($events, $calendarEvents);

        // Get activities (assignments, quizzes, etc.)
        $activityEvents = getActivityEvents($startDate, $endDate, $showCompleted, $showPastDue);
        $events = array_merge($events, $activityEvents);

        // Sort events by date
        usort($events, function($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });

        return $events;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error fetching calendar events: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user-created calendar events for a specific date range
 *
 * @param string $startDate Start date (YYYY-MM-DD)
 * @param string $endDate End date (YYYY-MM-DD)
 * @return array Array of events
 */
function getUserCalendarEvents($startDate, $endDate) {
    global $pdo;

    $events = [];

    try {
        // Check if the calendar_events table exists
        $tableExists = false;
        $stmt = $pdo->query("SHOW TABLES LIKE 'calendar_events'");
        if ($stmt && $stmt->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, return empty array
        if (!$tableExists) {
            return [];
        }

        // Build the query based on user role
        if (isAdmin()) {
            // Admin can see all events they created
            $sql = "SELECT * FROM calendar_events
                    WHERE user_id = :user_id AND event_date BETWEEN :start_date AND :end_date
                    ORDER BY event_date ASC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':user_id' => $_SESSION['user_id'],
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]);
        } else if (isTeacher()) {
            // Teachers see events for courses they teach
            $sql = "SELECT ce.*,
                    CASE
                        WHEN ce.user_id = :user_id THEN 'personal'
                        WHEN c.created_by = :user_id OR ci.instructor_id = :user_id THEN 'instructor'
                        ELSE 'system'
                    END as event_source
                    FROM calendar_events ce
                    LEFT JOIN courses c ON ce.course_id = c.course_id
                    LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                    WHERE (ce.user_id = :user_id
                           OR c.created_by = :user_id
                           OR ci.instructor_id = :user_id
                           OR ce.event_type IN ('system', 'admin'))
                    AND ce.event_date BETWEEN :start_date AND :end_date
                    ORDER BY ce.event_date ASC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':user_id' => $_SESSION['user_id'],
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]);
        } else {
            // Students see events for courses they're enrolled in
            $sql = "SELECT ce.*,
                    CASE
                        WHEN ce.user_id = :user_id THEN 'personal'
                        WHEN e.user_id = :user_id THEN 'course'
                        ELSE 'system'
                    END as event_source,
                    c.title as course_title,
                    u.username as creator_name
                    FROM calendar_events ce
                    LEFT JOIN courses c ON ce.course_id = c.course_id
                    LEFT JOIN enrollments e ON c.course_id = e.course_id
                    LEFT JOIN users u ON ce.user_id = u.user_id
                    WHERE (ce.user_id = :user_id
                           OR e.user_id = :user_id
                           OR ce.event_type = 'system')
                    AND ce.event_date BETWEEN :start_date AND :end_date
                    ORDER BY ce.event_date ASC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':user_id' => $_SESSION['user_id'],
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]);
        }

        // Fetch all events
        $results = $stmt->fetchAll();

        foreach ($results as $row) {
            $events[] = [
                'id' => $row['event_id'],
                'date' => $row['event_date'],
                'title' => $row['title'],
                'description' => $row['description'],
                'type' => $row['event_type'],
                'course' => $row['course'],
                'course_id' => $row['course_id'],
                'item_id' => $row['item_id'],
                'status' => isset($row['status']) ? $row['status'] : 'pending',
                'source' => 'calendar'
            ];
        }

        return $events;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error fetching user calendar events: " . $e->getMessage());
        return [];
    }
}

/**
 * Get activity events (assignments, quizzes, etc.) for a specific date range
 *
 * @param string $startDate Start date (YYYY-MM-DD)
 * @param string $endDate End date (YYYY-MM-DD)
 * @param bool $showCompleted Whether to show completed activities (default: false)
 * @param bool $showPastDue Whether to show past due activities (default: false)
 * @return array Array of events
 */
function getActivityEvents($startDate, $endDate, $showCompleted = false, $showPastDue = false) {
    global $pdo;

    $events = [];

    try {
        // Check if the activities table exists
        $tableExists = false;
        $stmt = $pdo->query("SHOW TABLES LIKE 'activities'");
        if ($stmt && $stmt->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, return empty array
        if (!$tableExists) {
            return [];
        }

        // Current date for filtering past due activities
        $currentDate = date('Y-m-d H:i:s');

        // Build the query based on user role
        if (isAdmin()) {
            // Admin can see all activities
            $sql = "SELECT a.*, c.course_name, c.course_code
                    FROM activities a
                    JOIN courses c ON a.course_id = c.course_id
                    WHERE a.due_date BETWEEN :start_date AND :end_date
                    AND a.is_published = 1";

            // Add past due filter if needed
            if (!$showPastDue) {
                $sql .= " AND (a.due_date >= :current_date OR a.due_date IS NULL)";
            }

            $sql .= " ORDER BY a.due_date ASC";

            $stmt = $pdo->prepare($sql);
            $params = [
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ];

            if (!$showPastDue) {
                $params[':current_date'] = $currentDate;
            }

            $stmt->execute($params);
        } else if (isTeacher()) {
            // Teachers see activities for courses they teach
            $sql = "SELECT a.*, c.course_name, c.course_code
                    FROM activities a
                    JOIN courses c ON a.course_id = c.course_id
                    LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                    WHERE (c.created_by = :user_id OR ci.instructor_id = :user_id)
                    AND a.due_date BETWEEN :start_date AND :end_date
                    AND a.is_published = 1";

            // Add past due filter if needed
            if (!$showPastDue) {
                $sql .= " AND (a.due_date >= :current_date OR a.due_date IS NULL)";
            }

            $sql .= " ORDER BY a.due_date ASC";

            $stmt = $pdo->prepare($sql);
            $params = [
                ':user_id' => $_SESSION['user_id'],
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ];

            if (!$showPastDue) {
                $params[':current_date'] = $currentDate;
            }

            $stmt->execute($params);
        } else {
            // Students see activities for courses they're enrolled in
            $sql = "SELECT a.*, c.course_name, c.course_code,
                           CASE WHEN s.submission_id IS NOT NULL THEN 'done' ELSE 'pending' END as status
                    FROM activities a
                    JOIN courses c ON a.course_id = c.course_id
                    JOIN enrollments e ON c.course_id = e.course_id
                    LEFT JOIN assignment_submissions s ON a.activity_id = s.activity_id AND s.user_id = :user_id
                    WHERE e.user_id = :user_id
                    AND a.due_date BETWEEN :start_date AND :end_date
                    AND a.is_published = 1";

            // Filter out completed activities if needed
            if (!$showCompleted) {
                $sql .= " AND (s.submission_id IS NULL)";
            }

            // Add past due filter if needed
            if (!$showPastDue) {
                $sql .= " AND (a.due_date >= :current_date OR a.due_date IS NULL)";
            }

            $sql .= " ORDER BY a.due_date ASC";

            $stmt = $pdo->prepare($sql);
            $params = [
                ':user_id' => $_SESSION['user_id'],
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ];

            if (!$showPastDue) {
                $params[':current_date'] = $currentDate;
            }

            $stmt->execute($params);
        }

        // Fetch all activities
        $results = $stmt->fetchAll();

        foreach ($results as $row) {
            // Format the date (due_date includes time)
            $date = date('Y-m-d', strtotime($row['due_date']));

            $events[] = [
                'id' => $row['activity_id'],
                'date' => $date,
                'title' => $row['title'],
                'description' => $row['description'],
                'type' => $row['activity_type'],
                'course' => $row['course_name'] . ' (' . $row['course_code'] . ')',
                'course_id' => $row['course_id'],
                'item_id' => $row['activity_id'],
                'status' => isset($row['status']) ? $row['status'] : 'pending',
                'source' => 'activity',
                'points' => $row['points'],
                'due_date' => $row['due_date'] // Keep the full datetime for display
            ];
        }

        return $events;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error fetching activity events: " . $e->getMessage());
        return [];
    }
}

/**
 * Add a new calendar event
 *
 * @param string $title Event title
 * @param string $date Event date (YYYY-MM-DD)
 * @param string $type Event type
 * @param string $description Event description
 * @param string $course Course name (optional)
 * @param int $courseId Course ID (optional)
 * @param int $itemId Item ID (optional)
 * @return bool True if successful, false otherwise
 */
function addCalendarEvent($title, $date, $type, $description, $course = null, $courseId = null, $itemId = null) {
    global $pdo;

    try {
        // Check if the calendar_events table exists
        $tableExists = false;
        $stmt = $pdo->query("SHOW TABLES LIKE 'calendar_events'");
        if ($stmt && $stmt->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, create it
        if (!$tableExists) {
            $createTable = "CREATE TABLE calendar_events (
                event_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                event_date DATE NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                course VARCHAR(100),
                course_id INT,
                item_id INT,
                status ENUM('pending', 'done') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";

            if (!$pdo->query($createTable)) {
                error_log("Error creating calendar_events table");
                return false;
            }
        }

        $sql = "INSERT INTO calendar_events (user_id, title, description, event_date, event_type, course, course_id, item_id)
                VALUES (:user_id, :title, :description, :event_date, :event_type, :course, :course_id, :item_id)";
        $stmt = $pdo->prepare($sql);

        $result = $stmt->execute([
            ':user_id' => $_SESSION['user_id'],
            ':title' => $title,
            ':description' => $description,
            ':event_date' => $date,
            ':event_type' => $type,
            ':course' => $course,
            ':course_id' => $courseId,
            ':item_id' => $itemId
        ]);

        if ($result) {
            // Get the new event ID
            $eventId = $pdo->lastInsertId();

            // Create notification for the event creator
            $notificationTitle = "New Event Created";
            $notificationMessage = "You created a new event: $title on " . date('F j, Y', strtotime($date));
            createNotification($_SESSION['user_id'], $notificationTitle, $notificationMessage, 'event_creation', $eventId);

            // If this is a course event created by an instructor, notify enrolled students
            if (isTeacher() && $courseId !== null) {
                // Get all students enrolled in this course
                $enrolledStudentsStmt = $pdo->prepare("
                    SELECT u.user_id, u.username
                    FROM enrollments e
                    JOIN users u ON e.user_id = u.user_id
                    WHERE e.course_id = :course_id AND e.is_active = 1
                ");
                $enrolledStudentsStmt->execute([':course_id' => $courseId]);
                $enrolledStudents = $enrolledStudentsStmt->fetchAll();

                // Get instructor name
                $instructorName = $_SESSION['username'] ?? 'Your instructor';

                // Notify each enrolled student
                foreach ($enrolledStudents as $student) {
                    $notificationTitle = "New Calendar Event";
                    $notificationMessage = "$instructorName added a new event to your calendar: $title on " . date('F j, Y', strtotime($date));
                    createNotification($student['user_id'], $notificationTitle, $notificationMessage, 'event_creation', $eventId);
                }
            }

            // Notify admin of system events
            if (isAdmin() && $type == 'system') {
                $adminId = 1; // Assuming admin has user_id = 1, adjust as needed
                $notificationTitle = "New System Event Added";
                $notificationMessage = "A new system event was added: $title on " . date('F j, Y', strtotime($date));
                createNotification($adminId, $notificationTitle, $notificationMessage, 'event_creation', $eventId);
            }
        }

        return $result;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error adding calendar event: " . $e->getMessage());
        return false;
    }
}

/**
 * Update an existing calendar event
 *
 * @param int $eventId Event ID
 * @param string $title Event title
 * @param string $date Event date (YYYY-MM-DD)
 * @param string $type Event type
 * @param string $description Event description
 * @return bool True if successful, false otherwise
 */
function updateCalendarEvent($eventId, $title, $date, $type, $description) {
    global $pdo;

    try {
        // Only allow updating events created by the current user
        $sql = "UPDATE calendar_events
                SET title = :title, description = :description, event_date = :event_date, event_type = :event_type
                WHERE event_id = :event_id AND user_id = :user_id";
        $stmt = $pdo->prepare($sql);

        $result = $stmt->execute([
            ':title' => $title,
            ':description' => $description,
            ':event_date' => $date,
            ':event_type' => $type,
            ':event_id' => $eventId,
            ':user_id' => $_SESSION['user_id']
        ]);

        $rowsAffected = $stmt->rowCount();

        if ($rowsAffected > 0) {
            // Create notification for the event update
            $notificationTitle = "Event Updated";
            $notificationMessage = "You updated the event: $title on " . date('F j, Y', strtotime($date));
            createNotification($_SESSION['user_id'], $notificationTitle, $notificationMessage, 'event_update', $eventId);
        }

        return $rowsAffected > 0;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error updating calendar event: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete a calendar event
 *
 * @param int $eventId Event ID
 * @return bool True if successful, false otherwise
 */
function deleteCalendarEvent($eventId) {
    global $pdo;

    try {
        // Only allow deleting events created by the current user
        $sql = "DELETE FROM calendar_events
                WHERE event_id = :event_id AND user_id = :user_id";
        $stmt = $pdo->prepare($sql);

        // First, get event details for the notification
        $getEventSql = "SELECT title, event_date FROM calendar_events WHERE event_id = :event_id AND user_id = :user_id";
        $getEventStmt = $pdo->prepare($getEventSql);
        $getEventStmt->execute([
            ':event_id' => $eventId,
            ':user_id' => $_SESSION['user_id']
        ]);
        $event = $getEventStmt->fetch(PDO::FETCH_ASSOC);

        // Now delete the event
        $stmt->execute([
            ':event_id' => $eventId,
            ':user_id' => $_SESSION['user_id']
        ]);

        $rowsAffected = $stmt->rowCount();

        if ($rowsAffected > 0 && $event) {
            // Create notification for the event deletion
            $notificationTitle = "Event Deleted";
            $notificationMessage = "You deleted the event: " . $event['title'] . " on " .
                                  date('F j, Y', strtotime($event['event_date']));
            createNotification($_SESSION['user_id'], $notificationTitle, $notificationMessage, 'event_update', $eventId);
        }

        return $rowsAffected > 0;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error deleting calendar event: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark a calendar event as done or pending
 *
 * @param int $eventId Event ID
 * @param string $status Status ('done' or 'pending')
 * @return bool True if successful, false otherwise
 */
function updateEventStatus($eventId, $status) {
    global $pdo;

    try {
        // Debug information
        error_log("updateEventStatus function called: Event ID = $eventId, Status = $status, User ID = " . $_SESSION['user_id']);

        // Validate status
        if ($status !== 'done' && $status !== 'pending') {
            error_log("Invalid status value: $status");
            return false;
        }

        // Check if the table exists
        $tableExists = false;
        $checkTable = $pdo->query("SHOW TABLES LIKE 'calendar_events'");
        if ($checkTable && $checkTable->rowCount() > 0) {
            $tableExists = true;
        }

        // If table doesn't exist, create it
        if (!$tableExists) {
            error_log("Calendar events table does not exist, creating it");
            $createTable = "CREATE TABLE calendar_events (
                event_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                event_date DATE NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                course VARCHAR(100),
                course_id INT,
                item_id INT,
                status ENUM('pending', 'done') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";

            if (!$pdo->query($createTable)) {
                error_log("Error creating calendar_events table");
                return false;
            }
        }

        // Check if the status column exists
        $checkColumn = $pdo->query("SHOW COLUMNS FROM calendar_events LIKE 'status'");
        if ($checkColumn && $checkColumn->rowCount() == 0) {
            error_log("Status column does not exist, adding it");
            $addColumn = "ALTER TABLE calendar_events ADD COLUMN status ENUM('pending', 'done') DEFAULT 'pending'";
            $pdo->query($addColumn);
        }

        // Only allow updating events created by the current user
        $sql = "UPDATE calendar_events
                SET status = :status
                WHERE event_id = :event_id AND user_id = :user_id";
        $stmt = $pdo->prepare($sql);

        $stmt->execute([
            ':status' => $status,
            ':event_id' => $eventId,
            ':user_id' => $_SESSION['user_id']
        ]);

        $rowsAffected = $stmt->rowCount();
        error_log("Rows affected by update: $rowsAffected");

        if ($rowsAffected > 0) {
            // Get event details for the notification
            $sql = "SELECT title, event_date FROM calendar_events WHERE event_id = :event_id";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([':event_id' => $eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($event) {
                // Create notification for the status change
                $statusText = ($status === 'done') ? 'completed' : 'marked as pending';
                $notificationTitle = "Event Status Updated";
                $notificationMessage = "You have $statusText the event: " . $event['title'] . " on " .
                                      date('F j, Y', strtotime($event['event_date']));
                createNotification($_SESSION['user_id'], $notificationTitle, $notificationMessage, 'status_change', $eventId);
            }
        }

        return $rowsAffected > 0;
    } catch (PDOException $e) {
        // Log the error
        error_log("Error updating event status: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark an activity as done for a specific user
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @return bool True if successful, false otherwise
 */
function markActivityAsDone($activityId, $userId) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("
            SELECT a.*, c.course_id
            FROM activities a
            JOIN courses c ON a.course_id = c.course_id
            WHERE a.activity_id = :activityId AND a.is_published = 1
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            error_log("Activity not found or not published: ID = $activityId");
            return false;
        }

        $activity = $stmt->fetch();

        // Check if the user is enrolled in the course
        $stmt = $pdo->prepare("
            SELECT enrollment_id
            FROM enrollments
            WHERE user_id = :userId AND course_id = :courseId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $activity['course_id']);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            error_log("User not enrolled in course: User ID = $userId, Course ID = " . $activity['course_id']);
            return false;
        }

        // Check if there's already a submission
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM assignment_submissions
            WHERE activity_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Already marked as done
            error_log("Activity already marked as done: ID = $activityId, User ID = $userId");
            return true;
        }

        // Create a submission record to mark it as done
        $stmt = $pdo->prepare("
            INSERT INTO assignment_submissions (
                activity_id, user_id, content, submission_date, is_late
            ) VALUES (
                :activityId, :userId, 'Marked as done via calendar', NOW(),
                CASE WHEN :dueDate IS NOT NULL AND NOW() > :dueDate THEN 1 ELSE 0 END
            )
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':dueDate', $activity['due_date']);
        $stmt->execute();

        // Create a notification
        $notificationTitle = "Activity Marked as Done";
        $notificationMessage = "You marked the activity '" . $activity['title'] . "' as done.";
        createNotification($userId, $notificationTitle, $notificationMessage, 'status_change', $activityId);

        return true;
    } catch (PDOException $e) {
        error_log("Error marking activity as done: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark an activity as pending for a specific user
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @return bool True if successful, false otherwise
 */
function markActivityAsPending($activityId, $userId) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("
            SELECT a.*, c.course_id
            FROM activities a
            JOIN courses c ON a.course_id = c.course_id
            WHERE a.activity_id = :activityId AND a.is_published = 1
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            error_log("Activity not found or not published: ID = $activityId");
            return false;
        }

        $activity = $stmt->fetch();

        // Check if the user is enrolled in the course
        $stmt = $pdo->prepare("
            SELECT enrollment_id
            FROM enrollments
            WHERE user_id = :userId AND course_id = :courseId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':courseId', $activity['course_id']);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            error_log("User not enrolled in course: User ID = $userId, Course ID = " . $activity['course_id']);
            return false;
        }

        // Delete any existing submissions
        $stmt = $pdo->prepare("
            DELETE FROM assignment_submissions
            WHERE activity_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        // Create a notification
        $notificationTitle = "Activity Marked as Pending";
        $notificationMessage = "You marked the activity '" . $activity['title'] . "' as pending.";
        createNotification($userId, $notificationTitle, $notificationMessage, 'status_change', $activityId);

        return true;
    } catch (PDOException $e) {
        error_log("Error marking activity as pending: " . $e->getMessage());
        return false;
    }
}