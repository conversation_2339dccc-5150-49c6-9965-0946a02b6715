<?php
// Include configuration file
require_once '../includes/config.php';
require_once '../includes/utility_functions.php';
require_once '../includes/submission_functions.php';

// Check if the user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'You must be logged in to perform this action.']);
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Check if the required parameters are provided
if (!isset($_POST['activity_id']) || !isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters.']);
    exit;
}

$activityId = intval($_POST['activity_id']);
$action = $_POST['action'];
$userId = $_SESSION['user_id'];

// Validate the activity ID
$activity = getActivityById($activityId);
if (is_string($activity)) {
    echo json_encode(['success' => false, 'message' => 'Activity not found.']);
    exit;
}

// Check if the user is a student
if (!isStudent()) {
    echo json_encode(['success' => false, 'message' => 'Only students can perform this action.']);
    exit;
}

// Check if the user is enrolled in the course
if (!isEnrolled($userId, $activity['course_id'])) {
    echo json_encode(['success' => false, 'message' => 'You are not enrolled in this course.']);
    exit;
}

// Handle the action
if ($action === 'mark_done') {
    // Check if the student has already submitted this activity
    $existingSubmission = getStudentActivitySubmission($activityId, $userId);
    if (!is_string($existingSubmission)) {
        echo json_encode(['success' => false, 'message' => 'You have already submitted this activity.']);
        exit;
    }

    // Create a new submission
    $submissionData = [
        'activity_id' => $activityId,
        'user_id' => $userId,
        'submission_date' => date('Y-m-d H:i:s'),
        'is_late' => false,
        'grade' => null,
        'feedback' => null,
        'file_path' => null,
        'file_name' => null,
        'submission_link' => null,
        'submission_text' => 'Marked as done'
    ];

    // Check if the submission is late
    if (!empty($activity['due_date'])) {
        $dueDate = new DateTime($activity['due_date']);
        $now = new DateTime();
        if ($now > $dueDate) {
            $submissionData['is_late'] = true;
        }
    }

    // Insert the submission
    $result = createActivitySubmission($submissionData);
    if (is_string($result)) {
        echo json_encode(['success' => false, 'message' => $result]);
        exit;
    }

    echo json_encode(['success' => true, 'message' => 'Activity marked as done.']);
    exit;
} elseif ($action === 'unsubmit') {
    // Check if the student has submitted this activity
    $existingSubmission = getStudentActivitySubmission($activityId, $userId);
    if (is_string($existingSubmission)) {
        echo json_encode(['success' => false, 'message' => 'You have not submitted this activity.']);
        exit;
    }

    // Delete the submission
    $result = deleteActivitySubmission($existingSubmission['submission_id']);
    if (is_string($result)) {
        echo json_encode(['success' => false, 'message' => $result]);
        exit;
    }

    echo json_encode(['success' => true, 'message' => 'Activity unsubmitted.']);
    exit;
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid action.']);
    exit;
}
?>
