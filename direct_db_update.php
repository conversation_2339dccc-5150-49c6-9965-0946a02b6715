<?php
/**
 * Direct Database Update Script
 *
 * This script adds the is_archived column to the enrollments, course_instructors, and courses tables
 * without requiring admin login.
 */

// Include configuration file
require_once 'includes/config.php';

// Set page title
$page_title = "Direct Database Update";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h1>Direct Database Update: Archive Functionality</h1>
        </div>
        <div class="card-body">
            <?php
            try {
                // Check if the is_archived column exists in the enrollments table
                $stmt = $pdo->prepare("SHOW COLUMNS FROM enrollments LIKE 'is_archived'");
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // Column doesn't exist, add it
                    $stmt = $pdo->prepare("ALTER TABLE enrollments ADD COLUMN is_archived TINYINT(1) NOT NULL DEFAULT 0");
                    $stmt->execute();
                    echo "<div class='alert alert-success'>Added is_archived column to enrollments table.</div>";
                } else {
                    echo "<div class='alert alert-info'>is_archived column already exists in enrollments table.</div>";
                }
                
                // Check if the is_archived column exists in the course_instructors table
                $stmt = $pdo->prepare("SHOW COLUMNS FROM course_instructors LIKE 'is_archived'");
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // Column doesn't exist, add it
                    $stmt = $pdo->prepare("ALTER TABLE course_instructors ADD COLUMN is_archived TINYINT(1) NOT NULL DEFAULT 0");
                    $stmt->execute();
                    echo "<div class='alert alert-success'>Added is_archived column to course_instructors table.</div>";
                } else {
                    echo "<div class='alert alert-info'>is_archived column already exists in course_instructors table.</div>";
                }
                
                // Check if the is_archived column exists in the courses table
                $stmt = $pdo->prepare("SHOW COLUMNS FROM courses LIKE 'is_archived'");
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // Column doesn't exist, add it
                    $stmt = $pdo->prepare("ALTER TABLE courses ADD COLUMN is_archived TINYINT(1) NOT NULL DEFAULT 0");
                    $stmt->execute();
                    echo "<div class='alert alert-success'>Added is_archived column to courses table.</div>";
                } else {
                    echo "<div class='alert alert-info'>is_archived column already exists in courses table.</div>";
                }
                
                echo "<div class='alert alert-success mt-3'>Database update completed successfully.</div>";
            } catch (PDOException $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
            
            <div class="mt-4">
                <h4>What's New?</h4>
                <p>This update adds archive functionality to the e-learning system:</p>
                <ul>
                    <li>Students and instructors can now archive courses</li>
                    <li>Completed courses are automatically archived</li>
                    <li>Archived courses can be accessed from the Archive section in the sidebar</li>
                    <li>Archived courses can be restored at any time</li>
                </ul>
            </div>
        </div>
        <div class="card-footer">
            <a href="index.php" class="btn btn-primary">Return to Dashboard</a>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
