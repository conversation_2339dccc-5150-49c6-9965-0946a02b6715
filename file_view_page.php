<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    $error = "File not found. Please contact the administrator.";
}

// Get file information
$fileInfo = pathinfo($filePath);
$fileName = isset($fileInfo['basename']) ? $fileInfo['basename'] : '';
$fileExtension = isset($fileInfo['extension']) ? strtolower($fileInfo['extension']) : '';

// Set page title
$page_title = "View File: " . $fileName;

// Include header
require_once 'includes/header.php';

// Determine file type for display
$fileType = 'unknown';
if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'svg'])) {
    $fileType = 'image';
} elseif ($fileExtension == 'pdf') {
    $fileType = 'pdf';
} elseif (in_array($fileExtension, ['txt', 'html', 'css', 'js', 'json', 'xml'])) {
    $fileType = 'text';
} elseif (in_array($fileExtension, ['doc', 'docx'])) {
    $fileType = 'word';
} elseif (in_array($fileExtension, ['xls', 'xlsx'])) {
    $fileType = 'excel';
} elseif (in_array($fileExtension, ['ppt', 'pptx'])) {
    $fileType = 'powerpoint';
}
?>

<div class="container mt-4">
    <div class="mb-3">
        <?php
        // Get the referer URL if available
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

        // Check if referer is from our site
        $isFromOurSite = !empty($referer) && (strpos($referer, $_SERVER['HTTP_HOST']) !== false);

        // If we have a valid referer, use it; otherwise, go to home
        $backUrl = $isFromOurSite ? $referer : 'index.php';
        ?>
        <a href="<?php echo htmlspecialchars($backUrl); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left mr-2"></i> Back
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-file mr-2"></i> <?php echo htmlspecialchars($fileName); ?>
            </h5>
            <?php if ($fileType == 'word' || $fileType == 'excel' || $fileType == 'powerpoint'): ?>
                <a href="file_preview.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-sm btn-primary" target="_blank">
                    <i class="fas fa-external-link-alt mr-1"></i> Open in New Tab
                </a>
            <?php else: ?>
                <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-sm btn-primary" target="_blank">
                    <i class="fas fa-external-link-alt mr-1"></i> Open in New Tab
                </a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
            <?php else: ?>
                <?php if ($fileType == 'image'): ?>
                    <div class="text-center">
                        <img src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="img-fluid" alt="<?php echo htmlspecialchars($fileName); ?>">
                    </div>
                <?php elseif ($fileType == 'pdf'): ?>
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" allowfullscreen></iframe>
                    </div>
                <?php elseif ($fileType == 'text'): ?>
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="file_viewer.php?file=<?php echo urlencode($filePath); ?>"></iframe>
                    </div>
                <?php elseif ($fileType == 'word' || $fileType == 'excel' || $fileType == 'powerpoint'): ?>
                    <div class="text-center p-4">
                        <div class="mb-4">
                            <i class="fas fa-file-<?php
                                if ($fileType == 'word') echo 'word';
                                elseif ($fileType == 'excel') echo 'excel';
                                elseif ($fileType == 'powerpoint') echo 'powerpoint';
                            ?> fa-4x text-primary mb-3"></i>
                            <h4><?php echo htmlspecialchars($fileName); ?></h4>
                        </div>

                        <div class="alert alert-info">
                            <p><strong>Office Document Preview</strong></p>
                            <p>This file is a Microsoft Office document. You have the following options to view it:</p>
                        </div>

                        <div class="mt-4">
                            <div class="d-flex justify-content-center mt-3">
                                <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-primary mx-2">
                                    <i class="fas fa-download mr-2"></i> Download File
                                </a>
                                <a href="office_file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-outline-primary mx-2" target="_blank">
                                    <i class="fas fa-eye mr-2"></i> Try Direct View
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <p>This file type (<?php echo htmlspecialchars($fileExtension); ?>) cannot be previewed directly in the browser.</p>
                        <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download mr-2"></i> Download File
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
