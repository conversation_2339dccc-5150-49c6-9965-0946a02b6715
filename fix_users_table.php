<?php
require_once 'includes/config.php';

echo "<h1>Fixing Users Table Structure</h1>";

try {
    // Check if users table exists
    $stmt = $pdo->query('SHOW TABLES LIKE "users"');
    if ($stmt->rowCount() == 0) {
        echo "<p>Users table does not exist! Creating it...</p>";
        
        // Create users table
        $pdo->exec("
            CREATE TABLE users (
                user_id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) NOT NULL UNIQUE,
                first_name VARCHAR(50) NOT NULL,
                last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
                gender ENUM('male', 'female', 'other') DEFAULT NULL,
                birthday DATE DEFAULT NULL,
                phone_number VARCHAR(20) DEFAULT NULL,
                profile_picture VARCHAR(255) DEFAULT NULL,
                role_id INT NOT NULL DEFAULT 3,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL DEFAULT NULL,
                FOREIGN KEY (role_id) REFERENCES roles(role_id)
            )
        ");
        echo "<p>Users table created successfully!</p>";
    } else {
        echo "<p>Users table exists. Checking structure...</p>";
        
        // Check current structure
        $stmt = $pdo->query('DESCRIBE users');
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasRoleId = false;
        $hasUserId = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'role_id') {
                $hasRoleId = true;
            }
            if ($column['Field'] === 'user_id') {
                $hasUserId = true;
            }
        }
        
        // Check if we need to add role_id column
        if (!$hasRoleId) {
            echo "<p>Adding role_id column...</p>";
            try {
                $pdo->exec("ALTER TABLE users ADD COLUMN role_id INT NOT NULL DEFAULT 3");
                echo "<p>role_id column added successfully!</p>";
            } catch (PDOException $e) {
                echo "<p>Error adding role_id column: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>role_id column already exists.</p>";
        }
        
        // Check if we need to rename id to user_id
        if (!$hasUserId) {
            $hasId = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'id') {
                    $hasId = true;
                    break;
                }
            }
            
            if ($hasId) {
                echo "<p>Renaming 'id' column to 'user_id'...</p>";
                try {
                    $pdo->exec("ALTER TABLE users CHANGE id user_id INT AUTO_INCREMENT");
                    echo "<p>Column renamed successfully!</p>";
                } catch (PDOException $e) {
                    echo "<p>Error renaming column: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        // Try to add foreign key constraint if it doesn't exist
        try {
            $stmt = $pdo->query("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'role_id' 
                AND REFERENCED_TABLE_NAME = 'roles'
            ");
            
            if ($stmt->rowCount() == 0) {
                echo "<p>Adding foreign key constraint...</p>";
                $pdo->exec("ALTER TABLE users ADD CONSTRAINT fk_users_role_id FOREIGN KEY (role_id) REFERENCES roles(role_id)");
                echo "<p>Foreign key constraint added successfully!</p>";
            } else {
                echo "<p>Foreign key constraint already exists.</p>";
            }
        } catch (PDOException $e) {
            echo "<p>Note: Could not add foreign key constraint: " . $e->getMessage() . "</p>";
            echo "<p>This might be because there are existing users with invalid role_id values.</p>";
        }
    }
    
    // Show final table structure
    echo "<h2>Final Users Table Structure</h2>";
    $stmt = $pdo->query('DESCRIBE users');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Table structure has been fixed! You can now try creating users again.</strong></p>";
    echo "<p><a href='users.php'>Go back to User Management</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
