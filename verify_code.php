<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/email_helper.php';

// Check if user is already in the recovery process
if (!isset($_SESSION['recovery_email'])) {
    header("location: forgot_account.php");
    exit;
}

$email = $_SESSION['recovery_email'];

// Initialize variables
$code = "";
$code_err = "";
$error_msg = "";
$success_msg = "";

// Check if there's an error from resend_code.php
if (isset($_GET['error']) && $_GET['error'] == '1') {
    $error_msg = "Failed to resend verification code. Please try again.";
}

// Check if code was resent successfully
if (isset($_GET['resent']) && $_GET['resent'] == '1') {
    $success_msg = "A new verification code has been sent to your email.";
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if verification code is empty
    if (empty(trim($_POST["code"]))) {
        $code_err = "Please enter the verification code.";
    } else {
        $code = trim($_POST["code"]);
        // Basic validation - must be 6 digits
        if (!preg_match('/^\d{6}$/', $code)) {
            $code_err = "Verification code must be 6 digits.";
        }
    }

    // If no errors, verify the code
    if (empty($code_err)) {
        global $pdo;
        try {
            // Get user ID from email
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = :email");
            $stmt->bindParam(':email', $email);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                $userId = $user['user_id'];

                // Check if the verification code is valid
                $stmt = $pdo->prepare("
                    SELECT * FROM password_reset
                    WHERE user_id = :userId
                    AND reset_code = :code
                    AND expiry_time > NOW()
                ");
                $stmt->bindParam(':userId', $userId);
                $stmt->bindParam(':code', $code);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    // Code is valid, allow user to reset password
                    $_SESSION['reset_user_id'] = $userId;
                    header("location: reset_password.php");
                    exit;
                } else {
                    $error_msg = "Invalid or expired verification code. Please try again.";
                }
            } else {
                $error_msg = "An error occurred. Please try again.";
            }
        } catch (PDOException $e) {
            $error_msg = "An error occurred. Please try again later.";
            // Log the error for administrators
            error_log("Verification code error: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Code - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/AdobeStock_271791778.jpeg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .verify-container {
            width: 448px;
            padding: 48px 40px 36px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .verify-logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .verify-logo h1 {
            color: var(--primary-color);
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            font-size: 28px;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .verify-logo h1 i {
            margin-right: 12px;
            font-size: 32px;
        }

        .verify-logo p {
            color: var(--text-secondary);
            margin-top: 12px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-control {
            height: 56px;
            padding: 16px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            letter-spacing: 8px;
            font-weight: bold;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.25);
            transform: translateY(-2px);
        }

        .btn-submit {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:active {
            transform: translateY(1px);
        }

        .alert {
            margin-bottom: 24px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .text-center a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .text-center a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .back-to-forgot {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-family: 'Google Sans', sans-serif;
            font-size: 16px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .back-to-forgot i {
            margin-right: 8px;
        }

        .back-to-forgot:hover {
            transform: translateX(-5px);
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <a href="forgot_account.php" class="back-to-forgot">
        <i class="fas fa-arrow-left"></i> Back
    </a>
    <div class="verify-container">
        <div class="verify-logo">
            <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
            <p>Verify Your Email</p>
            <small class="text-muted">Enter the 6-digit code sent to <?php echo htmlspecialchars(maskEmail($email)); ?></small>
        </div>

        <?php
        // For development purposes only - show the verification code
        if (DEVELOPMENT_MODE && isset($_SESSION['dev_verification_code'])) {
            echo '<div class="alert alert-info">
                <strong>Development Mode:</strong> Your verification code is:
                <span style="font-size: 1.2em; font-weight: bold; letter-spacing: 3px;">' . $_SESSION['dev_verification_code'] . '</span>
                <br><small class="text-muted">(This message is only shown in development mode)</small>
                <br><small class="text-muted">Note: Actual email sending may have failed, but you can still use this code for testing.</small>
            </div>';
        }

        if (!empty($error_msg)) {
            echo '<div class="alert alert-danger">' . $error_msg . '</div>';
        }

        if (!empty($success_msg)) {
            echo '<div class="alert alert-success">' . $success_msg . '</div>';
        }
        ?>

        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <input type="text" name="code" placeholder="000000" class="form-control <?php echo (!empty($code_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $code; ?>" maxlength="6" inputmode="numeric" pattern="\d{6}" autocomplete="one-time-code">
                <span class="invalid-feedback"><?php echo $code_err; ?></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-submit">Verify Code</button>
            </div>
            <div class="text-center mt-3">
                <p>Didn't receive the code? <a href="resend_code.php">Resend code</a></p>
            </div>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
