<?php
// Include configuration file
require_once 'includes/config.php';

// Check if activity ID is provided
if (isset($_GET['id'])) {
    $activityId = intval($_GET['id']);
    
    try {
        // Get the activity type
        $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $activity = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<h2>Activity Details</h2>";
            echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
            echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
            echo "<p><strong>Activity Type:</strong> " . htmlspecialchars($activity['activity_type']) . "</p>";
            
            // Show a link to update the activity type
            echo "<p><a href='update_activity_type.php?id=" . $activity['activity_id'] . "'>Update Activity Type</a></p>";
        } else {
            echo "<p>Activity not found.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>No activity ID provided.</p>";
}

// Show all activity types in the database
try {
    echo "<h2>All Activity Types in Database</h2>";
    
    // Get the activity_type column definition
    $stmt = $pdo->query("SHOW COLUMNS FROM activities LIKE 'activity_type'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($column) {
        $type = $column['Type'];
        echo "<p>activity_type column definition: " . htmlspecialchars($type) . "</p>";
    }
    
    // Get all distinct activity types
    $stmt = $pdo->query("SELECT DISTINCT activity_type FROM activities");
    $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($types) > 0) {
        echo "<p>Activity types in use:</p>";
        echo "<ul>";
        foreach ($types as $type) {
            echo "<li>" . htmlspecialchars($type) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No activity types found.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
