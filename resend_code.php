<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/email_helper.php';

// Check if user is already in the recovery process
if (!isset($_SESSION['recovery_email'])) {
    header("location: forgot_account.php");
    exit;
}

$email = $_SESSION['recovery_email'];

try {
    global $pdo;

    // Get user information from email
    $stmt = $pdo->prepare("SELECT user_id, username FROM users WHERE email = :email");
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        $userId = $user['user_id'];
        $username = $user['username'];

        // Generate a new verification code
        $verificationCode = generateVerificationCode();

        // Update the verification code in the database
        $expiryTime = date('Y-m-d H:i:s', strtotime('+24 hours')); // Code expires in 24 hours

        // Check if a reset code already exists for this user
        $stmt = $pdo->prepare("SELECT * FROM password_reset WHERE user_id = :userId");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Update existing record
            $stmt = $pdo->prepare("UPDATE password_reset SET reset_code = :code, expiry_time = :expiryTime WHERE user_id = :userId");
        } else {
            // Insert new record
            $stmt = $pdo->prepare("INSERT INTO password_reset (user_id, reset_code, expiry_time) VALUES (:userId, :code, :expiryTime)");
        }

        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':code', $verificationCode);
        $stmt->bindParam(':expiryTime', $expiryTime);
        $stmt->execute();

        // Store verification code in session for development purposes
        $_SESSION['dev_verification_code'] = $verificationCode;

        // Try to send email with verification code
        $emailSent = sendVerificationCodeEmail($email, $username, $verificationCode);

        // In development mode, we'll always proceed even if email sending fails
        if (!$emailSent && DEVELOPMENT_MODE) {
            // Log the issue but continue
            error_log("Email sending failed in development mode. Proceeding anyway.");
        }

        // Redirect back to verification page with success parameter
        header("location: verify_code.php?resent=1");
        exit;
    } else {
        // This shouldn't happen as we already verified the email exists
        header("location: forgot_account.php");
        exit;
    }
} catch (PDOException $e) {
    // Log the error
    error_log("Resend code error: " . $e->getMessage());
    // Redirect to verification page with error
    header("location: verify_code.php?error=1");
    exit;
}
?>
