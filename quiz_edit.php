<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/quiz_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit quizzes.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists and is a quiz
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

if ($activity['activity_type'] != 'quiz') {
    $_SESSION['error'] = "This activity is not a quiz.";
    header("location: activity_view.php?id=$activityId");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this quiz
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit quizzes for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Get existing questions
$questions = getQuizQuestions($activityId);
if (is_string($questions)) {
    $questions = [];
}

// Initialize variables
$title = $activity['title'];
$description = $activity['description'];
$points = $activity['points'];
$dueDate = $activity['due_date'];
$isPublished = $activity['is_published'];
$allowLateSubmissions = isset($activity['allow_late_submissions']) ? $activity['allow_late_submissions'] : false;
$title_err = $description_err = $dueDate_err = "";
$success = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check which form was submitted
    if (isset($_POST['update_quiz'])) {
        // Validate title
        if (empty(trim($_POST["title"]))) {
            $title_err = "Please enter a title for the quiz.";
        } else {
            $title = trim($_POST["title"]);
        }

        // Validate description
        if (empty(trim($_POST["description"]))) {
            $description_err = "Please enter a description for the quiz.";
        } else {
            $description = trim($_POST["description"]);
        }

        // Set points to 0 (removed from UI)
        $points = 0;

        // Validate due date
        if (empty($_POST["due_date"])) {
            // Due date is optional
            $dueDate = null;
        } else {
            // Format the date for MySQL
            $dueDate = date("Y-m-d H:i:s", strtotime($_POST["due_date"]));
        }

        // Check if the quiz should be published
        $isPublished = isset($_POST["is_published"]) ? true : false;

        // Check if late submissions are allowed
        $allowLateSubmissions = isset($_POST["allow_late_submissions"]) ? true : false;

        // Check input errors before updating the quiz
        if (empty($title_err) && empty($description_err) && empty($dueDate_err)) {
            // Update the quiz
            $result = updateActivity($activityId, $title, $description, $points, $dueDate, $isPublished, $allowLateSubmissions);

            // Debug information
            if ($result !== true) {
                error_log("Error updating quiz: " . $result);
            }

            if ($result === true) {
                // Quiz updated successfully
                $success = "Quiz updated successfully!";

                // Refresh quiz data
                $activity = getActivityById($activityId);
                if (is_string($activity)) {
                    $_SESSION['error'] = $activity;
                    header("location: index.php");
                    exit;
                }
            } else {
                // Error occurred
                $error = $result;
            }
        }
    } elseif (isset($_POST['add_question'])) {
        // Add a new question
        error_log("Adding question to quiz: " . $activityId);
        $questionText = trim($_POST['question_text']);
        $questionType = $_POST['question_type'];
        $points = intval($_POST['points']);
        error_log("Question details: " . $questionText . ", Type: " . $questionType . ", Points: " . $points);

        if (empty($questionText)) {
            $error = "Question text cannot be empty.";
        } else {
            error_log("Calling addQuizActivityQuestion with activityId: " . $activityId);
            $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);
            error_log("Result from addQuizActivityQuestion: " . (is_numeric($result) ? "Success, ID: " . $result : "Error: " . $result));

            if (is_numeric($result)) {
                $questionId = $result;
                $success = "Question added successfully.";

                // Handle different question types
                if ($questionType == 'multiple_choice') {
                    // Add multiple choice options
                    $options = array();
                    $correctAnswer = trim($_POST['mc_correct_answer']);

                    // Add options A, B, C, D if they exist
                    if (!empty($_POST['option_a'])) {
                        $optionA = trim($_POST['option_a']);
                        $isCorrect = ($correctAnswer == 'A');
                        $optionResult = addQuizOption($questionId, $optionA, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $error = "Question added but failed to add option A: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_b'])) {
                        $optionB = trim($_POST['option_b']);
                        $isCorrect = ($correctAnswer == 'B');
                        $optionResult = addQuizOption($questionId, $optionB, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $error = "Question added but failed to add option B: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_c'])) {
                        $optionC = trim($_POST['option_c']);
                        $isCorrect = ($correctAnswer == 'C');
                        $optionResult = addQuizOption($questionId, $optionC, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $error = "Question added but failed to add option C: " . $optionResult;
                        }
                    }

                    if (!empty($_POST['option_d'])) {
                        $optionD = trim($_POST['option_d']);
                        $isCorrect = ($correctAnswer == 'D');
                        $optionResult = addQuizOption($questionId, $optionD, $isCorrect);
                        if (!is_numeric($optionResult)) {
                            $error = "Question added but failed to add option D: " . $optionResult;
                        }
                    }

                    $success = "Question and options added successfully.";
                } elseif ($questionType == 'true_false') {
                    // Add true/false options
                    $correctAnswer = $_POST['tf_correct_answer'];

                    // Add True option
                    $isCorrect = ($correctAnswer == 'true');
                    $optionResult = addQuizOption($questionId, 'True', $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add True option: " . $optionResult;
                    }

                    // Add False option
                    $isCorrect = ($correctAnswer == 'false');
                    $optionResult = addQuizOption($questionId, 'False', $isCorrect);
                    if (!is_numeric($optionResult)) {
                        $error = "Question added but failed to add False option: " . $optionResult;
                    }

                    $success = "True/False question added successfully.";
                } elseif ($questionType == 'short_answer') {
                    // Process short answer
                    if (isset($_POST['sa_correct_answer']) && !empty($_POST['sa_correct_answer'])) {
                        $correctAnswer = trim($_POST['sa_correct_answer']);

                        // For short answer, we store the correct answer as an option
                        addQuizOption($questionId, $correctAnswer, true);

                        $success = "Short answer question added successfully.";
                    } else {
                        $error = "Short answer questions require a correct answer.";
                    }
                }

                // Refresh questions
                $questions = getQuizQuestions($activityId);
                if (is_string($questions)) {
                    $questions = [];
                }
            } else {
                $error = $result;
            }
        }
    } elseif (isset($_POST['delete_question']) || isset($_GET['delete_question'])) {
        // Delete a question (handle both POST and GET requests)
        $questionId = isset($_POST['delete_question']) ?
                      intval($_POST['delete_question']) :
                      intval($_GET['delete_question']);

        error_log("Deleting quiz question ID: $questionId");

        // Use the appropriate delete function for quiz questions
        $result = deleteActivityQuizQuestion($questionId);

        error_log("Delete result: " . ($result === true ? "Success" : $result));

        if ($result === true) {
            $_SESSION['success'] = "Question deleted successfully!";
            header("location: quiz_edit.php?id=$activityId#questions");
            exit;
        } else {
            $_SESSION['error'] = $result;
            header("location: quiz_edit.php?id=$activityId#questions");
            exit;
        }
    } elseif (isset($_POST['update_question']) && isset($_POST['question_id'])) {
        // Update a question
        $questionId = intval($_POST['question_id']);
        $questionText = trim($_POST['question_text']);
        $points = intval($_POST['points']);

        if (empty($questionText)) {
            $error = "Question text cannot be empty.";
        } else {
            // Get the question type from the database
            $stmt = $pdo->prepare("SELECT question_type FROM quiz_questions WHERE question_id = :questionId");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();
            $questionType = $stmt->fetchColumn();

            $result = updateActivityQuizQuestion($questionId, $questionText, $points);

            if ($result === true) {
                $success = "Question updated successfully.";

                // Refresh questions
                $questions = getQuizQuestions($activityId);
                if (is_string($questions)) {
                    $questions = [];
                }
            } else {
                $error = $result;
            }
        }
    }
}

// Set page title
$page_title = "Edit Quiz - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($activity['title']); ?></a></li>
                    <li class="breadcrumb-item active">Edit Quiz</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Quiz</h1>
                <a href="?id=<?php echo $activityId; ?>&manage_questions=1" class="btn btn-primary">
                    <i class="fas fa-question-circle"></i> Manage Questions
                </a>
            </div>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <ul class="nav nav-tabs mb-4" id="quizTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="true">Quiz Details</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="questions-tab" data-toggle="tab" href="#questions" role="tab" aria-controls="questions" aria-selected="false">Questions</a>
                </li>
            </ul>

            <div class="tab-content" id="quizTabsContent">
                <!-- Quiz Details Tab -->
                <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                    <div class="card">
                        <div class="card-body">
                            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post">
                                <div class="form-group">
                                    <label for="title">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                                    <span class="invalid-feedback"><?php echo $title_err; ?></span>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description <span class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                                    <span class="invalid-feedback"><?php echo $description_err; ?></span>
                                </div>



                                <div class="form-group">
                                    <label for="due_date">Due Date (optional)</label>
                                    <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo !empty($dueDate) ? date('Y-m-d\TH:i', strtotime($dueDate)) : ''; ?>">
                                    <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                                    <small class="form-text text-muted">Leave blank if there is no due date.</small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" <?php echo $allowLateSubmissions ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                                        <small class="form-text text-muted">If checked, students can submit after the due date, but submissions will be marked as late.</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="is_published">Publish this quiz (visible to students)</label>
                                    </div>
                                </div>

                                <div class="form-group text-right">
                                    <input type="hidden" name="update_quiz" value="1">
                                    <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Update Quiz</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Questions Tab -->
                <div class="tab-pane fade" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                    <div class="text-right mb-3">
                        <button type="button" class="btn btn-primary btn-lg" data-toggle="collapse" data-target="#addQuestionFormContainer">
                            <i class="fas fa-plus"></i> Add New Question
                        </button>
                    </div>

                    <div class="collapse mb-4" id="addQuestionFormContainer">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Add New Question</h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId . '#questions'); ?>" method="post" id="questionAddForm">
                                    <div class="form-group">
                                        <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                        <textarea name="question_text" id="question_text" class="form-control" rows="3" required></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_type">Type:</label>
                                        <select name="question_type" id="question_type" class="form-control" onchange="toggleQuestionOptions()">
                                            <option value="multiple_choice">Multiple Choice</option>
                                            <option value="true_false">True/False</option>
                                            <option value="short_answer">Short Answer</option>
                                        </select>
                                    </div>

                                    <!-- Multiple Choice Options -->
                                    <div id="multiple_choice_options" class="question-options">
                                        <div class="form-group">
                                            <label for="option_a">Option A:</label>
                                            <input type="text" name="option_a" id="option_a" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_b">Option B:</label>
                                            <input type="text" name="option_b" id="option_b" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_c">Option C:</label>
                                            <input type="text" name="option_c" id="option_c" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="option_d">Option D:</label>
                                            <input type="text" name="option_d" id="option_d" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="mc_correct_answer">Correct Answer:</label>
                                            <select name="mc_correct_answer" id="mc_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="A">Option A</option>
                                                <option value="B">Option B</option>
                                                <option value="C">Option C</option>
                                                <option value="D">Option D</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- True/False Options -->
                                    <div id="true_false_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="tf_correct_answer">Correct Answer:</label>
                                            <select name="tf_correct_answer" id="tf_correct_answer" class="form-control">
                                                <option value="">-- Select --</option>
                                                <option value="true">True</option>
                                                <option value="false">False</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Short Answer Options -->
                                    <div id="short_answer_options" class="question-options" style="display: none;">
                                        <div class="form-group">
                                            <label for="sa_correct_answer">Correct Answer:</label>
                                            <input type="text" name="sa_correct_answer" id="sa_correct_answer" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="question_points">Points <span class="text-danger">*</span></label>
                                        <input type="number" name="points" id="question_points" class="form-control" value="1" min="1" required>
                                    </div>

                                    <div class="form-group text-right">
                                        <button type="button" class="btn btn-secondary mr-2" data-toggle="collapse" data-target="#addQuestionFormContainer">Cancel</button>
                                        <input type="hidden" name="add_question" value="1">
                                        <input type="hidden" name="activity_id" value="<?php echo $activityId; ?>">
                                        <button type="submit" class="btn btn-success" id="addQuestionBtn">Add Question</button>
                                    </div>
                                </form>

                                <script>
                                    // Direct form submission without JavaScript validation
                                    document.getElementById('questionAddForm').addEventListener('submit', function(e) {
                                        console.log('Form submitted directly');
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Questions</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <?php if (count($questions) > 0): ?>
                                <?php foreach ($questions as $index => $question): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                        <span class="badge badge-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'warning'); ?> p-2"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                                    <?php
                                    // Find the correct answer for display in the main view
                                    if (isset($question['options']) && count($question['options']) > 0):
                                        $correctAnswer = null;
                                        $correctIndex = null;
                                        foreach ($question['options'] as $optIndex => $opt) {
                                            if ($opt['is_correct']) {
                                                $correctAnswer = $opt['option_text'];
                                                $correctIndex = $optIndex;
                                                break;
                                            }
                                        }

                                        if ($correctAnswer):
                                            if ($question['question_type'] == 'true_false'):
                                    ?>
                                    <div class="d-flex align-items-center mt-2 mb-2">
                                        <div class="mr-2">
                                            <span class="badge badge-info p-2">
                                                <i class="fas fa-<?php echo $correctAnswer == 'True' ? 'check' : 'times'; ?> mr-1"></i>
                                                <?php echo $correctAnswer; ?>
                                            </span>
                                        </div>
                                        <div>
                                            <span class="badge badge-success">Correct Answer</span>
                                        </div>
                                    </div>
                                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                    <div class="alert alert-success mt-2 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success mr-2"></i>
                                            <strong>Correct Answer:</strong>
                                            <span class="ml-2 p-1 bg-light rounded"><?php echo htmlspecialchars($correctAnswer); ?></span>
                                        </div>
                                    </div>
                                    <?php
                                            endif;
                                        endif;
                                    endif;
                                    ?>

                                    <?php if (isset($question['options']) && count($question['options']) > 0): ?>
                                    <div class="card mb-3 ml-4 border-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'primary'); ?>">
                                        <div class="card-header bg-<?php echo $question['question_type'] == 'multiple_choice' ? 'primary' : ($question['question_type'] == 'true_false' ? 'info' : 'primary'); ?> text-white d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <?php if ($question['question_type'] == 'true_false'): ?>
                                                    True/False Options
                                                <?php else: ?>
                                                    Answer Options
                                                <?php endif; ?>
                                            </h6>
                                            <?php
                                            // Find the correct option
                                            $correctOption = null;
                                            foreach ($question['options'] as $optIndex => $opt) {
                                                if ($opt['is_correct']) {
                                                    $correctOption = $opt;
                                                    $correctIndex = $optIndex;
                                                    break;
                                                }
                                            }

                                            if ($correctOption && $question['question_type'] == 'multiple_choice'):
                                            ?>
                                            <div class="correct-answer-summary">
                                                <span class="badge badge-light">Correct: Option <?php echo chr(65 + $correctIndex); ?></span>
                                            </div>
                                            <?php elseif ($correctOption && $question['question_type'] == 'true_false'): ?>
                                            <div class="correct-answer-summary">
                                                <span class="badge badge-light">Correct Answer: <?php echo $correctOption['option_text']; ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option): ?>
                                            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-success-light' : ''; ?>">
                                                <div class="d-flex align-items-center">
                                                    <?php if ($question['question_type'] == 'multiple_choice'): ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo $option['is_correct'] ? 'success' : 'secondary'; ?> option-badge"><?php echo chr(65 + $optionIndex); ?></span>
                                                        </div>
                                                    <?php elseif ($question['question_type'] == 'true_false'): ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo $option['is_correct'] ? 'success' : 'secondary'; ?> option-badge"><?php echo $option['option_text'] == 'True' ? 'T' : 'F'; ?></span>
                                                        </div>
                                                        <div class="tf-icon mr-2">
                                                            <i class="fas fa-<?php echo $option['option_text'] == 'True' ? 'check text-success' : 'times text-danger'; ?>"></i>
                                                        </div>
                                                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                                        <i class="far fa-edit mr-3"></i>
                                                    <?php endif; ?>

                                                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold text-success' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>

                                                    <?php if ($option['is_correct']): ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success"><i class="fas fa-check mr-1"></i> Correct Answer</span>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                    <?php
                                    // Find the correct answer for short answer questions
                                    $correctAnswer = "";
                                    if (isset($question['options']) && count($question['options']) > 0) {
                                        foreach ($question['options'] as $opt) {
                                            if ($opt['is_correct']) {
                                                $correctAnswer = $opt['option_text'];
                                                break;
                                            }
                                        }
                                    }
                                    ?>
                                    <div class="card mb-3 ml-4 border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">Short Answer Question</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (!empty($correctAnswer)): ?>
                                            <div class="alert alert-success mb-0">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success mr-2"></i>
                                                    <strong>Correct Answer:</strong>
                                                    <span class="ml-2 p-1 bg-light rounded font-weight-bold"><?php echo htmlspecialchars($correctAnswer); ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="btn-group">
                                        <a href="activity_question_edit.php?id=<?php echo $question['question_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger delete-question" data-question-id="<?php echo $question['question_id']; ?>">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="list-group-item text-center py-4">
                                    <p class="text-muted mb-0">No questions added yet. Add questions above.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<style>
/* Enhanced tab styling */
.nav-tabs .nav-link {
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 0;
    border-top: 3px solid transparent;
}

.nav-tabs .nav-link.active {
    border-top: 3px solid #007bff;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-top: 3px solid #cce5ff;
    background-color: #f8f9fa;
}

/* Question styling */
.list-group-item {
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.option-container {
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-container:hover {
    background-color: #f0f0f0;
}

/* Enhanced styling for correct answers */
.bg-success-light {
    background-color: rgba(40, 167, 69, 0.1);
}

.option-badge {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.border-primary {
    border-color: #007bff !important;
}

.correct-answer-summary .badge {
    font-size: 14px;
    padding: 6px 10px;
}

.text-success {
    color: #28a745 !important;
}
</style>

<script>
function toggleQuestionOptions() {
    var questionType = document.getElementById('question_type').value;
    var multipleChoiceOptions = document.getElementById('multiple_choice_options');
    var trueFalseOptions = document.getElementById('true_false_options');
    var shortAnswerOptions = document.getElementById('short_answer_options');

    // Hide all options first
    multipleChoiceOptions.style.display = 'none';
    trueFalseOptions.style.display = 'none';
    shortAnswerOptions.style.display = 'none';

    // Show the appropriate options based on question type
    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
        // Make option A and B required for multiple choice
        document.getElementById('option_a').setAttribute('required', 'required');
        document.getElementById('option_b').setAttribute('required', 'required');
        document.getElementById('mc_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'true_false') {
        trueFalseOptions.style.display = 'block';
        document.getElementById('tf_correct_answer').setAttribute('required', 'required');
    } else if (questionType === 'short_answer') {
        shortAnswerOptions.style.display = 'block';
        document.getElementById('sa_correct_answer').setAttribute('required', 'required');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleQuestionOptions();

    // Make sure the tabs are properly initialized
    $('#quizTabs a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Initialize Bootstrap tabs
    $('#quizTabs a[data-toggle="tab"]').tab();

    // Check if there's a hash in the URL to activate the correct tab
    var hash = window.location.hash;
    if (hash) {
        $('#quizTabs a[href="' + hash + '"]').tab('show');
    } else if (window.location.search.includes('tab=questions')) {
        // If there's a tab parameter in the URL, activate that tab
        $('#quizTabs a[href="#questions"]').tab('show');
    }

    // Add hash to URL when tab is clicked
    $('#quizTabs a').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.hash;
    });

    // Make the "Manage Questions" button work with the tabs
    $('a[href="#questions"]').on('click', function(e) {
        e.preventDefault();
        $('#quizTabs a[href="#questions"]').tab('show');
    });

    // Check if the URL has a query parameter to show the questions tab
    if (window.location.href.indexOf('manage_questions=1') > -1) {
        console.log('Showing questions tab');
        // Force the questions tab to be shown
        setTimeout(function() {
            $('#quizTabs a[href="#questions"]').tab('show');
            // Show the add question form automatically
            $('#addQuestionFormContainer').collapse('show');
        }, 100);
    }

    // Comment out the validation to allow direct form submission
    /*
    $('#questionAddForm').on('submit', function(e) {
        console.log('Add Question form submitted');

        // Validate the form
        var form = $(this);
        var questionText = form.find('#question_text').val();
        var questionType = form.find('#question_type').val();
        var activityId = form.find('input[name="activity_id"]').val();

        console.log('Activity ID:', activityId);
        console.log('Question Text:', questionText);
        console.log('Question Type:', questionType);

        // Make sure activity_id is included
        if (!activityId) {
            console.error('Error: activity_id field is missing!');
            alert('Error: activity_id field is missing. Please refresh the page and try again.');
            e.preventDefault();
            return false;
        }

        if (questionType === 'multiple_choice') {
            var optionA = form.find('#option_a').val();
            var optionB = form.find('#option_b').val();
            var correctAnswer = form.find('#mc_correct_answer').val();

            console.log('Option A:', optionA);
            console.log('Option B:', optionB);
            console.log('Correct Answer:', correctAnswer);

            if (!optionA || !optionB || !correctAnswer) {
                alert('Please fill in at least options A and B, and select a correct answer.');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'true_false') {
            var tfCorrectAnswer = form.find('#tf_correct_answer').val();
            console.log('TF Correct Answer:', tfCorrectAnswer);

            if (!tfCorrectAnswer) {
                alert('Please select the correct answer (True or False).');
                e.preventDefault();
                return false;
            }
        } else if (questionType === 'short_answer') {
            var saCorrectAnswer = form.find('#sa_correct_answer').val();
            console.log('SA Correct Answer:', saCorrectAnswer);

            if (!saCorrectAnswer) {
                alert('Please enter the correct answer for the short answer question.');
                e.preventDefault();
                return false;
            }
        }

        // If all validations pass, submit the form
        console.log('Form validation passed, submitting...');
        return true;
    });
    */

    // Add click handler for the Add Question button
    $('#addQuestionBtn').on('click', function() {
        console.log('Add Question button clicked');
        // Make sure the form submits directly
        $('#questionAddForm').submit();
    });

    // Add confirmation and functionality for delete buttons
    $('.delete-question').on('click', function(e) {
        e.preventDefault(); // Prevent the default link behavior

        if (confirm('Are you sure you want to delete this question?')) {
            // Get the question ID from the data attribute
            var questionId = $(this).data('question-id');
            var activityId = <?php echo $activityId; ?>;

            // Create a form and submit it programmatically
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = 'quiz_edit.php?id=' + activityId;

            // Add question ID as a hidden field
            var questionIdInput = document.createElement('input');
            questionIdInput.type = 'hidden';
            questionIdInput.name = 'delete_question';
            questionIdInput.value = questionId;
            form.appendChild(questionIdInput);

            // Add activity ID as a hidden field
            var activityIdInput = document.createElement('input');
            activityIdInput.type = 'hidden';
            activityIdInput.name = 'activity_id';
            activityIdInput.value = activityId;
            form.appendChild(activityIdInput);

            // Append the form to the body and submit it
            document.body.appendChild(form);
            form.submit();
        }
    });
});
</script>
