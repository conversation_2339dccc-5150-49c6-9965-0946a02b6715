<?php
/**
 * Calendar Actions
 *
 * This file handles CRUD operations for calendar events.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/calendar_events.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['error'] = "You must be logged in to perform this action.";
    header('Location: login.php');
    exit;
}

// All logged-in users can add events

// Add new event
if (isset($_POST['add_event'])) {
    $title = trim($_POST['event_title']);
    $date = trim($_POST['event_date']);
    $type = trim($_POST['event_type']);
    $description = trim($_POST['event_description']);

    // Get course ID if provided (for instructors)
    $courseId = null;
    $course = null;

    if (isTeacher() && isset($_POST['event_course']) && !empty($_POST['event_course'])) {
        $courseId = intval($_POST['event_course']);

        // Get course name for display
        global $pdo;
        $stmt = $pdo->prepare("SELECT title, class_code FROM courses WHERE course_id = ?");
        $stmt->execute([$courseId]);
        $courseData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($courseData) {
            $course = $courseData['title'] . ' (' . $courseData['class_code'] . ')';
        }
    }

    // Validate input
    if (empty($title) || empty($date) || empty($type)) {
        $_SESSION['error'] = "Title, date, and type are required.";
        header('Location: calendar.php');
        exit;
    }

    // Set course name based on event type and user role
    if (isAdmin()) {
        if ($type == 'system') {
            $course = 'System';
        } elseif ($type == 'admin' || $type == 'report') {
            $course = 'Admin';
        } elseif ($type == 'meeting' && empty($course)) {
            $course = 'Admin Meeting';
        }
    } elseif (isTeacher()) {
        if ($type == 'meeting' && empty($course)) {
            $course = 'Instructor Meeting';
        } elseif (empty($course)) {
            $course = 'Instructor Event';
        }
    } else {
        // Student events
        $course = 'Student Calendar';
    }

    // Add the event to the database
    if (addCalendarEvent($title, $date, $type, $description, $course, $courseId)) {
        $_SESSION['success'] = "Event added successfully.";
    } else {
        $_SESSION['error'] = "Failed to add event. Please try again.";
    }

    header('Location: calendar.php');
    exit;
}

// Update existing event
if (isset($_POST['update_event'])) {
    $eventId = $_POST['event_id'];
    $title = trim($_POST['event_title']);
    $date = trim($_POST['event_date']);
    $type = trim($_POST['event_type']);
    $description = trim($_POST['event_description']);

    // Validate input
    if (empty($eventId) || empty($title) || empty($date) || empty($type)) {
        $_SESSION['error'] = "Event ID, title, date, and type are required.";
        header('Location: calendar.php');
        exit;
    }

    // Update the event in the database
    if (updateCalendarEvent($eventId, $title, $date, $type, $description)) {
        $_SESSION['success'] = "Event updated successfully.";
    } else {
        $_SESSION['error'] = "Failed to update event. Please ensure you are the owner of this event.";
    }

    header('Location: calendar.php');
    exit;
}

// Delete event
if (isset($_POST['delete_event'])) {
    $eventId = $_POST['event_id'];

    // Validate input
    if (empty($eventId)) {
        $_SESSION['error'] = "Event ID is required.";
        header('Location: calendar.php');
        exit;
    }

    // Delete the event from the database
    if (deleteCalendarEvent($eventId)) {
        $_SESSION['success'] = "Event deleted successfully.";
    } else {
        $_SESSION['error'] = "Failed to delete event. Please ensure you are the owner of this event.";
    }

    header('Location: calendar.php');
    exit;
}

// If we get here, no valid action was specified
$_SESSION['error'] = "Invalid action.";
header('Location: calendar.php');
exit;
