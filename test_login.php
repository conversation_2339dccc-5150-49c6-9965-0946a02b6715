<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Test login with admin credentials
$username = 'admin';
$password = 'admin123';

echo "Testing login with username: $username and password: $password<br>";

$result = loginUser($username, $password);

if ($result === true) {
    echo "Login successful! User is now logged in.<br>";
    echo "Session data:<br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    echo "Username: " . $_SESSION['username'] . "<br>";
    echo "Email: " . $_SESSION['email'] . "<br>";
    echo "First Name: " . $_SESSION['first_name'] . "<br>";
    echo "Last Name: " . $_SESSION['last_name'] . "<br>";
    echo "Role: " . $_SESSION['role'] . "<br>";
} else {
    echo "Login failed: " . $result . "<br>";
}
?>
