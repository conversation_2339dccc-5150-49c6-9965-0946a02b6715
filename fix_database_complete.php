<?php
// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "<br>");
}

echo "<h1>Complete Database Fix Script</h1>";

// Step 1: Check if the roles table exists and get role mappings
$result = $conn->query("SHOW TABLES LIKE 'roles'");
$roles_exist = $result->num_rows > 0;

$role_mappings = [];
if ($roles_exist) {
    $result = $conn->query("SELECT role_id, role_name FROM roles");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $role_mappings[$row['role_id']] = strtolower($row['role_name']);
        }
    }
    echo "Found roles table with " . count($role_mappings) . " roles<br>";
} else {
    echo "Roles table does not exist<br>";
    // Default mappings if roles table doesn't exist
    $role_mappings = [
        1 => 'admin',
        2 => 'teacher',
        3 => 'student'
    ];
}

// Step 2: Check if users table has role_id and/or role columns
$result_role_id = $conn->query("SHOW COLUMNS FROM users LIKE 'role_id'");
$has_role_id = $result_role_id->num_rows > 0;

$result_role = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
$has_role = $result_role->num_rows > 0;

echo "Users table has role_id column: " . ($has_role_id ? "Yes" : "No") . "<br>";
echo "Users table has role column: " . ($has_role ? "Yes" : "No") . "<br>";

// Step 3: Add role column if it doesn't exist
if (!$has_role) {
    $sql = "ALTER TABLE users ADD COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student' AFTER password";
    if ($conn->query($sql) === TRUE) {
        echo "Successfully added role column<br>";
        $has_role = true;
    } else {
        echo "Error adding role column: " . $conn->error . "<br>";
    }
}

// Step 4: If both columns exist, update role based on role_id
if ($has_role && $has_role_id) {
    foreach ($role_mappings as $id => $name) {
        $sql = "UPDATE users SET role = '$name' WHERE role_id = $id";
        if ($conn->query($sql) === TRUE) {
            echo "Updated users with role_id $id to role '$name'<br>";
        } else {
            echo "Error updating role for role_id $id: " . $conn->error . "<br>";
        }
    }
}

// Step 5: Remove foreign key constraint before dropping role_id column
if ($has_role_id) {
    // Get the constraint name
    $result = $conn->query("
        SELECT CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = '" . DB_NAME . "'
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'role_id'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $constraint_name = $row['CONSTRAINT_NAME'];
        
        // Drop the foreign key constraint
        $sql = "ALTER TABLE users DROP FOREIGN KEY `$constraint_name`";
        if ($conn->query($sql) === TRUE) {
            echo "Successfully dropped foreign key constraint: $constraint_name<br>";
        } else {
            echo "Error dropping foreign key constraint: " . $conn->error . "<br>";
        }
    }
    
    // Now drop the role_id column
    $sql = "ALTER TABLE users DROP COLUMN role_id";
    if ($conn->query($sql) === TRUE) {
        echo "Successfully dropped role_id column<br>";
    } else {
        echo "Error dropping role_id column: " . $conn->error . "<br>";
    }
}

// Step 6: Update the register.php file to use the correct column names
$register_file = file_get_contents('register.php');
if ($register_file) {
    // Make sure the SQL query uses the correct column names
    $register_file = preg_replace(
        '/INSERT INTO users \((.*?)\) VALUES \((.*?)\)/',
        'INSERT INTO users (first_name, last_name, username, email, password, role, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
        $register_file
    );
    
    file_put_contents('register.php', $register_file);
    echo "Updated register.php file<br>";
}

// Show the current table structure
$result = $conn->query("SHOW COLUMNS FROM users");
if ($result) {
    echo "<h2>Current Users Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

// Create admin user if it doesn't exist
$stmt = $conn->prepare("SELECT user_id FROM users WHERE username = 'admin'");
if ($stmt) {
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows == 0) {
        // Create admin user
        $admin_password = password_hash("admin123", PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, email, first_name, last_name, role, is_active) 
                VALUES ('admin', '$admin_password', '<EMAIL>', 'Admin', 'User', 'admin', 1)";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p>Admin user created successfully</p>";
        } else {
            echo "<p>Error creating admin user: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Admin user already exists</p>";
    }
    $stmt->close();
}

echo "<p>Database fix complete. You can now <a href='register.php'>register</a> or <a href='login.php'>login</a>.</p>";

$conn->close();
