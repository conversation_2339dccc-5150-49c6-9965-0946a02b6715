<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/settings_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "Only teachers can edit materials.";
    header("location: index.php");
    exit;
}

// Check if material ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Material ID is required.";
    header("location: index.php");
    exit;
}

$materialId = intval($_GET['id']);

// Get material details
$material = getMaterialById($materialId);

// Check if material exists
if (is_string($material)) {
    $_SESSION['error'] = $material;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $material['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized for this course
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit materials for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Initialize variables
$title = $material['title'];
$content = $material['content'];
$isPublished = $material['is_published'];
$title_err = $content_err = "";
$file_err = "";
$success = "";

// Get existing files
$materialFiles = getActivityFiles($materialId);

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate content
    if (empty(trim($_POST["content"]))) {
        $content_err = "Please enter content.";
    } else {
        $content = trim($_POST["content"]);
    }

    // Check if the material should be published
    $isPublished = isset($_POST["is_published"]) ? true : false;

    // Check input errors before updating the material
    if (empty($title_err) && empty($content_err)) {
        // Update the material
        $result = updateActivity($materialId, $title, $content, 0, null, $isPublished);

        if ($result === true) {
            // Handle file uploads if any
            if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
                // Get allowed file types from settings
                $allowedTypes = getAllowedFileTypes();

                // Get max file size from settings
                $maxFileSize = getMaxFileUploadSize();
                $maxFileSizeMB = $maxFileSize / (1024 * 1024);

                // Create uploads directory if it doesn't exist
                $uploadDir = 'uploads/materials/' . $materialId . '/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Process each uploaded file
                $fileCount = count($_FILES['files']['name']);
                for ($i = 0; $i < $fileCount; $i++) {
                    if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = basename($_FILES['files']['name'][$i]);
                        $fileType = $_FILES['files']['type'][$i];
                        $fileSize = $_FILES['files']['size'][$i];
                        $fileTmpName = $_FILES['files']['tmp_name'][$i];

                        // Check file type
                        if (!in_array($fileType, $allowedTypes)) {
                            $file_err = "File type not allowed: " . $fileType;
                            break;
                        }

                        // Check file size
                        if ($fileSize > $maxFileSize) {
                            $file_err = "File size exceeds the maximum allowed size of " . $maxFileSizeMB . "MB.";
                            break;
                        }

                        // Generate a unique filename to prevent overwriting
                        $uniqueFileName = uniqid() . '_' . $fileName;
                        $targetFilePath = $uploadDir . $uniqueFileName;

                        // Move the uploaded file to the target directory
                        if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                            // Save file information to the database
                            try {
                                global $pdo;
                                $stmt = $pdo->prepare("
                                    INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size, uploaded_by)
                                    VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize, :uploadedBy)
                                ");

                                $relativePath = 'uploads/materials/' . $materialId . '/' . $uniqueFileName;

                                $stmt->bindParam(':activityId', $materialId);
                                $stmt->bindParam(':fileName', $fileName);
                                $stmt->bindParam(':filePath', $relativePath);
                                $stmt->bindParam(':fileType', $fileType);
                                $stmt->bindParam(':fileSize', $fileSize);
                                $stmt->bindParam(':uploadedBy', $_SESSION['user_id']);

                                $stmt->execute();
                            } catch (PDOException $e) {
                                $file_err = "Database error: " . $e->getMessage();
                                break;
                            }
                        } else {
                            $file_err = "Failed to upload file: " . $fileName;
                            break;
                        }
                    } elseif ($_FILES['files']['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                        $file_err = "Error uploading file: " . getFileUploadError($_FILES['files']['error'][$i]);
                        break;
                    }
                }
            }

            if (empty($file_err)) {
                // Material updated successfully
                $success = "Material updated successfully!";

                // Refresh material data
                $material = getMaterialById($materialId);
                if (is_string($material)) {
                    $_SESSION['error'] = $material;
                    header("location: index.php");
                    exit;
                }

                // Refresh file list
                $materialFiles = getActivityFiles($materialId);
            }
        } else {
            // Error updating material
            $_SESSION['error'] = $result;
        }
    }
}

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Set page title
$page_title = "Edit Material - " . htmlspecialchars($material['title']);

// Include header
require_once 'includes/header.php';

// Get max file size from settings
$maxFileSizeMB = getMaxFileUploadSize() / (1024 * 1024);

// Get allowed file types description
$allowedTypesDesc = [];
$allowedTypes = getAllowedFileTypes();

if (in_array('image/jpeg', $allowedTypes) || in_array('image/png', $allowedTypes) || in_array('image/gif', $allowedTypes)) {
    $allowedTypesDesc[] = 'images';
}
if (in_array('application/pdf', $allowedTypes)) {
    $allowedTypesDesc[] = 'PDFs';
}
if (in_array('application/msword', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.wordprocessingml.document', $allowedTypes)) {
    $allowedTypesDesc[] = 'Word documents';
}
if (in_array('application/vnd.ms-excel', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', $allowedTypes)) {
    $allowedTypesDesc[] = 'Excel spreadsheets';
}
if (in_array('application/vnd.ms-powerpoint', $allowedTypes) || in_array('application/vnd.openxmlformats-officedocument.presentationml.presentation', $allowedTypes)) {
    $allowedTypesDesc[] = 'PowerPoint presentations';
}
if (in_array('text/plain', $allowedTypes)) {
    $allowedTypesDesc[] = 'text files';
}
if (in_array('application/zip', $allowedTypes) || in_array('application/x-rar-compressed', $allowedTypes)) {
    $allowedTypesDesc[] = 'archives';
}

$allowedTypesText = implode(', ', $allowedTypesDesc);
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="content_view.php?type=material&id=<?php echo $materialId; ?>"><?php echo htmlspecialchars($material['title']); ?></a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>

            <h1 class="mb-4">Edit Material</h1>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <?php if (!empty($file_err)): ?>
                <div class="alert alert-danger"><?php echo $file_err; ?></div>
            <?php endif; ?>

            <!-- Material form -->
            <div class="card">
                <div class="card-body">
                    <form action="material_update_handler.php" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="material_id" value="<?php echo $materialId; ?>">
                        <div class="form-group">
                            <label for="title">Title</label>
                            <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($title); ?>">
                            <span class="invalid-feedback"><?php echo $title_err; ?></span>
                        </div>
                        <div class="form-group">
                            <label for="content">Content</label>
                            <textarea name="content" id="content" rows="10" class="form-control <?php echo (!empty($content_err)) ? 'is-invalid' : ''; ?>"><?php echo htmlspecialchars($content); ?></textarea>
                            <span class="invalid-feedback"><?php echo $content_err; ?></span>
                        </div>

                        <?php if (!empty($materialFiles) && !is_string($materialFiles)): ?>
                        <div class="form-group">
                            <label>Existing Files</label>
                            <div class="list-group">
                                <?php foreach ($materialFiles as $file): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <a href="file_view_page.php?file=<?php echo urlencode($file['file_path']); ?>">
                                        <?php echo htmlspecialchars($file['file_name']); ?>
                                    </a>
                                    <span class="badge badge-primary badge-pill">
                                        <?php echo formatFileSize($file['file_size']); ?>
                                    </span>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="files">Add Files (Optional)</label>
                            <input type="file" name="files[]" id="files" class="form-control-file" multiple>
                            <small class="form-text text-muted">
                                Allowed file types: pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, zip. Maximum size: 10GB per file.
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_published" name="is_published" <?php echo $isPublished ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_published">Publish this material (visible to students)</label>
                            </div>
                        </div>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary">Update Material</button>
                            <a href="content_view.php?type=material&id=<?php echo $materialId; ?>" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
