<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Create Test User</h1>";

try {
    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    $rolesTableExists = $stmt->rowCount() > 0;
    
    if (!$rolesTableExists) {
        echo "<p>Creating roles table...</p>";
        
        // Create roles table
        $pdo->exec("
            CREATE TABLE roles (
                role_id INT PRIMARY KEY AUTO_INCREMENT,
                role_name VARCHAR(50) NOT NULL UNIQUE
            )
        ");
        
        // Insert default roles
        $pdo->exec("INSERT INTO roles (role_id, role_name) VALUES (1, 'admin'), (2, 'teacher'), (3, 'student')");
        
        echo "<p>Roles table created and populated successfully.</p>";
    }
    
    // Get student role ID
    $stmt = $pdo->query("SELECT role_id FROM roles WHERE role_name = 'student'");
    if ($stmt->rowCount() > 0) {
        $role = $stmt->fetch(PDO::FETCH_ASSOC);
        $studentRoleId = $role['role_id'];
    } else {
        $studentRoleId = 3; // Default student role ID
    }
    
    // Create a test user
    $username = "student";
    $password = "student123";
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Check if user already exists
    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        // Update existing user
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $userId = $user['user_id'];
        
        $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :user_id");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        echo "<p>Updated existing user with username: $username</p>";
    } else {
        // Create new user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, first_name, last_name, role_id, is_active) 
            VALUES (:username, :password, :email, :first_name, :last_name, :role_id, :is_active)
        ");
        
        $email = "<EMAIL>";
        $firstName = "Student";
        $lastName = "User";
        $isActive = 1;
        
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':first_name', $firstName);
        $stmt->bindParam(':last_name', $lastName);
        $stmt->bindParam(':role_id', $studentRoleId);
        $stmt->bindParam(':is_active', $isActive);
        
        $stmt->execute();
        
        echo "<p>Created new user with username: $username</p>";
    }
    
    // Create admin user if it doesn't exist
    $adminUsername = "admin";
    $adminPassword = "admin123";
    $adminHashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = :username");
    $stmt->bindParam(':username', $adminUsername);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        // Update existing admin user
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $userId = $user['user_id'];
        
        $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :user_id");
        $stmt->bindParam(':password', $adminHashedPassword);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        echo "<p>Updated existing admin user with username: $adminUsername</p>";
    } else {
        // Get admin role ID
        $stmt = $pdo->query("SELECT role_id FROM roles WHERE role_name = 'admin'");
        if ($stmt->rowCount() > 0) {
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            $adminRoleId = $role['role_id'];
        } else {
            $adminRoleId = 1; // Default admin role ID
        }
        
        // Create new admin user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, first_name, last_name, role_id, is_active) 
            VALUES (:username, :password, :email, :first_name, :last_name, :role_id, :is_active)
        ");
        
        $adminEmail = "<EMAIL>";
        $adminFirstName = "Admin";
        $adminLastName = "User";
        $adminIsActive = 1;
        
        $stmt->bindParam(':username', $adminUsername);
        $stmt->bindParam(':password', $adminHashedPassword);
        $stmt->bindParam(':email', $adminEmail);
        $stmt->bindParam(':first_name', $adminFirstName);
        $stmt->bindParam(':last_name', $adminLastName);
        $stmt->bindParam(':role_id', $adminRoleId);
        $stmt->bindParam(':is_active', $adminIsActive);
        
        $stmt->execute();
        
        echo "<p>Created new admin user with username: $adminUsername</p>";
    }
    
    echo "<h2>Login Credentials</h2>";
    echo "<p>Student User:</p>";
    echo "<ul>";
    echo "<li>Username: $username</li>";
    echo "<li>Password: $password</li>";
    echo "</ul>";
    
    echo "<p>Admin User:</p>";
    echo "<ul>";
    echo "<li>Username: $adminUsername</li>";
    echo "<li>Password: $adminPassword</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
