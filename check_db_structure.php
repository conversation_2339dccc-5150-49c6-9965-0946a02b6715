<?php
// Include configuration file
require_once 'includes/config.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Database Structure Check";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h1>Database Structure Check</h1>
        </div>
        <div class="card-body">
            <h3>Courses Table Structure</h3>
            <?php
            try {
                $stmt = $pdo->query("DESCRIBE courses");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<table class='table table-bordered'>";
                echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                echo "<tbody>";

                $isArchivedExists = false;

                foreach ($columns as $column) {
                    echo "<tr>";
                    foreach ($column as $key => $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";

                    if ($column['Field'] == 'is_archived') {
                        $isArchivedExists = true;
                    }
                }

                echo "</tbody></table>";

                if (!$isArchivedExists) {
                    echo "<div class='alert alert-danger'>The 'is_archived' column does not exist in the courses table!</div>";
                    echo "<a href='update_archive_columns.php' class='btn btn-primary'>Add Missing Columns</a>";
                } else {
                    echo "<div class='alert alert-success'>The 'is_archived' column exists in the courses table.</div>";
                }

                echo "<h3>Enrollments Table Structure</h3>";

                $stmt = $pdo->query("DESCRIBE enrollments");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<table class='table table-bordered'>";
                echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                echo "<tbody>";

                $isArchivedExists = false;

                foreach ($columns as $column) {
                    echo "<tr>";
                    foreach ($column as $key => $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";

                    if ($column['Field'] == 'is_archived') {
                        $isArchivedExists = true;
                    }
                }

                echo "</tbody></table>";

                if (!$isArchivedExists) {
                    echo "<div class='alert alert-danger'>The 'is_archived' column does not exist in the enrollments table!</div>";
                } else {
                    echo "<div class='alert alert-success'>The 'is_archived' column exists in the enrollments table.</div>";
                }

                echo "<h3>Course Instructors Table Structure</h3>";

                $stmt = $pdo->query("DESCRIBE course_instructors");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<table class='table table-bordered'>";
                echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                echo "<tbody>";

                $isArchivedExists = false;

                foreach ($columns as $column) {
                    echo "<tr>";
                    foreach ($column as $key => $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";

                    if ($column['Field'] == 'is_archived') {
                        $isArchivedExists = true;
                    }
                }

                echo "</tbody></table>";

                if (!$isArchivedExists) {
                    echo "<div class='alert alert-danger'>The 'is_archived' column does not exist in the course_instructors table!</div>";
                } else {
                    echo "<div class='alert alert-success'>The 'is_archived' column exists in the course_instructors table.</div>";
                }

            } catch (PDOException $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
        <div class="card-footer">
            <a href="index.php" class="btn btn-primary">Return to Dashboard</a>
            <a href="update_archive_columns.php" class="btn btn-success">Run Update Script</a>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
