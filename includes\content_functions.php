<?php
/**
 * Content Management Functions
 *
 * This file contains functions related to module and lesson management.
 */

require_once 'config.php';
require_once 'course_functions.php';

/**
 * Function to create a new module
 * Admin can create modules for any course, teachers can only create modules for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The module title
 * @param string $description The module description
 * @param int $orderNumber The order number
 * @return bool|string True if creation successful, error message otherwise
 */
function createModule($courseId, $title, $description, $orderNumber) {
    global $pdo;

    try {
        // Check if user is authorized to create modules for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("SELECT created_by FROM courses WHERE course_id = :courseId");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $course = $stmt->fetch();

                if (!isTeacher() || $course['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to create modules for this course.";
                }
            } else {
                return "Course not found.";
            }
        }

        // Create the module
        $stmt = $pdo->prepare("
            INSERT INTO modules (course_id, title, description, order_number)
            VALUES (:courseId, :title, :description, :orderNumber)
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':orderNumber', $orderNumber);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create module: " . $e->getMessage();
    }
}

/**
 * Function to update a module
 * Admin can update any module, teachers can only update modules in their own courses
 *
 * @param int $moduleId The module ID
 * @param string $title The module title
 * @param string $description The module description
 * @param int $orderNumber The order number
 * @return bool|string True if update successful, error message otherwise
 */
function updateModule($moduleId, $title, $description, $orderNumber) {
    global $pdo;

    try {
        // Check if user is authorized to update this module
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM modules m
                JOIN courses c ON m.course_id = c.course_id
                WHERE m.module_id = :moduleId
            ");
            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $module = $stmt->fetch();

                if (!isTeacher() || $module['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this module.";
                }
            } else {
                return "Module not found.";
            }
        }

        // Update the module
        $stmt = $pdo->prepare("
            UPDATE modules
            SET title = :title, description = :description, order_number = :orderNumber
            WHERE module_id = :moduleId
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':orderNumber', $orderNumber);
        $stmt->bindParam(':moduleId', $moduleId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update module: " . $e->getMessage();
    }
}

/**
 * Function to delete a module
 * Admin can delete any module, teachers can only delete modules in their own courses
 *
 * @param int $moduleId The module ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteModule($moduleId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this module
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM modules m
                JOIN courses c ON m.course_id = c.course_id
                WHERE m.module_id = :moduleId
            ");
            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $module = $stmt->fetch();

                if (!isTeacher() || $module['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this module.";
                }
            } else {
                return "Module not found.";
            }
        }

        // Delete the module
        $stmt = $pdo->prepare("DELETE FROM modules WHERE module_id = :moduleId");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Module not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete module: " . $e->getMessage();
    }
}

/**
 * Function to get all modules for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of modules if successful, error message otherwise
 */
function getModulesByCourse($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT module_id, title, description, order_number, created_at, updated_at
            FROM modules
            WHERE course_id = :courseId
            ORDER BY order_number
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve modules: " . $e->getMessage();
    }
}

/**
 * Function to get a module by ID
 *
 * @param int $moduleId The module ID
 * @return array|string Module data if successful, error message otherwise
 */
function getModuleById($moduleId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT module_id, course_id, title, description, order_number, created_at, updated_at
            FROM modules
            WHERE module_id = :moduleId
        ");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Module not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve module: " . $e->getMessage();
    }
}

/**
 * Function to create a new lesson
 * Admin can create lessons for any module, teachers can only create lessons for modules in their own courses
 *
 * @param int $moduleId The module ID
 * @param string $title The lesson title
 * @param string $content The lesson content
 * @param int $orderNumber The order number
 * @return bool|string True if creation successful, error message otherwise
 */
function createLesson($moduleId, $title, $content, $orderNumber) {
    global $pdo;

    try {
        // Check if user is authorized to create lessons for this module
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM modules m
                JOIN courses c ON m.course_id = c.course_id
                WHERE m.module_id = :moduleId
            ");
            $stmt->bindParam(':moduleId', $moduleId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $module = $stmt->fetch();

                if (!isTeacher() || $module['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to create lessons for this module.";
                }
            } else {
                return "Module not found.";
            }
        }

        // Create the lesson
        $stmt = $pdo->prepare("
            INSERT INTO lessons (module_id, title, content, order_number)
            VALUES (:moduleId, :title, :content, :orderNumber)
        ");

        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':orderNumber', $orderNumber);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to create lesson: " . $e->getMessage();
    }
}

/**
 * Function to update a lesson
 * Admin can update any lesson, teachers can only update lessons in their own courses
 *
 * @param int $lessonId The lesson ID
 * @param string $title The lesson title
 * @param string $content The lesson content
 * @param int $orderNumber The order number
 * @return bool|string True if update successful, error message otherwise
 */
function updateLesson($lessonId, $title, $content, $orderNumber) {
    global $pdo;

    try {
        // Check if user is authorized to update this lesson
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM lessons l
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE l.lesson_id = :lessonId
            ");
            $stmt->bindParam(':lessonId', $lessonId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $lesson = $stmt->fetch();

                if (!isTeacher() || $lesson['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to update this lesson.";
                }
            } else {
                return "Lesson not found.";
            }
        }

        // Update the lesson
        $stmt = $pdo->prepare("
            UPDATE lessons
            SET title = :title, content = :content, order_number = :orderNumber
            WHERE lesson_id = :lessonId
        ");

        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':orderNumber', $orderNumber);
        $stmt->bindParam(':lessonId', $lessonId);

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update lesson: " . $e->getMessage();
    }
}

/**
 * Function to delete a lesson
 * Admin can delete any lesson, teachers can only delete lessons in their own courses
 *
 * @param int $lessonId The lesson ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteLesson($lessonId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this lesson
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM lessons l
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE l.lesson_id = :lessonId
            ");
            $stmt->bindParam(':lessonId', $lessonId);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $lesson = $stmt->fetch();

                if (!isTeacher() || $lesson['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to delete this lesson.";
                }
            } else {
                return "Lesson not found.";
            }
        }

        // Delete the lesson
        $stmt = $pdo->prepare("DELETE FROM lessons WHERE lesson_id = :lessonId");
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Lesson not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete lesson: " . $e->getMessage();
    }
}

/**
 * Function to get all lessons for a module
 *
 * @param int $moduleId The module ID
 * @return array|string Array of lessons if successful, error message otherwise
 */
function getLessonsByModule($moduleId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT lesson_id, title, content, order_number, created_at, updated_at
            FROM lessons
            WHERE module_id = :moduleId
            ORDER BY order_number
        ");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve lessons: " . $e->getMessage();
    }
}

/**
 * Function to get a lesson by ID
 *
 * @param int $lessonId The lesson ID
 * @return array|string Lesson data if successful, error message otherwise
 */
function getLessonById($lessonId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT lesson_id, module_id, title, content, order_number, created_at, updated_at
            FROM lessons
            WHERE lesson_id = :lessonId
        ");
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Lesson not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve lesson: " . $e->getMessage();
    }
}

/**
 * Function to update a student's lesson progress
 *
 * @param int $userId The user ID
 * @param int $lessonId The lesson ID
 * @param string $status The progress status (not_started, in_progress, completed)
 * @return bool|string True if update successful, error message otherwise
 */
function updateLessonProgress($userId, $lessonId, $status) {
    global $pdo;

    try {
        // Check if the progress record exists
        $stmt = $pdo->prepare("
            SELECT progress_id
            FROM lesson_progress
            WHERE user_id = :userId AND lesson_id = :lessonId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Create a new progress record
            $stmt = $pdo->prepare("
                INSERT INTO lesson_progress (user_id, lesson_id, status)
                VALUES (:userId, :lessonId, :status)
            ");
        } else {
            // Update the existing progress record
            $stmt = $pdo->prepare("
                UPDATE lesson_progress
                SET status = :status, last_accessed = CURRENT_TIMESTAMP
                WHERE user_id = :userId AND lesson_id = :lessonId
            ");
        }

        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->bindParam(':status', $status);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update lesson progress: " . $e->getMessage();
    }
}

/**
 * Function to get a student's lesson progress
 *
 * @param int $userId The user ID
 * @param int $lessonId The lesson ID
 * @return array|string Progress data if successful, error message otherwise
 */
function getLessonProgress($userId, $lessonId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT progress_id, status, last_accessed
            FROM lesson_progress
            WHERE user_id = :userId AND lesson_id = :lessonId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':lessonId', $lessonId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return [
                'progress_id' => null,
                'status' => 'not_started',
                'last_accessed' => null
            ];
        }
    } catch (PDOException $e) {
        return "Failed to retrieve lesson progress: " . $e->getMessage();
    }
}

/**
 * Function to get all modules and their lessons for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of modules with lessons if successful, error message otherwise
 */
function getCourseModules($courseId) {
    try {
        // Get all modules for the course
        $modules = getModulesByCourse($courseId);

        if (is_string($modules)) {
            return $modules; // Return error message
        }

        // For each module, get its lessons
        foreach ($modules as &$module) {
            $lessons = getLessonsByModule($module['module_id']);

            if (is_string($lessons)) {
                $module['lessons'] = []; // Set empty array if error
                $module['error'] = $lessons; // Store error message
            } else {
                $module['lessons'] = $lessons;
            }
        }

        return $modules;
    } catch (Exception $e) {
        return "Failed to retrieve course modules: " . $e->getMessage();
    }
}

/**
 * Function to get all content for a module (assignments, activities, quizzes, materials)
 *
 * @param int $moduleId The module ID
 * @return array|string Array of content if successful, error message otherwise
 */
function getModuleContent($moduleId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT
                a.activity_id as content_id,
                a.title,
                a.description,
                a.activity_type as content_type,
                a.points,
                a.due_date,
                a.is_published,
                a.created_at,
                a.created_by,
                u.username as creator_name
            FROM activities a
            JOIN module_activities ma ON a.activity_id = ma.activity_id
            JOIN users u ON a.created_by = u.user_id
            WHERE ma.module_id = :moduleId AND a.is_published = 1
            ORDER BY ma.order_number, a.created_at DESC
        ");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve module content: " . $e->getMessage();
    }
}

/**
 * Function to get material by ID
 *
 * @param int $materialId The material ID
 * @return array|string Material data if successful, error message otherwise
 */
function getMaterialById($materialId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.activity_id, a.course_id, a.title, a.description as content, a.activity_type,
                   a.points, a.due_date, a.is_published, a.created_at, a.created_by,
                   u.username as creator_name
            FROM activities a
            JOIN users u ON a.created_by = u.user_id
            WHERE a.activity_id = :materialId AND a.activity_type = 'material'
        ");
        $stmt->bindParam(':materialId', $materialId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Material not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve material: " . $e->getMessage();
    }
}

/**
 * Function to get all announcements for a course
 *
 * @param int $courseId The course ID
 * @return array|string Array of announcements if successful, error message otherwise
 */
function getCourseAnnouncements($courseId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.announcement_id, a.title, a.content, a.created_at, a.created_by,
                   u.user_id, u.first_name, u.last_name, u.username,
                   CONCAT(u.first_name, ' ', u.last_name) AS creator_name
            FROM announcements a
            JOIN users u ON a.created_by = u.user_id
            WHERE a.course_id = :courseId
            ORDER BY a.created_at DESC
        ");
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve announcements: " . $e->getMessage();
    }
}
