<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Updating Activities Table Schema</h1>";

try {
    // Check if 'activity' is already in the activity_type enum
    $stmt = $pdo->query("SHOW COLUMNS FROM activities LIKE 'activity_type'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($column) {
        $type = $column['Type'];
        echo "<p>Current activity_type definition: " . htmlspecialchars($type) . "</p>";
        
        // Check if 'activity' is already in the enum
        if (strpos($type, "'activity'") !== false) {
            echo "<p>The 'activity' type is already included in the activity_type enum.</p>";
        } else {
            // Add 'activity' to the enum
            $newType = str_replace("enum('material','assignment','quiz','question')", "enum('material','assignment','quiz','question','activity')", $type);
            
            // Alter the table
            $pdo->exec("ALTER TABLE activities MODIFY COLUMN activity_type " . $newType);
            
            echo "<p>✓ Successfully added 'activity' to the activity_type enum.</p>";
        }
    } else {
        echo "<p>Error: Could not find the activity_type column in the activities table.</p>";
    }
    
    echo "<p><a href='index.php' class='btn btn-primary'>Return to Home</a></p>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error updating activities table: " . $e->getMessage() . "</div>";
}
?>
