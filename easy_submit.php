<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can submit activities.";
    header("location: index.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if student is enrolled in the course
if (!isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You are not enrolled in this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Get activity questions
$questions = [];
if ($activity['activity_type'] == 'quiz') {
    $questions = getQuizQuestions($activityId);
} else {
    $questions = getActivityQuestions($activityId);
}

if (is_string($questions)) {
    $questions = [];
}

// Check if the student has already submitted this activity
$studentSubmission = null;
$result = getStudentSubmission($activityId, $_SESSION['user_id']);
if (!is_string($result)) {
    $studentSubmission = $result;
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $answers = [];
    $content = "";
    $filePath = null;
    $fileName = null;
    $fileType = null;
    $fileSize = null;

    // Process answers for questions
    if (!empty($questions)) {
        foreach ($questions as $question) {
            $questionId = $question['question_id'];
            if (isset($_POST["answer_$questionId"])) {
                $answers[$questionId] = $_POST["answer_$questionId"];
            }
        }
    }

    // We no longer process content or file uploads for activities

    // Submit the activity
    $result = submitActivity($activityId, $_SESSION['user_id'], $content, $answers, $filePath, $fileName, $fileType, $fileSize);

    if (is_numeric($result)) {
        // Submission successful
        $_SESSION['success'] = ucfirst($activity['activity_type']) . " submitted successfully!";

        // Redirect back to activity view
        header("location: activity_view.php?id=$activityId");
        exit;
    } else {
        // Error submitting activity
        $_SESSION['error'] = $result;
    }
}

// Set page title
$page_title = "Submit " . ucfirst($activity['activity_type']) . " - " . htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <!-- Back button -->
            <div class="mb-3">
                <a href="activity_view.php?id=<?php echo $activityId; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to <?php echo ucfirst($activity['activity_type']); ?>
                </a>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><?php echo htmlspecialchars($activity['title']); ?></h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                    <?php endif; ?>

                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $activityId); ?>" method="post" enctype="multipart/form-data">
                        <?php if (!empty($questions)): ?>
                            <div class="mb-4">
                                <h5><?php echo ($activity['activity_type'] == 'assignment') ? 'Assignment Questions' : 'Questions'; ?></h5>
                                <?php foreach ($questions as $index => $question): ?>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6><?php echo ($index + 1) . '. ' . htmlspecialchars($question['question_text']); ?></h6>

                                            <?php if ($question['question_type'] == 'multiple_choice' && isset($question['options'])): ?>
                                                <?php foreach ($question['options'] as $option): ?>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_<?php echo $option['option_id']; ?>" value="<?php echo $option['option_id']; ?>" required>
                                                        <label class="form-check-label" for="option_<?php echo $option['option_id']; ?>">
                                                            <?php echo htmlspecialchars($option['option_text']); ?>
                                                        </label>
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php elseif ($question['question_type'] == 'true_false'): ?>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_true_<?php echo $question['question_id']; ?>" value="true_<?php echo $question['question_id']; ?>" required>
                                                    <label class="form-check-label" for="option_true_<?php echo $question['question_id']; ?>">True</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="answer_<?php echo $question['question_id']; ?>" id="option_false_<?php echo $question['question_id']; ?>" value="false_<?php echo $question['question_id']; ?>" required>
                                                    <label class="form-check-label" for="option_false_<?php echo $question['question_id']; ?>">False</label>
                                                </div>
                                            <?php elseif ($question['question_type'] == 'short_answer'): ?>
                                                <input type="text" name="answer_<?php echo $question['question_id']; ?>" class="form-control" placeholder="Your answer" required>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
