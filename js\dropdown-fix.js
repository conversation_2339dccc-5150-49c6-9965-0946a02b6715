/**
 * Custom dropdown menu handler
 * This script ensures that dropdown menus work properly
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all dropdown toggles
    var dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    // Add click handlers to each toggle
    dropdownToggles.forEach(function(toggle) {
        var dropdown = toggle.closest('.dropdown');
        var menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            // Handle click on the toggle button
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Toggle the dropdown menu visibility
                if (menu.classList.contains('show')) {
                    menu.classList.remove('show');
                    menu.style.display = 'none';
                } else {
                    // Close all other open dropdowns first
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(openMenu) {
                        if (openMenu !== menu) {
                            openMenu.classList.remove('show');
                            openMenu.style.display = 'none';
                        }
                    });
                    
                    // Show this dropdown
                    menu.classList.add('show');
                    menu.style.display = 'block';
                    menu.style.zIndex = '9999';
                    menu.style.position = 'absolute';
                    menu.style.top = (toggle.offsetHeight + 2) + 'px';
                    menu.style.right = '0';
                    menu.style.left = 'auto';
                }
            });
            
            // Prevent dropdown from closing when clicking inside
            menu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
            var toggle = menu.previousElementSibling;
            if (toggle && !toggle.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.remove('show');
                menu.style.display = 'none';
            }
        });
    });
});
