<?php
/**
 * AJAX endpoint for dashboard filtering
 * Handles semester and time range filtering for admin dashboard
 */

require_once '../includes/config.php';
require_once '../includes/dashboard_functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get filter parameters
    $semester = isset($_GET['semester']) ? $_GET['semester'] : null;
    $timeRange = isset($_GET['time_range']) ? $_GET['time_range'] : 'month';
    
    // Validate semester parameter
    if ($semester && !in_array($semester, ['first', 'second', 'all'])) {
        throw new Exception('Invalid semester parameter');
    }
    
    // Convert 'all' to null for the function
    if ($semester === 'all') {
        $semester = null;
    }
    
    // Get dashboard data with filters
    $dashboardCounts = getDashboardCounts($semester);
    $enrollmentTrends = getEnrollmentTrends();
    $userDistribution = getUserDistribution();
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => [
            'counts' => $dashboardCounts,
            'trends' => $enrollmentTrends,
            'distribution' => $userDistribution,
            'filters' => [
                'semester' => $semester,
                'time_range' => $timeRange
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
