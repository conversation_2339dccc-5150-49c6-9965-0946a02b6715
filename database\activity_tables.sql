-- Activity Files Table
CREATE TABLE IF NOT EXISTS activity_files (
    file_id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    uploaded_by INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(user_id)
);

-- Add allow_late_submissions column to activities table if it doesn't exist
ALTER TABLE activities ADD COLUMN IF NOT EXISTS allow_late_submissions TINYINT(1) DEFAULT 0;
