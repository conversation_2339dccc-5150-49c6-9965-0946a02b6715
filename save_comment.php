<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'You must be logged in to perform this action.']);
    exit;
}

// Set the response header to JSON
header('Content-Type: application/json');

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Check if the action is specified
if (!isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'No action specified.']);
    exit;
}

// Check if the activity ID is specified
if (!isset($_POST['activity_id'])) {
    echo json_encode(['success' => false, 'message' => 'No activity ID specified.']);
    exit;
}

$activityId = intval($_POST['activity_id']);
$action = $_POST['action'];

// Create the data directory if it doesn't exist
$dataDir = 'data';
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Define the comments file path
$commentsFile = $dataDir . '/comments_' . $activityId . '.json';

// Load existing comments
$comments = [];
if (file_exists($commentsFile)) {
    $commentsJson = file_get_contents($commentsFile);
    $comments = json_decode($commentsJson, true);

    if ($comments === null) {
        $comments = [];
    }
}

// Handle the action
switch ($action) {
    case 'get_comments':
        // Filter comments to only show private comments for the current user or instructor
        $filteredComments = [];
        foreach ($comments as $index => $comment) {
            // Include private comments if they belong to the current user or if the user is an instructor/admin
            if (isset($comment['user_id']) && $comment['user_id'] == $_SESSION['user_id']) {
                // This is the user's own private comment
                $filteredComments[] = $comment;
            } elseif (isTeacher() || isAdmin()) {
                // Instructors and admins can see all private comments
                $filteredComments[] = $comment;
            }
        }

        // Return the filtered comments
        echo json_encode(['success' => true, 'comments' => $filteredComments]);
        break;

    case 'save_comment':
        // Check if the comment is specified
        if (!isset($_POST['comment'])) {
            echo json_encode(['success' => false, 'message' => 'No comment specified.']);
            exit;
        }

        // Decode the comment
        $comment = json_decode($_POST['comment'], true);

        if ($comment === null) {
            echo json_encode(['success' => false, 'message' => 'Invalid comment format.']);
            exit;
        }

        // Make sure the username is properly formatted
        if (!isset($comment['username']) || empty($comment['username'])) {
            // Use the session data to set the username
            if (isset($_SESSION['first_name']) && isset($_SESSION['last_name'])) {
                $comment['username'] = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
            } elseif (isset($_SESSION['username'])) {
                $comment['username'] = $_SESSION['username'];
            } else {
                $comment['username'] = 'User';
            }
        }

        // Make sure the avatar is properly set
        if (!isset($comment['avatar']) || empty($comment['avatar'])) {
            // Get user profile picture
            try {
                $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                $stmt->bindParam(':userId', $_SESSION['user_id']);
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    $userData = $stmt->fetch();
                    if (!empty($userData['profile_picture'])) {
                        $comment['avatar'] = $userData['profile_picture'];
                    }
                }
            } catch (PDOException $e) {
                // Silently fail and use default avatar
            }
        }

        // Ensure the avatar is not empty by explicitly setting it
        if (empty($comment['avatar'])) {
            try {
                // Try to get the profile picture again with a more direct approach
                $userId = $_SESSION['user_id'];
                $stmt = $pdo->query("SELECT profile_picture FROM users WHERE user_id = $userId");
                $userData = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($userData && !empty($userData['profile_picture'])) {
                    $comment['avatar'] = $userData['profile_picture'];
                }
            } catch (PDOException $e) {
                // Silently fail
            }
        }

        // Ensure user_id is set
        if (!isset($comment['user_id'])) {
            $comment['user_id'] = $_SESSION['user_id'];
        }

        // Always set comments as private
        $comment['is_private'] = true;

        // Add the comment to the array
        $commentId = count($comments);
        $comments[] = $comment;

        // Save the comments to the file
        if (file_put_contents($commentsFile, json_encode($comments)) === false) {
            echo json_encode(['success' => false, 'message' => 'Failed to save comment.']);
            exit;
        }

        // Return success
        echo json_encode(['success' => true, 'comment_id' => $commentId]);
        break;

    case 'delete_comment':
        // Check if the comment ID is specified
        if (!isset($_POST['comment_id'])) {
            echo json_encode(['success' => false, 'message' => 'No comment ID specified.']);
            exit;
        }

        $commentId = $_POST['comment_id'];

        // Check if the comment ID is valid
        if (!is_numeric($commentId) || !isset($comments[$commentId])) {
            echo json_encode(['success' => false, 'message' => 'Invalid comment ID.']);
            exit;
        }

        // Remove the comment
        unset($comments[$commentId]);

        // Reindex the array
        $comments = array_values($comments);

        // Save the comments to the file
        if (file_put_contents($commentsFile, json_encode($comments)) === false) {
            echo json_encode(['success' => false, 'message' => 'Failed to delete comment.']);
            exit;
        }

        // Return success
        echo json_encode(['success' => true]);
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action.']);
        break;
}
?>
