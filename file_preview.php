<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found. Please contact the administrator.";
    exit;
}

// Get file information
$fileInfo = pathinfo($filePath);
$fileName = $fileInfo['basename'];
$fileExtension = strtolower($fileInfo['extension']);

// Determine file type for display
$fileType = 'unknown';
if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'svg'])) {
    $fileType = 'image';
} elseif ($fileExtension == 'pdf') {
    $fileType = 'pdf';
} elseif (in_array($fileExtension, ['txt', 'html', 'css', 'js', 'json', 'xml'])) {
    $fileType = 'text';
} elseif (in_array($fileExtension, ['doc', 'docx'])) {
    $fileType = 'word';
} elseif (in_array($fileExtension, ['xls', 'xlsx'])) {
    $fileType = 'excel';
} elseif (in_array($fileExtension, ['ppt', 'pptx'])) {
    $fileType = 'powerpoint';
}

// Set page title
$page_title = "Preview: " . $fileName;

// Don't include the standard header for this page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($fileName); ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        .viewer-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .toolbar {
            background-color: #f8f9fa;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #dee2e6;
        }
        .content-frame {
            flex: 1;
            border: none;
            width: 100%;
            height: calc(100% - 60px);
        }
        .preview-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: calc(100% - 60px);
            padding: 20px;
        }
        .file-icon {
            font-size: 5rem;
            margin-bottom: 20px;
        }
        .file-info {
            text-align: center;
            max-width: 600px;
        }
    </style>
</head>
<body>
    <div class="viewer-container">
        <div class="toolbar">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-file-<?php
                        if ($fileType == 'word') echo 'word';
                        elseif ($fileType == 'excel') echo 'excel';
                        elseif ($fileType == 'powerpoint') echo 'powerpoint';
                        elseif ($fileType == 'pdf') echo 'pdf';
                        elseif ($fileType == 'image') echo 'image';
                        elseif ($fileType == 'text') echo 'alt';
                        else echo 'alt';
                    ?> mr-2"></i> <?php echo htmlspecialchars($fileName); ?>
                </h5>
            </div>
            <div>
                <?php
                // Get the referer URL if available
                $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

                // Check if referer is from our site
                $isFromOurSite = !empty($referer) && (strpos($referer, $_SERVER['HTTP_HOST']) !== false);

                // If we have a valid referer, use it; otherwise, go to home
                $backUrl = $isFromOurSite ? $referer : 'index.php';
                ?>
                <a href="<?php echo htmlspecialchars($backUrl); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
                <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-primary ml-2">
                    <i class="fas fa-download mr-2"></i> Download
                </a>
            </div>
        </div>

        <?php if ($fileType == 'image'): ?>
            <div class="text-center p-4" style="height: calc(100% - 60px); overflow: auto;">
                <img src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" style="max-width: 100%; max-height: 100%;" alt="<?php echo htmlspecialchars($fileName); ?>">
            </div>
        <?php elseif ($fileType == 'pdf'): ?>
            <iframe src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="content-frame" allowfullscreen></iframe>
        <?php elseif ($fileType == 'text'): ?>
            <iframe src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="content-frame"></iframe>
        <?php elseif ($fileType == 'word' || $fileType == 'excel' || $fileType == 'powerpoint'): ?>
            <div class="preview-container">
                <div class="file-icon text-primary">
                    <i class="fas fa-file-<?php
                        if ($fileType == 'word') echo 'word';
                        elseif ($fileType == 'excel') echo 'excel';
                        elseif ($fileType == 'powerpoint') echo 'powerpoint';
                    ?>"></i>
                </div>
                <div class="file-info">
                    <h3><?php echo htmlspecialchars($fileName); ?></h3>
                    <p class="text-muted"><?php echo strtoupper($fileExtension); ?> File</p>

                    <div class="alert alert-info mt-4">
                        <p>This file is a Microsoft Office document that cannot be previewed directly in the browser.</p>
                        <p>Please download the file to view its contents.</p>
                    </div>

                    <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-lg btn-primary mt-3">
                        <i class="fas fa-download mr-2"></i> Download File
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="preview-container">
                <div class="file-icon text-secondary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="file-info">
                    <h3><?php echo htmlspecialchars($fileName); ?></h3>
                    <p class="text-muted"><?php echo strtoupper($fileExtension); ?> File</p>

                    <div class="alert alert-warning mt-4">
                        <p>This file type cannot be previewed in the browser.</p>
                        <p>Please download the file to view its contents.</p>
                    </div>

                    <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-lg btn-primary mt-3">
                        <i class="fas fa-download mr-2"></i> Download File
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
