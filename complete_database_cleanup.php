<?php
/**
 * Complete Database Cleanup Script
 * This script removes unnecessary columns and fixes structural issues
 *
 * IMPORTANT: This script will make permanent changes to your database.
 * Make sure to backup your database before running this script.
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
try {
    $pdo = new PDO("mysql:host=" . DB_SERVER . ";dbname=" . DB_NAME, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h1>Complete Database Cleanup Script</h1>";
    echo "<p>Connected to database successfully.</p>";
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<strong>⚠️ WARNING:</strong> This script will make permanent changes to your database. ";
    echo "Make sure you have backed up your database before proceeding.";
    echo "</div>";
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to execute SQL with error handling
function executeSQL($sql, $description) {
    global $pdo;
    try {
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ $description - SUCCESS</p>";
        return true;
    } catch(PDOException $e) {
        echo "<p style='color: red;'>✗ $description - ERROR: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Function to check if table exists
function tableExists($tableName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch(PDOException $e) {
        return false;
    }
}

// Function to check if column exists
function columnExists($tableName, $columnName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$tableName` LIKE ?");
        $stmt->execute([$columnName]);
        return $stmt->rowCount() > 0;
    } catch(PDOException $e) {
        return false;
    }
}

// Function to check if foreign key exists
function foreignKeyExists($tableName, $constraintName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND CONSTRAINT_NAME = ?
        ");
        $stmt->execute([DB_NAME, $tableName, $constraintName]);
        return $stmt->rowCount() > 0;
    } catch(PDOException $e) {
        return false;
    }
}

echo "<h2>Starting Complete Database Cleanup...</h2>";

// 1. Remove obsolete quizzes table (conflicts with activities-based system)
echo "<h3>1. Removing Obsolete Tables</h3>";
if (tableExists('quizzes')) {
    executeSQL("DROP TABLE IF EXISTS quizzes", "Remove obsolete 'quizzes' table");
} else {
    echo "<p style='color: blue;'>ℹ 'quizzes' table doesn't exist - skipping</p>";
}

// 2. Fix submissions table structure
echo "<h3>2. Fixing Submissions Table Structure</h3>";

if (tableExists('submissions')) {
    // Remove file-related columns from submissions (these should be in activity_files table)
    $fileColumns = ['file_path', 'file_name', 'file_type', 'file_size'];

    foreach ($fileColumns as $column) {
        if (columnExists('submissions', $column)) {
            executeSQL("ALTER TABLE submissions DROP COLUMN `$column`", "Remove '$column' from submissions table");
        }
    }

    // Fix primary key issue in submissions table
    executeSQL("ALTER TABLE submissions MODIFY submission_id INT AUTO_INCREMENT PRIMARY KEY", "Fix submissions table primary key");

    // Add proper foreign key constraints if they don't exist
    if (!foreignKeyExists('submissions', 'fk_submissions_activity')) {
        executeSQL("ALTER TABLE submissions ADD CONSTRAINT fk_submissions_activity FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE", "Add foreign key constraint for activity_id in submissions");
    }

    if (!foreignKeyExists('submissions', 'fk_submissions_user')) {
        executeSQL("ALTER TABLE submissions ADD CONSTRAINT fk_submissions_user FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE", "Add foreign key constraint for user_id in submissions");
    }
}

echo "<p><strong>Continuing cleanup...</strong></p>";

// 3. Simplify user_notification_settings table
echo "<h3>3. Simplifying User Notification Settings</h3>";

if (tableExists('user_notification_settings')) {
    // Keep only essential notification columns, remove excessive ones
    $unnecessaryColumns = [
        'assignment_reminders', 'course_announcements', 'due_date_reminders',
        'grade_updates', 'student_submissions', 'enrollment_requests',
        'course_activity', 'user_registrations', 'course_creation',
        'account_changes', 'error_alerts', 'weekly_reports',
        'comments_mentions', 'private_comments', 'event_creation',
        'event_updates', 'event_reminders', 'status_changes',
        'system_announcements'
    ];

    foreach ($unnecessaryColumns as $column) {
        if (columnExists('user_notification_settings', $column)) {
            executeSQL("ALTER TABLE user_notification_settings DROP COLUMN `$column`", "Remove unnecessary '$column' from user_notification_settings");
        }
    }
} else {
    echo "<p style='color: blue;'>ℹ 'user_notification_settings' table doesn't exist - skipping</p>";
}

// 4. Remove unused/redundant tables
echo "<h3>4. Removing Unused Tables</h3>";

$unusedTables = ['modules', 'lessons', 'forums', 'forum_posts', 'lesson_progress'];

foreach ($unusedTables as $table) {
    if (tableExists($table)) {
        executeSQL("DROP TABLE IF EXISTS `$table`", "Remove unused '$table' table");
    } else {
        echo "<p style='color: blue;'>ℹ '$table' table doesn't exist - skipping</p>";
    }
}

// 5. Clean up quiz-related tables structure
echo "<h3>5. Optimizing Quiz Tables</h3>";

// Remove redundant columns from quiz_settings if they exist
if (tableExists('quiz_settings')) {
    $redundantQuizColumns = ['passing_score', 'shuffle_questions', 'show_results_immediately'];

    foreach ($redundantQuizColumns as $column) {
        if (columnExists('quiz_settings', $column)) {
            executeSQL("ALTER TABLE quiz_settings DROP COLUMN `$column`", "Remove redundant '$column' from quiz_settings");
        }
    }
} else {
    echo "<p style='color: blue;'>ℹ 'quiz_settings' table properly structured</p>";
}

// 6. Final optimizations
echo "<h3>6. Final Database Optimizations</h3>";

// Optimize tables
$tablesToOptimize = ['users', 'courses', 'activities', 'submissions', 'enrollments', 'quiz_questions', 'quiz_options'];

foreach ($tablesToOptimize as $table) {
    if (tableExists($table)) {
        executeSQL("OPTIMIZE TABLE `$table`", "Optimize '$table' table");
    }
}

echo "<h2>Database Cleanup Completed!</h2>";
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
echo "<p><strong>Summary of changes made:</strong></p>";
echo "<ul>";
echo "<li>✓ Removed obsolete 'quizzes' table that conflicted with activities-based system</li>";
echo "<li>✓ Cleaned up submissions table by removing file-related columns</li>";
echo "<li>✓ Fixed primary key issues in submissions table</li>";
echo "<li>✓ Added proper foreign key constraints</li>";
echo "<li>✓ Simplified user_notification_settings table by removing excessive columns</li>";
echo "<li>✓ Removed unused tables (modules, lessons, forums, etc.)</li>";
echo "<li>✓ Optimized quiz_settings table structure</li>";
echo "<li>✓ Optimized all remaining tables</li>";
echo "</ul>";
echo "</div>";

echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ Database cleanup completed successfully!</p>";
echo "<p><em>Your database is now cleaner, more optimized, and follows better structural practices.</em></p>";
?>
