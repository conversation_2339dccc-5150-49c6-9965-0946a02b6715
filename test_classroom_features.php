<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Test Classroom Features";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <h1>Google Classroom-like Features Test</h1>
    
    <div class="alert alert-info">
        <p>This page tests the Google Classroom-like features implemented in the system.</p>
        <p>Current user: <strong><?php echo $_SESSION['username']; ?></strong> (<?php echo $_SESSION['role']; ?>)</p>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Role-Based Features</h2>
        </div>
        <div class="card-body">
            <h3>Admin Features</h3>
            <ul>
                <li>Monitor system usage</li>
                <li>Manage all users (create, edit, deactivate)</li>
                <li>Manage all courses</li>
                <li>Generate reports</li>
                <li>Configure system settings</li>
            </ul>
            
            <h3>Teacher Features</h3>
            <ul>
                <li>Create and manage courses</li>
                <li>Add course materials</li>
                <li>Create assignments</li>
                <li>Grade student submissions</li>
                <li>Manage enrolled students</li>
                <li>View student progress</li>
                <li>Create announcements</li>
                <li>Manage course discussions</li>
            </ul>
            
            <h3>Student Features</h3>
            <ul>
                <li>View enrolled courses</li>
                <li>Browse available courses</li>
                <li>Enroll in courses</li>
                <li>View course materials</li>
                <li>Submit assignments</li>
                <li>View grades</li>
                <li>Participate in discussions</li>
                <li>Track progress</li>
            </ul>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Available Features</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-primary text-white">
                            <h3 class="h5 mb-0">Course View</h3>
                        </div>
                        <div class="card-body">
                            <p>View courses with tabs for Stream, Classwork, People, and Grades.</p>
                            <a href="course_view_full.php?id=1" class="btn btn-primary">Open Course View</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-success text-white">
                            <h3 class="h5 mb-0">Create Assignment</h3>
                        </div>
                        <div class="card-body">
                            <p>Create assignments for students to complete.</p>
                            <?php if (isTeacher() || isAdmin()): ?>
                            <a href="assignment_create.php?course_id=1" class="btn btn-success">Create Assignment</a>
                            <?php else: ?>
                            <p class="text-muted">Only teachers can create assignments.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-info text-white">
                            <h3 class="h5 mb-0">Create Material</h3>
                        </div>
                        <div class="card-body">
                            <p>Add learning materials to courses.</p>
                            <?php if (isTeacher() || isAdmin()): ?>
                            <a href="material_create.php?course_id=1" class="btn btn-info">Create Material</a>
                            <?php else: ?>
                            <p class="text-muted">Only teachers can create materials.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-warning text-dark">
                            <h3 class="h5 mb-0">Create Announcement</h3>
                        </div>
                        <div class="card-body">
                            <p>Post announcements to course streams.</p>
                            <?php if (isTeacher() || isAdmin()): ?>
                            <a href="announcement_create.php?course_id=1" class="btn btn-warning">Create Announcement</a>
                            <?php else: ?>
                            <p class="text-muted">Only teachers can create announcements.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-secondary text-white">
                            <h3 class="h5 mb-0">Manage People</h3>
                        </div>
                        <div class="card-body">
                            <p>Manage instructors and students in courses.</p>
                            <a href="course_view_full.php?id=1&tab=people" class="btn btn-secondary">Manage People</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-danger text-white">
                            <h3 class="h5 mb-0">View Grades</h3>
                        </div>
                        <div class="card-body">
                            <p>View and manage grades for assignments.</p>
                            <a href="course_view_full.php?id=1&tab=grades" class="btn btn-danger">View Grades</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Database Tables</h2>
        </div>
        <div class="card-body">
            <p>The following tables have been created to support Google Classroom-like features:</p>
            <ul>
                <li><strong>modules</strong> - For organizing course content</li>
                <li><strong>materials</strong> - For course learning materials</li>
                <li><strong>assignments</strong> - For student assignments</li>
                <li><strong>submissions</strong> - For student assignment submissions</li>
                <li><strong>announcements</strong> - For course announcements</li>
                <li><strong>quizzes</strong> - For course quizzes</li>
                <li><strong>course_enrollments</strong> - For student enrollments</li>
                <li><strong>course_instructors</strong> - For course instructors</li>
            </ul>
            
            <p>You can run the setup script again if needed:</p>
            <a href="setup_classroom_tables.php" class="btn btn-primary">Run Setup Script</a>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
