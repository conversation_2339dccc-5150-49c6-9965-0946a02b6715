<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Debug information
if (isset($_GET['debug']) && $_GET['debug'] == 1 && isAdmin()) {
    echo "File path: " . $filePath . "<br>";
    echo "File exists: " . (file_exists($filePath) ? "Yes" : "No") . "<br>";

    // Try to find the file with different path combinations
    $possiblePaths = [
        $filePath,
        './' . $filePath,
        '../' . $filePath,
        $_SERVER['DOCUMENT_ROOT'] . '/' . $filePath,
        dirname(__FILE__) . '/' . $filePath
    ];

    foreach ($possiblePaths as $path) {
        echo "Checking path: " . $path . " - Exists: " . (file_exists($path) ? "Yes" : "No") . "<br>";
    }

    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath,
    dirname(__FILE__) . '/' . $filePath,
    $_SERVER['DOCUMENT_ROOT'] . '/' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
        $possiblePaths[] = dirname(__FILE__) . '/' . $matches[1];
        $possiblePaths[] = $_SERVER['DOCUMENT_ROOT'] . '/' . $matches[1];
    }
}

// Add more possible paths for announcements
if (strpos($filePath, 'announcements') !== false) {
    // Try different combinations for announcement files
    $possiblePaths[] = str_replace('uploads/announcements', './uploads/announcements', $filePath);
    $possiblePaths[] = str_replace('uploads/announcements', '../uploads/announcements', $filePath);

    // Extract announcement ID from path
    if (preg_match('/announcements\/(\d+)\//', $filePath, $matches)) {
        $announcementId = $matches[1];
        $possiblePaths[] = "uploads/announcements/$announcementId/" . basename($filePath);
        $possiblePaths[] = "./uploads/announcements/$announcementId/" . basename($filePath);
        $possiblePaths[] = "../uploads/announcements/$announcementId/" . basename($filePath);
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found. Please contact the administrator.";
    echo "<p>Path attempted: " . htmlspecialchars($filePath) . "</p>";

    // Additional debugging information
    echo "<p>Current working directory: " . getcwd() . "</p>";
    echo "<p>Document root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
    echo "<p>Script filename: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

    // Try to list the uploads directory
    $uploadsDir = 'uploads';
    if (file_exists($uploadsDir) && is_dir($uploadsDir)) {
        echo "<p>Uploads directory exists.</p>";
        $dirs = scandir($uploadsDir);
        echo "<p>Contents of uploads directory:</p><ul>";
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..') {
                echo "<li>" . htmlspecialchars($dir) . "</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>Uploads directory does not exist or is not accessible.</p>";
    }

    echo "<p><a href='file_debug.php'>Go to File Debug</a></p>";
    exit;
}

// Get file information
$fileInfo = pathinfo($filePath);
$fileName = $fileInfo['basename'];
$fileExtension = strtolower($fileInfo['extension']);
$fileSize = filesize($filePath);

// Get MIME type
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $filePath);
finfo_close($finfo);

// Determine MIME type from extension if needed
if ($mimeType == 'application/octet-stream' || $mimeType == 'text/plain') {
    // Try to determine MIME type from extension
    switch ($fileExtension) {
        case 'pdf':
            $mimeType = 'application/pdf';
            break;
        case 'doc':
            $mimeType = 'application/msword';
            break;
        case 'docx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            break;
        case 'xls':
            $mimeType = 'application/vnd.ms-excel';
            break;
        case 'xlsx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            break;
        case 'ppt':
            $mimeType = 'application/vnd.ms-powerpoint';
            break;
        case 'pptx':
            $mimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            break;
        case 'txt':
            $mimeType = 'text/plain';
            break;
        case 'jpg':
        case 'jpeg':
            $mimeType = 'image/jpeg';
            break;
        case 'png':
            $mimeType = 'image/png';
            break;
        case 'gif':
            $mimeType = 'image/gif';
            break;
        case 'svg':
            $mimeType = 'image/svg+xml';
            break;
    }
}

// List of MIME types that can be displayed in browser
$displayableMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/svg+xml',
    'application/pdf',
    'text/plain',
    'text/html',
    'text/css',
    'text/javascript',
    'application/javascript',
    'application/json',
    'text/xml',
    'application/xml',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
];

// Check if download is requested
$forceDownload = isset($_GET['download']) && $_GET['download'] == 'true';

// Determine if the file should be displayed in browser - default to display in browser
$displayInBrowser = !$forceDownload;

// Set appropriate headers
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . $fileSize);

if ($displayInBrowser) {
    // Display in browser
    header('Content-Disposition: inline; filename="' . $fileName . '"');
} else {
    // Force download
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
}

// Output file content
readfile($filePath);
exit;
