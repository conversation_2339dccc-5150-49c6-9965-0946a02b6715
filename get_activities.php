<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not return empty array
if (!isLoggedIn()) {
    echo json_encode([]);
    exit;
}

// Get activity type from query parameter
$activityType = isset($_GET['type']) ? $_GET['type'] : '';

// Validate activity type
$validTypes = ['activity', 'quiz', 'assignment', 'announcement'];
if (!in_array($activityType, $validTypes)) {
    echo json_encode([]);
    exit;
}

try {
    global $pdo;
    
    // Get activities of the specified type
    $query = "SELECT activity_id, title FROM activities WHERE activity_type = :activityType";
    
    // If not admin, only show activities for courses the user is associated with
    if (!isAdmin()) {
        if (isTeacher()) {
            // For teachers, show activities in courses they teach
            $query .= " AND course_id IN (
                SELECT course_id FROM courses WHERE created_by = :userId
                UNION
                SELECT course_id FROM course_instructors WHERE user_id = :userId
            )";
        } else {
            // For students, show activities in courses they're enrolled in
            $query .= " AND course_id IN (
                SELECT course_id FROM course_enrollments WHERE user_id = :userId
            )";
        }
    }
    
    $query .= " ORDER BY title ASC LIMIT 10";
    
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':activityType', $activityType);
    
    if (!isAdmin()) {
        $stmt->bindParam(':userId', $_SESSION['user_id']);
    }
    
    $stmt->execute();
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return activities as JSON
    echo json_encode($activities);
} catch (PDOException $e) {
    // Return empty array on error
    echo json_encode([]);
}
?>
