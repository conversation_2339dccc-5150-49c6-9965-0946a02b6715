-- Database backup before normalization
-- Created: 2025-05-27 10:51:30

SET FOREIGN_KEY_CHECKS = 0;

CREATE TABLE `activities` (
  `activity_id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `activity_type` enum('material','assignment','quiz','question','activity') DEFAULT NULL,
  `points` int(11) DEFAULT 0,
  `due_date` datetime DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 1,
  `allow_late_submissions` tinyint(1) DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`activity_id`),
  KEY `course_id` (`course_id`),
  <PERSON>EY `created_by` (`created_by`),
  CONSTRAINT `activities_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`course_id`) ON DELETE CASCADE,
  CONSTRAINT `activities_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activities` (`activity_id`, `course_id`, `title`, `description`, `activity_type`, `points`, `due_date`, `is_published`, `allow_late_submissions`, `created_by`, `created_at`, `updated_at`) VALUES
('3', '3', 'quiz 1', 'go with the flow', 'quiz', '0', '0000-00-00 00:00:00', '1', '0', '4', '2025-05-15 15:29:25', '2025-05-15 15:29:25'),
('5', '3', 'assignment 1', 'take a pick!', 'assignment', '0', '2025-05-17 07:49:00', '1', '1', '4', '2025-05-15 15:46:58', '2025-05-15 15:46:58'),
('7', '3', 'hki,', 'xffv', 'quiz', '0', '2025-05-20 23:32:00', '1', '1', '4', '2025-05-16 18:22:02', '2025-05-18 13:32:48'),
('8', '3', 'sample 1', 'ssffdf', 'activity', '0', '2025-05-10 11:23:00', '1', '1', '4', '2025-05-17 21:22:03', '2025-05-17 21:22:03'),
('10', '3', 'cxc', 'xcxc', 'material', '0', NULL, '1', '0', '4', '2025-05-18 12:29:04', '2025-05-18 12:29:04'),
('11', '3', 'sample 2', 'ccx', 'material', '0', NULL, '1', '0', '4', '2025-05-18 13:28:00', '2025-05-18 13:28:00'),
('12', '3', 'Assignment 2', 'Go with the flow', 'assignment', '0', '2025-05-22 23:16:00', '1', '0', '4', '2025-05-18 23:16:23', '2025-05-18 23:16:23'),
('17', '4', 'xzx', 'xzx', 'activity', '0', NULL, '1', '0', '4', '2025-05-18 23:42:05', '2025-05-18 23:42:05'),
('18', '4', 'jhjhjy', 'jhjhj', 'assignment', '0', '2025-05-29 14:45:00', '1', '0', '4', '2025-05-27 14:45:21', '2025-05-27 14:45:21'),
('19', '4', 'jjkjk', 'hhjhjh', 'activity', '0', '2025-05-29 18:54:00', '1', '0', '4', '2025-05-27 14:52:04', '2025-05-27 14:52:04');

CREATE TABLE `activity_answers` (
  `answer_id` int(11) NOT NULL AUTO_INCREMENT,
  `submission_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `answer_text` text DEFAULT NULL,
  `selected_option_id` int(11) DEFAULT NULL,
  `is_correct` tinyint(1) DEFAULT NULL,
  `points_earned` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`answer_id`),
  KEY `submission_id` (`submission_id`),
  KEY `question_id` (`question_id`),
  KEY `selected_option_id` (`selected_option_id`),
  CONSTRAINT `activity_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE,
  CONSTRAINT `activity_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `activity_questions` (`question_id`) ON DELETE CASCADE,
  CONSTRAINT `activity_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `activity_question_options` (`option_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_answers` (`answer_id`, `submission_id`, `question_id`, `answer_text`, `selected_option_id`, `is_correct`, `points_earned`) VALUES
('1', '6', '4', 'True', NULL, '1', '1.00'),
('2', '7', '1', 'maxcel', '1', '1', '1.00');

CREATE TABLE `activity_files` (
  `file_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `file_size` int(11) NOT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`file_id`),
  KEY `activity_id` (`activity_id`),
  CONSTRAINT `activity_files_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_files` (`file_id`, `activity_id`, `file_name`, `file_path`, `file_type`, `file_size`, `uploaded_at`) VALUES
('1', '10', 'Java.docx', 'uploads/materials/10/68296210170da_Java.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '29218', '2025-05-18 12:29:04'),
('2', '11', 'Presentation_Noe Narisma (1).pptx', 'uploads/materials/11/68296fe0cd01f_Presentation_Noe Narisma (1).pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', '7699546', '2025-05-18 13:28:00');

CREATE TABLE `activity_question_options` (
  `option_id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `option_text` text NOT NULL,
  `is_correct` tinyint(1) NOT NULL DEFAULT 0,
  `position` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`option_id`),
  KEY `question_id` (`question_id`),
  CONSTRAINT `activity_question_options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `activity_questions` (`question_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_question_options` (`option_id`, `question_id`, `option_text`, `is_correct`, `position`) VALUES
('1', '1', 'maxcel', '1', '0'),
('2', '1', 'kanan', '0', '0'),
('3', '1', 'tama', '0', '0'),
('4', '1', 'batas', '0', '0'),
('9', '4', 'True', '1', '0'),
('10', '4', 'False', '1', '0'),
('23', '10', 'True', '1', '0'),
('24', '10', 'False', '0', '0'),
('25', '11', 'Fruit', '1', '0'),
('26', '12', 'True', '1', '0'),
('27', '12', 'False', '0', '0'),
('28', '13', 'True', '1', '0'),
('29', '13', 'False', '0', '0');

CREATE TABLE `activity_questions` (
  `question_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `question_text` text NOT NULL,
  `question_type` enum('multiple_choice','true_false','short_answer') NOT NULL,
  `points` int(11) NOT NULL DEFAULT 1,
  `position` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`question_id`),
  KEY `activity_id` (`activity_id`),
  CONSTRAINT `activity_questions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_questions` (`question_id`, `activity_id`, `question_text`, `question_type`, `points`, `position`, `created_at`) VALUES
('1', '5', 'what is right?', 'multiple_choice', '1', '0', '2025-05-15 17:08:12'),
('4', '8', 'vcvcv', 'true_false', '1', '0', '2025-05-17 21:22:21'),
('10', '12', 'We can pass together.', 'true_false', '5', '0', '2025-05-18 23:17:34'),
('11', '17', 'What is Orange?', 'short_answer', '1', '0', '2025-05-18 23:43:02'),
('12', '18', 'jhjh', 'true_false', '1', '0', '2025-05-27 14:45:34'),
('13', '19', 'mhbmnm', 'true_false', '100', '0', '2025-05-27 14:52:25');

CREATE TABLE `activity_submissions` (
  `submission_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `submission_content` text DEFAULT NULL,
  `submission_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_late` tinyint(1) NOT NULL DEFAULT 0,
  `grade` float DEFAULT NULL,
  `feedback` text DEFAULT NULL,
  `graded_by` int(11) DEFAULT NULL,
  `graded_at` timestamp NULL DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  PRIMARY KEY (`submission_id`),
  KEY `activity_id` (`activity_id`),
  KEY `user_id` (`user_id`),
  KEY `graded_by` (`graded_by`),
  CONSTRAINT `activity_submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
  CONSTRAINT `activity_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `activity_submissions_ibfk_3` FOREIGN KEY (`graded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_submissions` (`submission_id`, `activity_id`, `user_id`, `submission_content`, `submission_date`, `is_late`, `grade`, `feedback`, `graded_by`, `graded_at`, `file_path`, `file_name`, `file_type`, `file_size`) VALUES
('5', '7', '12', NULL, '2025-05-18 16:17:33', '0', '100', NULL, NULL, '2025-05-18 16:17:33', NULL, NULL, NULL, NULL),
('6', '8', '12', '', '2025-05-18 16:17:46', '1', '100', NULL, NULL, '2025-05-18 16:17:46', NULL, NULL, NULL, NULL),
('7', '5', '12', '', '2025-05-18 23:15:02', '1', '100', NULL, NULL, '2025-05-18 23:15:02', NULL, NULL, NULL, NULL),
('8', '12', '12', '', '2025-05-19 11:46:11', '0', '0', NULL, NULL, '2025-05-19 11:46:11', NULL, NULL, NULL, NULL),
('9', '18', '12', '', '2025-05-27 14:45:59', '0', '0', NULL, NULL, '2025-05-27 14:45:59', NULL, NULL, NULL, NULL),
('10', '19', '12', '', '2025-05-27 14:52:42', '0', '0', NULL, NULL, '2025-05-27 14:52:42', NULL, NULL, NULL, NULL);

CREATE TABLE `announcement_comment_replies` (
  `reply_id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`reply_id`),
  KEY `comment_id` (`comment_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `announcement_comment_replies_ibfk_1` FOREIGN KEY (`comment_id`) REFERENCES `announcement_comments` (`comment_id`) ON DELETE CASCADE,
  CONSTRAINT `announcement_comment_replies_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `announcement_comment_replies` (`reply_id`, `comment_id`, `user_id`, `content`, `created_at`, `updated_at`) VALUES
('1', '1', '4', 'whats up', '2025-05-15 13:04:52', '2025-05-15 13:04:52'),
('2', '1', '12', 'sige', '2025-05-15 13:49:48', '2025-05-15 13:49:48'),
('3', '2', '4', 'ffff', '2025-05-18 13:36:15', '2025-05-18 13:36:15'),
('4', '2', '12', 'bbk', '2025-05-18 14:18:30', '2025-05-18 14:18:30'),
('5', '2', '12', 'dsdsd', '2025-05-19 11:43:30', '2025-05-19 11:43:30');

CREATE TABLE `announcement_comments` (
  `comment_id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`comment_id`),
  KEY `announcement_id` (`announcement_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `announcement_comments_ibfk_1` FOREIGN KEY (`announcement_id`) REFERENCES `announcements` (`announcement_id`) ON DELETE CASCADE,
  CONSTRAINT `announcement_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `announcement_comments` (`comment_id`, `announcement_id`, `user_id`, `content`, `created_at`, `updated_at`) VALUES
('1', '1', '4', '@everyone', '2025-05-15 13:03:37', '2025-05-15 13:03:37'),
('2', '3', '4', 'fbhfbg', '2025-05-18 13:36:09', '2025-05-18 13:36:09');

CREATE TABLE `announcement_files` (
  `file_id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `file_size` int(11) NOT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`file_id`),
  KEY `announcement_id` (`announcement_id`),
  CONSTRAINT `announcement_files_ibfk_1` FOREIGN KEY (`announcement_id`) REFERENCES `announcements` (`announcement_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `announcement_files` (`file_id`, `announcement_id`, `file_name`, `file_path`, `file_type`, `file_size`, `uploaded_at`) VALUES
('1', '3', 'gerald padilla.docx', 'uploads/announcements/3/682971bc1f539_gerald padilla.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '10832', '2025-05-18 13:35:56');

CREATE TABLE `announcements` (
  `announcement_id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `announcement_date` date DEFAULT curdate(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`announcement_id`),
  KEY `course_id` (`course_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `announcements_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`course_id`) ON DELETE CASCADE,
  CONSTRAINT `announcements_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `announcements` (`announcement_id`, `course_id`, `title`, `content`, `created_by`, `announcement_date`, `created_at`) VALUES
('1', '3', 'memo 105.s20', 'mga lego nata', '4', '2025-05-15', '2025-05-15 12:51:50'),
('3', '3', 'announcement 1', 'bbhbhbh', '4', '2025-05-18', '2025-05-18 13:35:56');

CREATE TABLE `assignment_submissions` (
  `submission_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `content` text DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `submission_date` datetime NOT NULL,
  `is_late` tinyint(1) DEFAULT 0,
  `grade` decimal(5,2) DEFAULT NULL,
  `feedback` text DEFAULT NULL,
  `graded_by` int(11) DEFAULT NULL,
  `graded_at` datetime DEFAULT NULL,
  PRIMARY KEY (`submission_id`),
  KEY `activity_id` (`activity_id`),
  KEY `user_id` (`user_id`),
  KEY `graded_by` (`graded_by`),
  CONSTRAINT `assignment_submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
  CONSTRAINT `assignment_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `assignment_submissions_ibfk_3` FOREIGN KEY (`graded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `assignments` (
  `assignment_id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `points` int(11) DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`assignment_id`),
  KEY `lesson_id` (`lesson_id`),
  CONSTRAINT `assignments_ibfk_1` FOREIGN KEY (`lesson_id`) REFERENCES `lessons` (`lesson_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `calendar_events` (
  `event_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `event_date` date NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `course` varchar(100) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `item_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('pending','done') DEFAULT 'pending',
  PRIMARY KEY (`event_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `calendar_events` (`event_id`, `user_id`, `title`, `description`, `event_date`, `event_type`, `course`, `course_id`, `item_id`, `created_at`, `updated_at`, `status`) VALUES
('2', '1', 'sample', 'qswdffg', '2025-05-16', 'report', 'Admin', NULL, NULL, '2025-05-14 21:06:48', '2025-05-14 21:17:29', 'pending'),
('3', '4', 'vcvc00', 'cxcxccx00', '2025-05-21', 'deadline', 'javanism (JAVA12)', '3', NULL, '2025-05-18 13:56:53', '2025-05-18 13:57:11', 'pending'),
('4', '12', 'fgfg', 'adsd?', '2025-05-21', 'assignment', 'Student Calendar', NULL, NULL, '2025-05-18 14:08:38', '2025-05-18 14:10:28', 'pending');

CREATE TABLE `course_instructors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `instructor_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_archived` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_course_instructor` (`course_id`,`instructor_id`),
  KEY `instructor_id` (`instructor_id`),
  CONSTRAINT `course_instructors_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`course_id`) ON DELETE CASCADE,
  CONSTRAINT `course_instructors_ibfk_2` FOREIGN KEY (`instructor_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `courses` (
  `course_id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `class_code` varchar(10) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `semester` enum('first','second') DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_archived` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`course_id`),
  UNIQUE KEY `class_code` (`class_code`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `courses` (`course_id`, `title`, `description`, `class_code`, `capacity`, `semester`, `created_by`, `is_active`, `created_at`, `updated_at`, `is_archived`) VALUES
('3', 'javanism', 'maka javawakis', 'JAVA12', NULL, 'first', '4', '1', '2025-05-15 11:16:34', '2025-05-19 11:48:59', '0'),
('4', 'information management', 'mag buhat tag system diri', '123456', '50', 'first', '4', '1', '2025-05-16 23:07:01', '2025-05-17 15:09:05', '0'),
('5', 'Objected Oriented Programming', 'Understanding structures of coding and it\'s logic and algorithm', 'SFQ92G', '50', 'first', '4', '1', '2025-05-17 13:19:14', '2025-05-17 15:08:18', '1'),
('6', 'web application and development', 'sddsdsd', 'HL65CV', '2', 'first', '1', '1', '2025-05-19 11:38:08', '2025-05-19 11:38:08', '0'),
('7', 'fdfdf', 'fdfdf', '5ZGSMB', '2', 'first', '4', '1', '2025-05-19 11:39:11', '2025-05-19 11:53:15', '1'),
('8', 'Introduction to Programming', 'Learn the basics of programming with Python', 'INT905', NULL, 'first', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0'),
('9', 'Mathematics for Computer Science', 'Essential mathematical concepts for CS students', 'MAT579', NULL, 'first', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0'),
('10', 'Digital Logic Design', 'Understanding digital circuits and logic gates', 'DIG756', NULL, 'first', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0'),
('11', 'Data Structures and Algorithms', 'Advanced programming concepts and problem solving', 'DAT634', NULL, 'second', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0'),
('12', 'Database Management Systems', 'Design and implementation of database systems', 'DAT852', NULL, 'second', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0'),
('13', 'Web Development', 'Building modern web applications', 'WEB126', NULL, 'second', '1', '1', '2025-05-27 15:46:11', '2025-05-27 15:46:11', '0');

CREATE TABLE `enrollment_requests` (
  `request_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `request_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `processed_by` int(11) DEFAULT NULL,
  `processed_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`request_id`),
  UNIQUE KEY `unique_request` (`user_id`,`course_id`),
  KEY `course_id` (`course_id`),
  KEY `processed_by` (`processed_by`),
  CONSTRAINT `enrollment_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `enrollment_requests_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`course_id`) ON DELETE CASCADE,
  CONSTRAINT `enrollment_requests_ibfk_3` FOREIGN KEY (`processed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `enrollment_requests` (`request_id`, `user_id`, `course_id`, `request_date`, `status`, `processed_by`, `processed_date`) VALUES
('1', '12', '7', '2025-05-19 11:51:57', 'approved', '4', '2025-05-19 11:52:21');

CREATE TABLE `enrollments` (
  `enrollment_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `enrollment_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `completion_status` enum('not_started','in_progress','completed') DEFAULT 'not_started',
  `is_archived` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`enrollment_id`),
  UNIQUE KEY `unique_enrollment` (`user_id`,`course_id`),
  KEY `course_id` (`course_id`),
  CONSTRAINT `enrollments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `enrollments_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `enrollments` (`enrollment_id`, `user_id`, `course_id`, `enrollment_date`, `completion_status`, `is_archived`) VALUES
('1', '12', '3', '2025-05-15 13:49:22', 'not_started', '0'),
('2', '12', '4', '2025-05-16 23:25:34', 'completed', '0'),
('3', '12', '7', '2025-05-19 11:52:21', 'completed', '1');

CREATE TABLE `forum_replies` (
  `reply_id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`reply_id`),
  KEY `topic_id` (`topic_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `forum_replies_ibfk_1` FOREIGN KEY (`topic_id`) REFERENCES `forum_topics` (`topic_id`) ON DELETE CASCADE,
  CONSTRAINT `forum_replies_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `forum_topics` (
  `topic_id` int(11) NOT NULL AUTO_INCREMENT,
  `forum_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`topic_id`),
  KEY `forum_id` (`forum_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `forum_topics_ibfk_1` FOREIGN KEY (`forum_id`) REFERENCES `forums` (`forum_id`) ON DELETE CASCADE,
  CONSTRAINT `forum_topics_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `module_activities` (
  `module_activity_id` int(11) NOT NULL AUTO_INCREMENT,
  `module_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `position` int(11) DEFAULT 0,
  PRIMARY KEY (`module_activity_id`),
  UNIQUE KEY `module_id` (`module_id`,`activity_id`),
  KEY `activity_id` (`activity_id`),
  CONSTRAINT `module_activities_ibfk_1` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE CASCADE,
  CONSTRAINT `module_activities_ibfk_2` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) NOT NULL,
  `related_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`notification_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notifications` (`notification_id`, `user_id`, `title`, `message`, `type`, `related_id`, `is_read`, `created_at`) VALUES
('1', '4', 'New Event Created', 'You created a new event: vcvc on May 21, 2025', 'event_creation', '3', '1', '2025-05-18 13:56:53'),
('2', '4', 'Event Updated', 'You updated the event: vcvc on May 21, 2025', 'event_update', '3', '1', '2025-05-18 13:57:03'),
('3', '4', 'Event Updated', 'You updated the event: vcvc00 on May 21, 2025', 'event_update', '3', '1', '2025-05-18 13:57:11'),
('4', '12', 'New Event Created', 'You created a new event: fgfg on May 21, 2025', 'event_creation', '4', '1', '2025-05-18 14:08:39'),
('5', '12', 'Event Updated', 'You updated the event: fgfg on May 21, 2025', 'event_update', '4', '0', '2025-05-18 14:10:28');

CREATE TABLE `password_reset` (
  `reset_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `reset_code` varchar(10) NOT NULL,
  `expiry_time` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`reset_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `password_reset_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `password_reset` (`reset_id`, `user_id`, `reset_code`, `expiry_time`, `created_at`) VALUES
('2', '11', '264581', '2025-05-16 17:02:57', '2025-05-16 22:02:57'),
('3', '1', '165012', '2025-05-28 10:27:00', '2025-05-27 16:26:45'),
('4', '15', '073120', '2025-05-28 10:36:31', '2025-05-27 16:32:00');

CREATE TABLE `quiz_answers` (
  `answer_id` int(11) NOT NULL AUTO_INCREMENT,
  `submission_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `answer_text` text DEFAULT NULL,
  `is_correct` tinyint(1) DEFAULT NULL,
  `points_earned` float DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`answer_id`),
  KEY `submission_id` (`submission_id`),
  KEY `question_id` (`question_id`),
  CONSTRAINT `quiz_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE,
  CONSTRAINT `quiz_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `quiz_questions` (`question_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `quiz_answers` (`answer_id`, `submission_id`, `question_id`, `answer_text`, `is_correct`, `points_earned`, `created_at`) VALUES
('7', '5', '6', 'cxc', '1', '1', '2025-05-18 16:17:33');

CREATE TABLE `quiz_attempts` (
  `attempt_id` int(11) NOT NULL AUTO_INCREMENT,
  `quiz_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` timestamp NULL DEFAULT NULL,
  `score` decimal(5,2) DEFAULT 0.00,
  PRIMARY KEY (`attempt_id`),
  KEY `quiz_id` (`quiz_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `quiz_attempts_ibfk_1` FOREIGN KEY (`quiz_id`) REFERENCES `quizzes` (`quiz_id`) ON DELETE CASCADE,
  CONSTRAINT `quiz_attempts_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `quiz_options` (
  `option_id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `option_text` text NOT NULL,
  `is_correct` tinyint(1) DEFAULT 0,
  `position` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`option_id`),
  KEY `question_id` (`question_id`),
  CONSTRAINT `quiz_options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `quiz_questions` (`question_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `quiz_options` (`option_id`, `question_id`, `option_text`, `is_correct`, `position`) VALUES
('7', '6', 'cxcx', '0', '0'),
('8', '6', 'cxc', '0', '0'),
('9', '6', 'cxc', '1', '0'),
('10', '6', 'cxc', '0', '0');

CREATE TABLE `quiz_questions` (
  `question_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `question_text` text NOT NULL,
  `question_type` enum('multiple_choice','true_false','short_answer') NOT NULL,
  `points` int(11) DEFAULT 1,
  `position` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`question_id`),
  KEY `quiz_questions_activity_fk` (`activity_id`),
  CONSTRAINT `quiz_questions_activity_fk` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `quiz_questions` (`question_id`, `activity_id`, `question_text`, `question_type`, `points`, `position`) VALUES
('6', '7', 'scxc', 'multiple_choice', '3', '0');

CREATE TABLE `quiz_responses` (
  `response_id` int(11) NOT NULL AUTO_INCREMENT,
  `attempt_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `option_id` int(11) DEFAULT NULL,
  `text_response` text DEFAULT NULL,
  `is_correct` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`response_id`),
  KEY `attempt_id` (`attempt_id`),
  KEY `question_id` (`question_id`),
  KEY `option_id` (`option_id`),
  CONSTRAINT `quiz_responses_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `quiz_attempts` (`attempt_id`) ON DELETE CASCADE,
  CONSTRAINT `quiz_responses_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `quiz_questions` (`question_id`) ON DELETE CASCADE,
  CONSTRAINT `quiz_responses_ibfk_3` FOREIGN KEY (`option_id`) REFERENCES `quiz_options` (`option_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `quiz_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `time_limit` int(11) DEFAULT 60,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  KEY `activity_id` (`activity_id`),
  CONSTRAINT `quiz_settings_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `quiz_settings` (`setting_id`, `activity_id`, `time_limit`, `created_at`, `updated_at`) VALUES
('1', '3', '60', '2025-05-15 15:29:25', '2025-05-15 15:29:25');

CREATE TABLE `roles` (
  `role_id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `role_name` (`role_name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `roles` (`role_id`, `role_name`, `description`, `created_at`) VALUES
('1', 'admin', 'Administrator with full system access', '2025-05-14 14:48:03'),
('2', 'teacher', 'Teacher who can create and manage courses', '2025-05-14 14:48:03'),
('3', 'student', 'Student who can enroll in courses', '2025-05-14 14:48:03');

CREATE TABLE `submissions` (
  `submission_id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `submission_content` text DEFAULT NULL,
  `submission_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_late` tinyint(1) DEFAULT 0,
  `grade` float DEFAULT NULL,
  `feedback` text DEFAULT NULL,
  `graded_by` int(11) DEFAULT NULL,
  `graded_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`submission_id`),
  KEY `activity_id` (`activity_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `submissions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE,
  CONSTRAINT `submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `submissions` (`submission_id`, `activity_id`, `user_id`, `submission_content`, `submission_date`, `is_late`, `grade`, `feedback`, `graded_by`, `graded_at`) VALUES
('1', '7', '12', NULL, '2025-05-18 16:17:33', '0', '100', NULL, NULL, '2025-05-18 16:17:33'),
('2', '8', '12', '', '2025-05-18 16:17:46', '1', '100', NULL, NULL, '2025-05-18 16:17:46'),
('3', '5', '12', '', '2025-05-18 23:15:02', '1', '100', NULL, NULL, '2025-05-18 23:15:02'),
('4', '12', '12', '', '2025-05-19 11:46:11', '0', '0', NULL, NULL, '2025-05-19 11:46:11');

CREATE TABLE `system_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_description`, `created_at`, `updated_at`) VALUES
('1', 'max_file_upload_size', '5', 'Maximum file upload size in MB', '2025-05-18 00:56:48', '2025-05-18 00:56:48'),
('2', 'allowed_file_types', 'image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/plain,application/zip,application/x-rar-compressed', 'Comma-separated list of allowed file MIME types', '2025-05-18 00:56:48', '2025-05-18 00:56:48'),
('3', 'site_name', 'E-Learning System', 'Name of the site', '2025-05-18 00:56:48', '2025-05-18 00:56:48'),
('4', 'site_description', 'A comprehensive e-learning platform', 'Description of the site', '2025-05-18 00:56:48', '2025-05-18 00:56:48'),
('5', 'admin_email', '<EMAIL>', 'Admin email address', '2025-05-18 00:56:48', '2025-05-18 00:56:48'),
('6', 'maintenance_mode', '0', 'Maintenance mode (0 = off, 1 = on)', '2025-05-18 00:56:48', '2025-05-18 00:56:48');

CREATE TABLE `user_notification_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT 1,
  `theme` varchar(20) DEFAULT 'light',
  `comments_on_posts` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `user_notification_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `user_notification_settings` (`setting_id`, `user_id`, `email_notifications`, `theme`, `comments_on_posts`) VALUES
('1', '1', '1', 'light', '1'),
('2', '2', '1', 'light', '1'),
('3', '3', '1', 'light', '1'),
('4', '4', '1', 'light', '1'),
('5', '9', '1', 'light', '1'),
('6', '10', '1', 'light', '1'),
('7', '11', '1', 'light', '1'),
('8', '12', '1', 'light', '1'),
('9', '13', '1', 'light', '1'),
('10', '14', '1', 'light', '1');

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `fk_role_id` (`role_id`),
  CONSTRAINT `fk_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `users` (`user_id`, `username`, `password`, `email`, `first_name`, `last_name`, `gender`, `birthday`, `phone_number`, `profile_picture`, `role_id`, `is_active`, `created_at`, `updated_at`, `last_login`) VALUES
('1', 'admin', '$2y$10$PJINfiF4GPc1LEv6RnmwG.WSYgA51UnK4DiYTEtn8EGPU2OCGG0Xu', '<EMAIL>', 'System', 'Administrator', 'female', '2004-12-25', '09855317641', 'uploads/profile_pics/profile_1_1747548475.png', '1', '1', '2025-05-14 14:48:03', '2025-05-27 16:09:25', '2025-05-27 16:09:25'),
('2', 'newadmin', '$2y$10$rbcSLvQuYD63AY8jFgsyf.B60xkA8n/1BcaLRAPvvUJsxR58vRrwu', '<EMAIL>', 'New', 'Admin', NULL, NULL, NULL, NULL, '1', '1', '2025-05-14 16:03:34', '2025-05-27 16:09:25', '2025-04-29 16:09:25'),
('3', 'simpleadmin', '$2y$10$1x/bdnNKq7dcLS4m/skLiugcY.D9fj67odc0N5SzWH5TGHKru/jmi', '<EMAIL>', 'Simple', 'Admin', NULL, NULL, NULL, NULL, '1', '1', '2025-05-14 16:07:39', '2025-05-27 16:09:25', '2025-05-19 16:09:25'),
('4', 'instructor', '$2y$10$SDIIqLWGkP9viCtsrTuBUOIe6B/mcF76psNHyRBWfjIQz6PVUd4.6', '<EMAIL>', 'Maxcel Jane', 'Narisma', 'female', '2004-12-25', '09855317641', 'uploads/profile_pics/profile_4_1747548388.png', '2', '1', '2025-05-14 20:02:16', '2025-05-27 16:27:41', '2025-05-27 16:27:41'),
('9', 'kate', '0', '<EMAIL>', 'Kate', 'Asion', NULL, NULL, NULL, NULL, '3', '1', '2025-05-15 09:32:01', '2025-05-27 16:09:25', '2025-05-15 16:09:25'),
('10', 'testuser9690', '$2y$10$dbUS5e.Vkm.ViQK1mm4NSeNYd8GwRnIR3OR7e0N.hB7rgmHmY8l.S', '<EMAIL>', 'Test', 'User', NULL, NULL, NULL, NULL, '3', '1', '2025-05-15 09:34:51', '2025-05-27 16:09:25', '2025-05-24 16:09:25'),
('11', 'student', '$2y$10$MQDoM/qCsyf2bz6Z3W2aV.UnrkoLq54Wr4W7DHeNWTN45hGoKlUN6', '<EMAIL>', 'Student', 'User', NULL, NULL, NULL, NULL, '3', '1', '2025-05-15 09:37:34', '2025-05-27 16:09:25', '2025-05-16 16:09:25'),
('12', 'louie', '$2y$10$k.mDLO6g9Zqp7IMaPxzhGOJzg3T4PfiGeXWvvBc9GfmhbYpZwJjYm', '<EMAIL>', 'Louie', 'Abadines', 'male', '2028-06-29', '09855317641', 'uploads/profile_pics/profile_12_1747443267.jpg', '3', '1', '2025-05-15 09:39:29', '2025-05-27 16:09:25', '2025-05-13 16:09:25'),
('13', 'janine', '$2y$10$DVIsT27mKGkqHh2juinyLu5ebzvrHaf6yD7jpMsugOPkXhWIa/kty', '<EMAIL>', 'Janine', 'Coscos', 'female', '2025-05-19', '09633773011', NULL, '3', '1', '2025-05-16 20:49:21', '2025-05-27 16:09:25', '2025-05-21 16:09:25'),
('14', 'maxceljane.narisma', '$2y$10$OHdG2D9BkilHDTNO4pUec.GPB1nMJNa7ap1omwAgGaTrbPuwwoWk6', '<EMAIL>', 'Mj', 'Narisma', 'female', '2025-06-10', '09633773011', NULL, '3', '1', '2025-05-16 21:55:58', '2025-05-27 16:09:25', '2025-05-06 16:09:25'),
('15', 'berhel', '$2y$10$eSkWJpWSXmHcMLtQwaAAlOkkrx7B5WwTsSfvX8whTH6WzbaaoegLq', '<EMAIL>', 'Vgwapo', 'Labarete', 'other', '2007-10-17', '09103875180', NULL, '3', '1', '2025-05-27 16:31:46', '2025-05-27 16:31:46', NULL);

SET FOREIGN_KEY_CHECKS = 1;
