<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "You must be logged in as an admin to run this script.";
    exit;
}

// Create the activity_submissions table if it doesn't exist
try {
    $sql = "
    CREATE TABLE IF NOT EXISTS activity_submissions (
        submission_id INT AUTO_INCREMENT PRIMARY KEY,
        activity_id INT NOT NULL,
        user_id INT NOT NULL,
        submission_date DATETIME NOT NULL,
        is_late BOOLEAN DEFAULT FALSE,
        grade DECIMAL(5,2) DEFAULT NULL,
        feedback TEXT DEFAULT NULL,
        file_path VARCHAR(255) DEFAULT NULL,
        file_name VARCHAR(255) DEFAULT NULL,
        submission_link VARCHAR(255) DEFAULT NULL,
        submission_text TEXT DEFAULT NULL,
        FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE KEY unique_submission (activity_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "Table 'activity_submissions' created successfully or already exists.";
} catch (PDOException $e) {
    echo "Error creating table: " . $e->getMessage();
}
?>
