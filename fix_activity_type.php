<?php
// Include configuration file
require_once 'includes/config.php';

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "No activity ID provided.";
    exit;
}

$activityId = intval($_GET['id']);

try {
    // Get current activity details
    $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        echo "Activity not found.";
        exit;
    }
    
    $activity = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentType = $activity['activity_type'];
    
    echo "<h2>Activity Details</h2>";
    echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
    echo "<p><strong>Current Type:</strong> " . htmlspecialchars($currentType) . "</p>";
    
    // Update the activity type to 'activity'
    $newType = 'activity';
    $updateStmt = $pdo->prepare("UPDATE activities SET activity_type = :newType WHERE activity_id = :activityId");
    $updateStmt->bindParam(':newType', $newType);
    $updateStmt->bindParam(':activityId', $activityId);
    
    if ($updateStmt->execute()) {
        echo "<p style='color: green;'>Activity type updated from '{$currentType}' to '{$newType}'.</p>";
        
        // Check if the activity_type enum includes 'activity'
        $stmt = $pdo->query("SHOW COLUMNS FROM activities LIKE 'activity_type'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column) {
            $type = $column['Type'];
            echo "<p>Current activity_type definition: " . htmlspecialchars($type) . "</p>";
            
            // Check if 'activity' is already in the enum
            if (strpos($type, "'activity'") === false) {
                // Add 'activity' to the enum
                $newType = str_replace("enum('material','assignment','quiz','question')", "enum('material','assignment','quiz','question','activity')", $type);
                
                // Alter the table
                $pdo->exec("ALTER TABLE activities MODIFY COLUMN activity_type " . $newType);
                
                echo "<p>✓ Successfully added 'activity' to the activity_type enum.</p>";
            } else {
                echo "<p>The 'activity' type is already included in the activity_type enum.</p>";
            }
        }
        
        echo "<p>Please <a href='activity_edit.php?id=$activityId#questions'>click here</a> to go to the edit page with the Questions tab active.</p>";
    } else {
        echo "<p style='color: red;'>Error updating activity type.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
