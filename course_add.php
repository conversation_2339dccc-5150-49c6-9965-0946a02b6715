<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is authorized to create courses
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You are not authorized to create courses.";
    header("location: index.php");
    exit;
}

// Get all teachers for admin
$teachers = [];
if (isAdmin()) {
    $teachers = getUsersByRole('teacher');
    if (is_string($teachers)) {
        $teacherError = $teachers;
        $teachers = [];
    }
}

// Initialize variables
$title = $description = "";
$title_err = $description_err = $class_code_err = $capacity_err = $semester_err = "";
$success = "";
$courseId = 0;

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title for the course.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description for the course.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Get class code if provided
    $classCode = null;
    if (isset($_POST["class_code"]) && !empty(trim($_POST["class_code"]))) {
        $classCode = strtoupper(trim($_POST["class_code"]));
        // Validate class code format (alphanumeric, 6 characters)
        if (!preg_match('/^[A-Z0-9]{6}$/', $classCode)) {
            $class_code_err = "Class code must be 6 alphanumeric characters.";
        }
    }

    // Get capacity if provided
    $capacity = null;
    if (isset($_POST["capacity"]) && !empty(trim($_POST["capacity"]))) {
        $capacity = (int)trim($_POST["capacity"]);
        // Validate capacity (must be a positive integer)
        if ($capacity <= 0) {
            $capacity_err = "Capacity must be a positive number.";
        }
    }

    // Get semester if provided
    $semester = null;
    if (isset($_POST["semester"]) && !empty(trim($_POST["semester"]))) {
        $semester = trim($_POST["semester"]);
        // Validate semester (must be 'first' or 'second')
        if (!in_array($semester, ['first', 'second'])) {
            $semester_err = "Semester must be either 'First Semester' or 'Second Semester'.";
        }
    }

    // Check input errors before creating the course
    if (empty($title_err) && empty($description_err) && empty($class_code_err) && empty($capacity_err) && empty($semester_err)) {
        // Create the course
        $result = createCourse($title, $description, $_SESSION['user_id'], $classCode, $capacity, $semester);

        if (is_numeric($result)) {
            $courseId = $result;

            // If admin is creating the course and selected instructors
            if (isAdmin() && isset($_POST['instructors']) && is_array($_POST['instructors'])) {
                foreach ($_POST['instructors'] as $instructorId) {
                    $assignResult = assignInstructor($courseId, $instructorId);
                    if ($assignResult !== true) {
                        // Log error but continue
                        error_log("Failed to assign instructor $instructorId to course $courseId: $assignResult");
                    }
                }
            }

            // Course created successfully
            $success = "Course created successfully!";
            // Clear form fields
            $title = $description = "";
        } else {
            // Error occurred
            $error = $result;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Course - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/classroom.css">
</head>
<body>
    <!-- Header -->
    <header class="classroom-header">
        <a href="index.php" class="classroom-logo">
            <i class="fas fa-graduation-cap"></i>
            <?php echo APP_NAME; ?>
        </a>

        <div class="classroom-header-right">
            <button class="btn-icon" title="Help">
                <i class="fas fa-question-circle"></i>
            </button>
            <div class="user-avatar" title="<?php echo htmlspecialchars($_SESSION["username"]); ?>">
                <?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?>
            </div>
        </div>
    </header>

    <div class="classroom-container">
        <!-- Sidebar -->
        <aside class="classroom-sidebar">
            <ul class="classroom-sidebar-nav">
                <li class="classroom-sidebar-item">
                    <a href="index.php" class="classroom-sidebar-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="calendar.php" class="classroom-sidebar-link">
                        <i class="fas fa-calendar-alt"></i> Calendar
                    </a>
                </li>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="courses.php" class="classroom-sidebar-link active">
                        <i class="fas fa-book"></i> Courses
                    </a>
                </li>
                <?php endif; ?>
                <?php if (isAdmin()): ?>
                <li class="classroom-sidebar-item">
                    <a href="users.php" class="classroom-sidebar-link">
                        <i class="fas fa-users"></i> Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="classroom-sidebar-item">
                    <a href="profile.php" class="classroom-sidebar-link">
                        <i class="fas fa-user"></i> Profile
                    </a>
                </li>
                <li class="classroom-sidebar-item">
                    <a href="logout.php" class="classroom-sidebar-link">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main content -->
        <main class="classroom-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Create Course</h1>
            </div>

            <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
                <div class="mt-2">
                    <a href="index.php" class="btn-classroom-outline btn-sm">Go to Home</a>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <div class="form-group">
                            <label for="title">Course Name <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                            <span class="invalid-feedback"><?php echo $title_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea name="description" id="description" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" rows="4"><?php echo $description; ?></textarea>
                            <span class="invalid-feedback"><?php echo $description_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label for="class_code">Class Code</label>
                            <div class="input-group">
                                <input type="text" name="class_code" id="class_code" class="form-control <?php echo (!empty($class_code_err)) ? 'is-invalid' : ''; ?>" placeholder="Leave blank to auto-generate" maxlength="6">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="generateCode">
                                        <i class="fas fa-sync-alt"></i> Generate
                                    </button>
                                </div>
                            </div>
                            <span class="invalid-feedback"><?php echo $class_code_err; ?></span>
                            <small class="form-text text-muted">Students can use this code to join your course. A random code will be generated if left blank.</small>
                        </div>

                        <div class="form-group">
                            <label for="capacity">Slot/Capacity</label>
                            <input type="number" name="capacity" id="capacity" class="form-control <?php echo (!empty($capacity_err)) ? 'is-invalid' : ''; ?>" placeholder="Maximum number of students" min="1">
                            <span class="invalid-feedback"><?php echo $capacity_err; ?></span>
                            <small class="form-text text-muted">Maximum number of students who can enroll in this course. Leave blank for unlimited.</small>
                        </div>

                        <div class="form-group">
                            <label for="semester">Semester</label>
                            <select name="semester" id="semester" class="form-control <?php echo (!empty($semester_err)) ? 'is-invalid' : ''; ?>">
                                <option value="">Select Semester</option>
                                <option value="first">First Semester</option>
                                <option value="second">Second Semester</option>
                            </select>
                            <span class="invalid-feedback"><?php echo $semester_err; ?></span>
                        </div>

                        <?php if (isAdmin() && count($teachers) > 0): ?>
                        <div class="form-group">
                            <label>Assign Instructors</label>
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <?php foreach ($teachers as $teacher): ?>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="instructor_<?php echo $teacher['user_id']; ?>" name="instructors[]" value="<?php echo $teacher['user_id']; ?>">
                                    <label class="custom-control-label" for="instructor_<?php echo $teacher['user_id']; ?>">
                                        <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>
                                        <small class="text-muted">(<?php echo htmlspecialchars($teacher['email']); ?>)</small>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <small class="form-text text-muted">Select teachers who will be instructors for this course.</small>
                        </div>
                        <?php endif; ?>
                        <div class="form-group text-right">
                            <a href="index.php" class="btn-classroom-outline mr-2">Cancel</a>
                            <button type="submit" class="btn-classroom">Create</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
    // Function to generate a random class code
    function generateRandomCode(length = 6) {
        const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let code = '';

        for (let i = 0; i < length; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
        }

        return code;
    }

    // Handle generate code button click
    document.getElementById('generateCode').addEventListener('click', function() {
        document.getElementById('class_code').value = generateRandomCode();
    });
    </script>
</body>
</html>
