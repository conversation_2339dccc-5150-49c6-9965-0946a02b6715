-- Add last_login column to users table for tracking user activity
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL DEFAULT NULL;

-- Update existing users to have a recent last_login for testing purposes
-- This sets all active users to have logged in within the last month
UPDATE users 
SET last_login = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)
WHERE is_active = 1;

-- Update admin user to have logged in recently
UPDATE users 
SET last_login = NOW() 
WHERE username = 'admin';
