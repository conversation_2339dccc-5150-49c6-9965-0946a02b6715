/* Text Editor Styles */
.text-formatting-toolbar {
    display: flex;
    background-color: #f8f9fa;
    border: 1px solid #dadce0;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.text-formatting-toolbar .btn {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    background-color: #ffffff;
    border: 1px solid #dadce0;
    color: #5f6368;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.1);
}

.text-formatting-toolbar .btn:hover {
    background-color: #f1f3f4;
    border-color: #dadce0;
    box-shadow: 0 1px 3px rgba(60, 64, 67, 0.2);
}

.text-formatting-toolbar .btn:active,
.text-formatting-toolbar .btn.active {
    background-color: #e8eaed;
    border-color: #4285f4;
    color: #4285f4;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
}

.text-formatting-toolbar .btn-bold {
    font-weight: bold;
}

.text-formatting-toolbar .btn-italic {
    font-style: italic;
}

.text-formatting-toolbar .btn-underline {
    text-decoration: underline;
}

/* Formatted output styles */
#formatted-output {
    min-height: 100px;
    background-color: #ffffff !important;
    border: 1px solid #dadce0 !important;
    padding: 15px !important;
    overflow-wrap: break-word;
}

#formatted-output strong {
    font-weight: bold;
    color: #1a73e8;
}

#formatted-output em {
    font-style: italic;
    color: #0f9d58;
}

#formatted-output u {
    text-decoration: underline;
    color: #4285f4;
}

#formatted-output del {
    text-decoration: line-through;
    color: #db4437;
}

#formatted-output ul.formatted-list {
    padding-left: 20px;
    margin-bottom: 10px;
}

#formatted-output ul.formatted-list li {
    margin-bottom: 5px;
}

/* Dark mode support */
body.dark-mode .text-formatting-toolbar {
    background-color: #292a2d;
    border-color: #5f6368;
}

body.dark-mode .text-formatting-toolbar .btn {
    color: #9aa0a6;
    background-color: #3c4043;
    border-color: #5f6368;
}

body.dark-mode .text-formatting-toolbar .btn:hover {
    background-color: #3c4043;
    border-color: #4285f4;
    color: #e8eaed;
}

body.dark-mode .text-formatting-toolbar .btn:active,
body.dark-mode .text-formatting-toolbar .btn.active {
    background-color: #3c4043;
    border-color: #4285f4;
    color: #4285f4;
}

body.dark-mode #formatted-output {
    background-color: #3c4043 !important;
    border-color: #5f6368 !important;
    color: #e8eaed;
}

body.dark-mode #formatted-output strong {
    color: #8ab4f8;
}

body.dark-mode #formatted-output em {
    color: #81c995;
}

body.dark-mode #formatted-output u {
    color: #8ab4f8;
}

body.dark-mode #formatted-output del {
    color: #f28b82;
}

body.dark-mode .table {
    color: #e8eaed;
}

body.dark-mode code {
    background-color: #3c4043;
    color: #e8eaed;
}
