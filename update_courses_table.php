<?php
// Include configuration file
require_once 'includes/config.php';

// Get the database connection
global $pdo;

// Add capacity and semester columns to courses table if they don't exist
try {
    // Check if the capacity column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM courses LIKE 'capacity'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $stmt = $pdo->prepare("ALTER TABLE courses ADD COLUMN capacity INT DEFAULT NULL AFTER class_code");
        $stmt->execute();
        echo "Added capacity column to courses table.<br>";
    } else {
        echo "capacity column already exists in courses table.<br>";
    }
    
    // Check if the semester column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM courses LIKE 'semester'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $stmt = $pdo->prepare("ALTER TABLE courses ADD COLUMN semester ENUM('first', 'second') DEFAULT NULL AFTER capacity");
        $stmt->execute();
        echo "Added semester column to courses table.<br>";
    } else {
        echo "semester column already exists in courses table.<br>";
    }
    
    echo "Database update completed successfully.";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
