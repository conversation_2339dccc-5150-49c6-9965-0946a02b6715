<?php
// Include configuration file
require_once 'includes/config.php';

// Create announcements table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS announcements (
            announcement_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            created_by INT NOT NULL,
            announcement_date DATE DEFAULT CURRENT_DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");
    
    echo "Announcements table created or already exists.<br>";
    
    // Create announcement_files table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS announcement_files (
            file_id INT AUTO_INCREMENT PRIMARY KEY,
            announcement_id INT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(255) NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            file_size INT NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE
        )
    ");
    
    echo "Announcement files table created or already exists.<br>";
    
    // Create uploads directory if it doesn't exist
    $uploadDir = 'uploads/announcements';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
        echo "Uploads directory created.<br>";
    } else {
        echo "Uploads directory already exists.<br>";
    }
    
    echo "Setup completed successfully!";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
