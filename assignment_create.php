<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/assessment_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher()) {
    $_SESSION['error'] = "Only teachers can create assignments.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized for this course
if ($course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId) && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to create assignments for this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Get course modules for dropdown
$modules = getCourseModules($courseId);
if (is_string($modules)) {
    $moduleError = $modules;
    $modules = [];
}

// Initialize variables
$title = $description = $dueDate = $points = $moduleId = "";
$allowLateSubmissions = false;
$title_err = $description_err = $dueDate_err = $points_err = $moduleId_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate description
    if (empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description.";
    } else {
        $description = trim($_POST["description"]);
    }

    // Validate due date (optional)
    if (!empty(trim($_POST["due_date"]))) {
        $dueDate = trim($_POST["due_date"]);

        // Check if date is valid
        $dateTime = DateTime::createFromFormat('Y-m-d\TH:i', $dueDate);
        if (!$dateTime || $dateTime->format('Y-m-d\TH:i') !== $dueDate) {
            $dueDate_err = "Please enter a valid date and time.";
        }
    }

    // Points will be set per question later
    $points = 0; // Default to 0

    // Validate module ID (optional)
    if (!empty($_POST["module_id"])) {
        $moduleId = intval($_POST["module_id"]);

        // Check if module exists and belongs to this course
        $moduleExists = false;
        foreach ($modules as $module) {
            if ($module['module_id'] == $moduleId) {
                $moduleExists = true;
                break;
            }
        }

        if (!$moduleExists) {
            $moduleId_err = "Invalid module selected.";
        }
    }

    // Check if late submissions are allowed
    $allowLateSubmissions = isset($_POST["allow_late_submissions"]) ? true : false;

    // File uploads have been removed

    // Check input errors before creating the assignment
    if (empty($title_err) && empty($description_err) && empty($dueDate_err) && empty($points_err) && empty($moduleId_err)) {
        // Create the assignment
        $result = createAssignment($courseId, $title, $description, $points, $dueDate, $moduleId, $allowLateSubmissions, $_SESSION['user_id'], null);

        if (is_numeric($result)) {
            // Assignment created successfully
            $_SESSION['success'] = "Assignment created successfully.";
            header("location: course_view_full.php?id=$courseId&tab=classwork");
            exit;
        } else {
            // Error creating assignment
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Create Assignment";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Assignment</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create an assignment for students to complete and submit.</p>
        </div>
    </div>
</div>

<!-- Assignment form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>

            <div class="form-group">
                <label for="description">Instructions</label>
                <textarea name="description" id="description" rows="6" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>"><?php echo $description; ?></textarea>
                <span class="invalid-feedback"><?php echo $description_err; ?></span>
            </div>

            <!-- Points will be set per question later -->

            <div class="form-group">
                <label for="due_date">Due Date (optional)</label>
                <input type="datetime-local" name="due_date" id="due_date" class="form-control <?php echo (!empty($dueDate_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $dueDate; ?>">
                <span class="invalid-feedback"><?php echo $dueDate_err; ?></span>
                <small class="form-text text-muted">If no due date is set, the assignment will be available indefinitely.</small>
            </div>

            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="allow_late_submissions" name="allow_late_submissions" <?php echo $allowLateSubmissions ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="allow_late_submissions">Allow late submissions</label>
                    <small class="form-text text-muted">If checked, students can submit after the due date, but submissions will be marked as late.</small>
                </div>
            </div>



            <div class="form-group">
                <label>Create Questions</label>
                <div class="alert alert-info">
                    <p class="mb-0">After creating this assignment, you'll be able to add questions of the following types:</p>
                    <ul class="mt-2 mb-0">
                        <li><strong>Multiple Choice</strong> - Students select one correct answer from several options</li>
                        <li><strong>True/False</strong> - Students select whether a statement is true or false</li>
                        <li><strong>Short Answer</strong> - Students type in a brief text response</li>
                    </ul>
                    <p class="mt-2 mb-0"><strong>Note:</strong> You'll be able to set points for each question individually after creating the assignment.</p>
                </div>
                <small class="form-text text-muted mt-2">You'll be redirected to the question editor after creating this assignment.</small>
            </div>

            <!-- No module field needed -->

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Create Assignment</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>



<?php
// Include footer
require_once 'includes/footer.php';
?>
