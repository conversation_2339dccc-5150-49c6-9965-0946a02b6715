<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/announcement_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher()) {
    $_SESSION['error'] = "Only teachers can create announcements.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized for this course
if ($course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId) && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to create announcements for this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Initialize variables
$title = $content = "";
$announcementDate = date('Y-m-d'); // Default to today
$title_err = $content_err = $file_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }

    // Validate content
    if (empty(trim($_POST["content"]))) {
        $content_err = "Please enter content.";
    } else {
        $content = trim($_POST["content"]);
    }

    // Validate announcement date (optional)
    if (isset($_POST["announcement_date"]) && !empty($_POST["announcement_date"])) {
        $announcementDate = $_POST["announcement_date"];

        // Validate date format
        $date = DateTime::createFromFormat('Y-m-d', $announcementDate);
        if (!$date || $date->format('Y-m-d') !== $announcementDate) {
            $date_err = "Invalid date format.";
        }
    } else {
        $announcementDate = date('Y-m-d'); // Default to today
    }

    // Validate file uploads (if any)
    if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'text/plain', 'application/zip', 'application/x-rar-compressed'];

        $maxFileSize = 5 * 1024 * 1024; // 5MB

        foreach ($_FILES['files']['name'] as $key => $name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                // Check file type
                if (!in_array($_FILES['files']['type'][$key], $allowedTypes)) {
                    $file_err = "Invalid file type. Allowed types: images, PDF, Office documents, text, zip, and rar.";
                    break;
                }

                // Check file size
                if ($_FILES['files']['size'][$key] > $maxFileSize) {
                    $file_err = "File size exceeds the maximum limit of 5MB.";
                    break;
                }
            } elseif ($_FILES['files']['error'][$key] !== UPLOAD_ERR_NO_FILE) {
                $file_err = getFileUploadError($_FILES['files']['error'][$key]);
                break;
            }
        }
    }

    // Check input errors before creating the announcement
    if (empty($title_err) && empty($content_err) && empty($file_err) && empty($date_err)) {
        // Create the announcement
        $result = createAnnouncement($courseId, $title, $content, $_SESSION['user_id'], $_FILES['files'] ?? null, $announcementDate);

        if (is_numeric($result)) {
            // Announcement created successfully
            $_SESSION['success'] = "Announcement created successfully.";
            header("location: course_view_full.php?id=$courseId&tab=stream");
            exit;
        } else {
            // Error creating announcement
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Create Announcement";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=stream" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Announcement</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create an announcement to share important information with your students.</p>
        </div>
    </div>
</div>

<!-- Announcement form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
            </div>

            <div class="form-group">
                <label for="content">Content</label>
                <textarea name="content" id="content" rows="6" class="form-control <?php echo (!empty($content_err)) ? 'is-invalid' : ''; ?>"><?php echo $content; ?></textarea>
                <span class="invalid-feedback"><?php echo $content_err; ?></span>
            </div>

            <div class="form-group">
                <label for="announcement_date">Date (Optional)</label>
                <input type="date" name="announcement_date" id="announcement_date" class="form-control <?php echo (!empty($date_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $announcementDate; ?>">
                <span class="invalid-feedback"><?php echo $date_err ?? ''; ?></span>
                <small class="form-text text-muted">Leave blank to use today's date.</small>
            </div>

            <div class="form-group">
                <label for="files">Attachments (Optional)</label>
                <div class="custom-file">
                    <input type="file" name="files[]" id="files" class="custom-file-input <?php echo (!empty($file_err)) ? 'is-invalid' : ''; ?>" multiple>
                    <label class="custom-file-label" for="files">Choose files...</label>
                    <span class="invalid-feedback"><?php echo $file_err ?? ''; ?></span>
                </div>
                <small class="form-text text-muted">
                    Allowed file types: images, PDF, Office documents, text, zip, and rar. Maximum size: 5MB per file.
                </small>
            </div>

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Post Announcement</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=stream" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<!-- Add JavaScript for file input -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update file input label with selected file names
    document.getElementById('files').addEventListener('change', function(e) {
        var fileName = '';
        if (this.files && this.files.length > 1) {
            fileName = (this.files.length) + ' files selected';
        } else if (this.files && this.files.length === 1) {
            fileName = this.files[0].name;
        }

        // Find the label element
        var label = document.querySelector('label.custom-file-label');
        if (label) {
            if (fileName) {
                label.innerHTML = fileName;
            } else {
                label.innerHTML = 'Choose files...';
            }
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
