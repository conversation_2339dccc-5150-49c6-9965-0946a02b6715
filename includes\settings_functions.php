<?php
/**
 * Settings Management Functions
 *
 * This file contains functions related to system settings.
 */

require_once 'config.php';

/**
 * Function to get a system setting
 *
 * @param string $key The setting key
 * @param mixed $default The default value if setting not found
 * @return mixed The setting value or default if not found
 */
function getSetting($key, $default = null) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT setting_value
            FROM system_settings
            WHERE setting_key = :key
        ");
        $stmt->bindParam(':key', $key);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $result = $stmt->fetch();
            return $result['setting_value'];
        } else {
            return $default;
        }
    } catch (PDOException $e) {
        // Log error
        error_log("Failed to get setting: " . $e->getMessage());
        return $default;
    }
}

/**
 * Function to update a system setting
 *
 * @param string $key The setting key
 * @param string $value The setting value
 * @param string $description Optional description
 * @return bool|string True if update successful, error message otherwise
 */
function updateSetting($key, $value, $description = null) {
    global $pdo;

    try {
        // Check if setting exists
        $stmt = $pdo->prepare("
            SELECT setting_id
            FROM system_settings
            WHERE setting_key = :key
        ");
        $stmt->bindParam(':key', $key);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            // Update existing setting
            if ($description !== null) {
                $stmt = $pdo->prepare("
                    UPDATE system_settings
                    SET setting_value = :value, setting_description = :description
                    WHERE setting_key = :key
                ");
                $stmt->bindParam(':description', $description);
            } else {
                $stmt = $pdo->prepare("
                    UPDATE system_settings
                    SET setting_value = :value
                    WHERE setting_key = :key
                ");
            }
        } else {
            // Create new setting
            $stmt = $pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value, setting_description)
                VALUES (:key, :value, :description)
            ");
            $stmt->bindParam(':description', $description);
        }

        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to update setting: " . $e->getMessage();
    }
}

/**
 * Function to get all system settings
 *
 * @return array|string Array of settings if successful, error message otherwise
 */
function getAllSettings() {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT setting_id, setting_key, setting_value, setting_description, created_at, updated_at
            FROM system_settings
            ORDER BY setting_key
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve settings: " . $e->getMessage();
    }
}

/**
 * Function to delete a system setting
 *
 * @param string $key The setting key
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteSetting($key) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            DELETE FROM system_settings
            WHERE setting_key = :key
        ");
        $stmt->bindParam(':key', $key);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Setting not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete setting: " . $e->getMessage();
    }
}

/**
 * Function to get the maximum file upload size
 *
 * @return int The maximum file upload size in bytes
 */
function getMaxFileUploadSize() {
    // Default to 10GB (10240MB)
    $maxSize = getSetting('max_file_upload_size', 10240);
    return $maxSize * 1024 * 1024; // Convert to bytes
}

/**
 * Function to get allowed file types
 *
 * @return array Array of allowed file MIME types
 */
function getAllowedFileTypes() {
    // Default allowed file types: PDF, Office documents, images, zip
    $defaultTypes = 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,';
    $defaultTypes .= 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,';
    $defaultTypes .= 'application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,';
    $defaultTypes .= 'image/jpeg,image/png,application/zip';

    $types = getSetting('allowed_file_types', $defaultTypes);
    return explode(',', $types);
}
?>
