<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "Test Sidebar Changes";
$current_page = "test";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Testing Sidebar Changes</h1>
            
            <div class="alert alert-info">
                <h4>Changes Made:</h4>
                <p>The "Courses" link has been removed from the sidebar for instructors.</p>
                <p>This change was made in the following files:</p>
                <ul>
                    <li>includes/header.php - Main sidebar</li>
                    <li>course_add.php - Classroom sidebar</li>
                    <li>course_edit.php - Classroom sidebar</li>
                    <li>assignment_view.php - Classroom sidebar</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>Current User Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                    <p><strong>Name:</strong> <?php echo $_SESSION['first_name'] . ' ' . $_SESSION['last_name']; ?></p>
                    <p><strong>Role:</strong> <?php echo ucfirst($_SESSION['role']); ?></p>
                    <p><strong>Is Admin:</strong> <?php echo isAdmin() ? 'Yes' : 'No'; ?></p>
                    <p><strong>Is Teacher:</strong> <?php echo isTeacher() ? 'Yes' : 'No'; ?></p>
                    <p><strong>Is Student:</strong> <?php echo isStudent() ? 'Yes' : 'No'; ?></p>
                </div>
            </div>
            
            <div class="mt-4">
                <h3>Sidebar Verification</h3>
                <p>Please check the sidebar on the left. If you are logged in as an instructor, you should NOT see a "Courses" link in the sidebar.</p>
                <p>If you are logged in as an admin, you should still see the "Courses" link.</p>
            </div>
            
            <div class="mt-4">
                <h3>Other Pages to Check</h3>
                <p>Please also verify the sidebar on these pages:</p>
                <ul>
                    <li><a href="course_add.php" class="btn btn-outline-primary">Course Add Page</a></li>
                    <li><a href="course_edit.php?id=1" class="btn btn-outline-primary">Course Edit Page</a></li>
                    <li><a href="assignment_view.php?id=1" class="btn btn-outline-primary">Assignment View Page</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
