<?php
// Direct login script - bypasses the regular login form
require_once 'includes/config.php';
global $pdo;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Admin credentials
$username = 'admin';

try {
    // Get user data directly
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, r.role_name 
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE u.username = :username AND u.is_active = 1
    ");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() == 1) {
        $user = $stmt->fetch();
        
        // Store data in session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['first_name'] = $user['first_name'];
        $_SESSION['last_name'] = $user['last_name'];
        $_SESSION['role'] = $user['role_name'];
        
        echo "You have been logged in as admin!<br>";
        echo "Redirecting to dashboard in 3 seconds...<br>";
        echo "<script>setTimeout(function() { window.location.href = 'index.php'; }, 3000);</script>";
    } else {
        echo "Admin user not found or not active.<br>";
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}
?>
