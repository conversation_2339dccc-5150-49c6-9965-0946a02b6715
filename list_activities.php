<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>All Activities</h1>";

try {
    // Get all activities
    $stmt = $pdo->query("SELECT activity_id, title, activity_type, course_id FROM activities ORDER BY activity_id");
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($activities) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Course ID</th><th>Actions</th></tr>";

        foreach ($activities as $activity) {
            echo "<tr>";
            echo "<td>" . $activity['activity_id'] . "</td>";
            echo "<td>" . htmlspecialchars($activity['title']) . "</td>";
            echo "<td>" . htmlspecialchars($activity['activity_type']) . "</td>";
            echo "<td>" . $activity['course_id'] . "</td>";
            echo "<td>";

            // Use the appropriate edit page based on activity type
            if ($activity['activity_type'] == 'material') {
                echo "<a href='material_edit.php?id=" . $activity['activity_id'] . "'>Edit</a> | ";
            } elseif ($activity['activity_type'] == 'quiz') {
                echo "<a href='quiz_edit.php?id=" . $activity['activity_id'] . "'>Edit</a> | ";
            } elseif ($activity['activity_type'] == 'assignment') {
                echo "<a href='assignment_edit.php?id=" . $activity['activity_id'] . "'>Edit</a> | ";
            } else {
                echo "<a href='activity_edit.php?id=" . $activity['activity_id'] . "'>Edit</a> | ";
            }

            if ($activity['activity_type'] != 'quiz') {
                echo "<a href='fix_quiz.php?id=" . $activity['activity_id'] . "'>Fix as Quiz</a> | ";
            }

            if ($activity['activity_type'] != 'activity') {
                echo "<a href='fix_activity_6.php?id=" . $activity['activity_id'] . "'>Fix as Activity</a> | ";
            }

            // Only show Questions link for activity types that support questions
            if ($activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') {
                echo "<a href='activity_edit.php?id=" . $activity['activity_id'] . "#questions'>Questions</a>";
            }

            echo "</td>";
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>No activities found.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
