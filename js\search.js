/**
 * Search functionality for manage_teachers.php and manage_students.php
 */

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Teacher search functionality
    if (document.getElementById('teacherSearch')) {
        const teacherSearchInput = document.getElementById('teacherSearch');
        const teacherSearchBtn = document.getElementById('teacherSearchBtn');
        const teacherTableBody = document.getElementById('teacherTableBody');
        const noTeacherResults = document.getElementById('noTeacherResults');

        // Function to perform teacher search
        function performTeacherSearch() {
            const value = teacherSearchInput.value.toLowerCase();
            let visibleRows = 0;
            
            // Get all rows except the "No results" row
            const rows = teacherTableBody.querySelectorAll('tr:not(#noTeacherResults)');
            
            // Loop through each row and check if it contains the search value
            rows.forEach(function(row) {
                const rowText = row.textContent.toLowerCase();
                const isVisible = rowText.includes(value);
                
                // Show or hide the row based on the search
                row.style.display = isVisible ? '' : 'none';
                
                // Count visible rows
                if (isVisible) {
                    visibleRows++;
                }
            });
            
            // Show "No results" message if no matching rows and search is not empty
            if (visibleRows === 0 && value !== '') {
                if (noTeacherResults) {
                    noTeacherResults.style.display = '';
                }
            } else {
                if (noTeacherResults) {
                    noTeacherResults.style.display = 'none';
                }
            }
        }
        
        // Add event listeners for teacher search
        teacherSearchInput.addEventListener('keyup', performTeacherSearch);
        teacherSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performTeacherSearch();
            }
        });
        
        if (teacherSearchBtn) {
            teacherSearchBtn.addEventListener('click', performTeacherSearch);
        }
    }
    
    // Student search functionality
    if (document.getElementById('studentSearch')) {
        const studentSearchInput = document.getElementById('studentSearch');
        const studentSearchBtn = document.getElementById('studentSearchBtn');
        const studentTableBody = document.getElementById('studentTableBody');
        const noStudentResults = document.getElementById('noStudentResults');

        // Function to perform student search
        function performStudentSearch() {
            const value = studentSearchInput.value.toLowerCase();
            let visibleRows = 0;
            
            // Get all rows except the "No results" row
            const rows = studentTableBody.querySelectorAll('tr:not(#noStudentResults)');
            
            // Loop through each row and check if it contains the search value
            rows.forEach(function(row) {
                const rowText = row.textContent.toLowerCase();
                const isVisible = rowText.includes(value);
                
                // Show or hide the row based on the search
                row.style.display = isVisible ? '' : 'none';
                
                // Count visible rows
                if (isVisible) {
                    visibleRows++;
                }
            });
            
            // Show "No results" message if no matching rows and search is not empty
            if (visibleRows === 0 && value !== '') {
                if (noStudentResults) {
                    noStudentResults.style.display = '';
                }
            } else {
                if (noStudentResults) {
                    noStudentResults.style.display = 'none';
                }
            }
        }
        
        // Add event listeners for student search
        studentSearchInput.addEventListener('keyup', performStudentSearch);
        studentSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performStudentSearch();
            }
        });
        
        if (studentSearchBtn) {
            studentSearchBtn.addEventListener('click', performStudentSearch);
        }
    }
});
