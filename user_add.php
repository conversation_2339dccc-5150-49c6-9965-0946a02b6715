<?php
/**
 * User Add Page
 *
 * This page allows administrators to add new user accounts.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/user_functions.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Get all roles
$roles = getAllRoles();

// Check if roles is an error message
if (is_string($roles)) {
    $error = $roles;
    $roles = [];
}

// Initialize variables
$username = $email = $firstName = $lastName = $roleId = $gender = $birthday = $phoneNumber = "";
$username_err = $email_err = $firstName_err = $lastName_err = $roleId_err = $password_err = $gender_err = $birthday_err = $phoneNumber_err = "";

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        $username = trim($_POST["username"]);
    }

    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";
    } elseif (strlen(trim($_POST["password"])) < 6) {
        $password_err = "Password must have at least 6 characters.";
    } else {
        $password = trim($_POST["password"]);
    }

    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } elseif (!filter_var(trim($_POST["email"]), FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email.";
    } else {
        $email = trim($_POST["email"]);
    }

    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $firstName_err = "Please enter a first name.";
    } else {
        $firstName = trim($_POST["first_name"]);
    }

    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $lastName_err = "Please enter a last name.";
    } else {
        $lastName = trim($_POST["last_name"]);
    }

    // Validate role
    if (empty(trim($_POST["role_id"]))) {
        $roleId_err = "Please select a role.";
    } else {
        $roleId = trim($_POST["role_id"]);
    }

    // Validate gender
    if (isset($_POST["gender"]) && !empty(trim($_POST["gender"]))) {
        $gender = trim($_POST["gender"]);
        // Check if gender is one of the allowed values
        if (!in_array($gender, ['male', 'female', 'other'])) {
            $gender_err = "Please select a valid gender.";
        }
    }

    // Validate birthday
    if (!empty($_POST["birth_month"]) && !empty($_POST["birth_day"]) && !empty($_POST["birth_year"])) {
        $birth_month = trim($_POST["birth_month"]);
        $birth_day = trim($_POST["birth_day"]);
        $birth_year = trim($_POST["birth_year"]);

        // Format as YYYY-MM-DD for database storage
        $birthday = sprintf('%04d-%02d-%02d', $birth_year, $birth_month, $birth_day);

        // Check if the date is valid
        $date = date_create_from_format('Y-m-d', $birthday);
        if (!$date || date_format($date, 'Y-m-d') !== $birthday) {
            $birthday_err = "Please enter a valid date.";
        }
    } else {
        // Birthday is optional for admin-created accounts
        $birthday = null;
    }

    // Validate phone number
    if (isset($_POST["phone_number"]) && !empty(trim($_POST["phone_number"]))) {
        $phoneNumber = trim($_POST["phone_number"]);
        // Simple validation for phone number format
        if (!preg_match('/^[0-9+\-\s()]{7,20}$/', $phoneNumber)) {
            $phoneNumber_err = "Please enter a valid phone number.";
        }
    }

    // Check input errors before creating the user
    if (empty($username_err) && empty($password_err) && empty($email_err) && empty($firstName_err) && empty($lastName_err) && empty($roleId_err) && empty($gender_err) && empty($birthday_err) && empty($phoneNumber_err)) {
        // Create the user
        $result = registerUser($username, $password, $email, $firstName, $lastName, $roleId, $gender, $birthday, $phoneNumber);

        if ($result === true) {
            $_SESSION['success'] = "User created successfully.";
            header('Location: users.php');
            exit;
        } else {
            $error = $result;
        }
    }
}

// Set page title
$pageTitle = "Add User";

// Include header
require_once 'includes/header.php';
?>

<style>
    /* Date input styling */
    input[type="date"] {
        padding-right: 10px;
    }

    /* Select styling for consistency */
    select.form-control {
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0.375rem 0.75rem;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Add New User</h1>
    <a href="users.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Users
    </a>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-user-plus"></i> Create New User Account</h5>
    </div>
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <label>Username</label>
                <input type="text" name="username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($username); ?>">
                <span class="invalid-feedback"><?php echo $username_err; ?></span>
            </div>
            <div class="form-group">
                <label>Password</label>
                <input type="password" name="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>">
                <span class="invalid-feedback"><?php echo $password_err; ?></span>
            </div>
            <div class="form-group">
                <label>Email</label>
                <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($email); ?>">
                <span class="invalid-feedback"><?php echo $email_err; ?></span>
            </div>
            <div class="form-group">
                <label>First Name</label>
                <input type="text" name="first_name" class="form-control <?php echo (!empty($firstName_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($firstName); ?>">
                <span class="invalid-feedback"><?php echo $firstName_err; ?></span>
            </div>
            <div class="form-group">
                <label>Last Name</label>
                <input type="text" name="last_name" class="form-control <?php echo (!empty($lastName_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($lastName); ?>">
                <span class="invalid-feedback"><?php echo $lastName_err; ?></span>
            </div>
            <div class="form-group">
                <label>Gender</label>
                <select name="gender" class="form-control <?php echo (!empty($gender_err)) ? 'is-invalid' : ''; ?>">
                    <option value="">Select Gender</option>
                    <option value="male" <?php echo ($gender == 'male') ? 'selected' : ''; ?>>Male</option>
                    <option value="female" <?php echo ($gender == 'female') ? 'selected' : ''; ?>>Female</option>
                    <option value="other" <?php echo ($gender == 'other') ? 'selected' : ''; ?>>Other</option>
                </select>
                <span class="invalid-feedback"><?php echo $gender_err; ?></span>
            </div>
            <div class="form-group">
                <label for="birthday">Birthday</label>
                <div class="row">
                    <div class="col-md-4">
                        <select id="birth_month" name="birth_month" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Month</option>
                            <?php
                            $months = [
                                1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                                5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                                9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                            ];

                            $selected_month = isset($_POST['birth_month']) ? $_POST['birth_month'] : '';
                            if (empty($selected_month) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_month = (int)$date_parts[1];
                                }
                            }

                            foreach ($months as $num => $name) {
                                echo '<option value="' . $num . '"' . ($selected_month == $num ? ' selected' : '') . '>' . $name . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="birth_day" name="birth_day" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Day</option>
                            <?php
                            $selected_day = isset($_POST['birth_day']) ? $_POST['birth_day'] : '';
                            if (empty($selected_day) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_day = (int)$date_parts[2];
                                }
                            }

                            for ($i = 1; $i <= 31; $i++) {
                                echo '<option value="' . $i . '"' . ($selected_day == $i ? ' selected' : '') . '>' . $i . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="birth_year" name="birth_year" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Year</option>
                            <?php
                            $current_year = (int)date('Y');
                            $selected_year = isset($_POST['birth_year']) ? $_POST['birth_year'] : '';
                            if (empty($selected_year) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_year = (int)$date_parts[0];
                                }
                            }

                            for ($i = $current_year; $i >= $current_year - 100; $i--) {
                                echo '<option value="' . $i . '"' . ($selected_year == $i ? ' selected' : '') . '>' . $i . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <span class="invalid-feedback"><?php echo $birthday_err; ?></span>
            </div>
            <div class="form-group">
                <label>Phone Number</label>
                <input type="text" name="phone_number" class="form-control <?php echo (!empty($phoneNumber_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($phoneNumber); ?>">
                <span class="invalid-feedback"><?php echo $phoneNumber_err; ?></span>
            </div>
            <div class="form-group">
                <label>Role</label>
                <select name="role_id" class="form-control <?php echo (!empty($roleId_err)) ? 'is-invalid' : ''; ?>">
                    <option value="">Select Role</option>
                    <?php foreach ($roles as $role): ?>
                    <option value="<?php echo $role['role_id']; ?>" <?php echo ($roleId == $role['role_id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars(ucfirst($role['role_name'])); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
                <span class="invalid-feedback"><?php echo $roleId_err; ?></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Create User</button>
                <a href="users.php" class="btn btn-secondary ml-2">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>


