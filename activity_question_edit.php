<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit questions.";
    header("location: index.php");
    exit;
}

// Check if question ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$questionId = intval($_GET['id']);

// Check if this is a quiz question or an activity question
$stmt = $pdo->prepare("
    SELECT activity_type
    FROM activities a
    JOIN quiz_questions q ON a.activity_id = q.activity_id
    WHERE q.question_id = :questionId
");
$stmt->bindParam(':questionId', $questionId);
$stmt->execute();

$isQuizQuestion = ($stmt->rowCount() > 0);

// Get question details based on question type
if ($isQuizQuestion) {
    $stmt = $pdo->prepare("
        SELECT q.*, a.activity_id, a.title as activity_title, a.course_id
        FROM quiz_questions q
        JOIN activities a ON q.activity_id = a.activity_id
        WHERE q.question_id = :questionId
    ");
} else {
    $stmt = $pdo->prepare("
        SELECT q.*, a.activity_id, a.title as activity_title, a.course_id
        FROM activity_questions q
        JOIN activities a ON q.activity_id = a.activity_id
        WHERE q.question_id = :questionId
    ");
}

$stmt->bindParam(':questionId', $questionId);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    $_SESSION['error'] = "Question not found.";
    header("location: index.php");
    exit;
}

$question = $stmt->fetch();
$activityId = $question['activity_id'];
$courseId = $question['course_id'];

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this question
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit questions for this activity.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Initialize variables
$questionText = $question['question_text'];
$questionType = $question['question_type'];
$points = $question['points'];
$questionText_err = $points_err = "";
$success = "";

// Get existing options if this is a multiple choice or true/false question
$options = [];
if ($questionType == 'multiple_choice' || $questionType == 'true_false') {
    if ($isQuizQuestion) {
        $stmt = $pdo->prepare("
            SELECT * FROM quiz_options
            WHERE question_id = :questionId
            ORDER BY position, option_id
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT * FROM activity_question_options
            WHERE question_id = :questionId
            ORDER BY position, option_id
        ");
    }
    $stmt->bindParam(':questionId', $questionId);
    $stmt->execute();
    $options = $stmt->fetchAll();
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate question text
    if (empty(trim($_POST["question_text"]))) {
        $questionText_err = "Please enter question text.";
    } else {
        $questionText = trim($_POST["question_text"]);
    }

    // Validate points
    if (!isset($_POST["points"]) || !is_numeric($_POST["points"]) || $_POST["points"] < 1) {
        $points_err = "Please enter a valid number of points (minimum 1).";
    } else {
        $points = intval($_POST["points"]);
    }

    // Check input errors before updating the question
    if (empty($questionText_err) && empty($points_err)) {
        // Start transaction
        $pdo->beginTransaction();

        try {
            // Update the question based on its type
            if ($isQuizQuestion) {
                $result = updateActivityQuizQuestion($questionId, $questionText, $points);
            } else {
                $result = updateActivityQuestion($questionId, $questionText, $points);
            }

            if ($result === true) {
                // If this is a multiple choice or true/false question, update options
                if (($questionType == 'multiple_choice' || $questionType == 'true_false') && isset($_POST['option_text'])) {
                    // First, set all options to not correct
                    if ($isQuizQuestion) {
                        $stmt = $pdo->prepare("
                            UPDATE quiz_options
                            SET is_correct = 0
                            WHERE question_id = :questionId
                        ");
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE activity_question_options
                            SET is_correct = 0
                            WHERE question_id = :questionId
                        ");
                    }
                    $stmt->bindParam(':questionId', $questionId);
                    $stmt->execute();

                    // Update option texts and set correct options
                    foreach ($_POST['option_text'] as $optionId => $text) {
                        $isCorrect = isset($_POST['is_correct']) && in_array($optionId, $_POST['is_correct']) ? 1 : 0;

                        if ($isQuizQuestion) {
                            $stmt = $pdo->prepare("
                                UPDATE quiz_options
                                SET option_text = :text, is_correct = :isCorrect
                                WHERE option_id = :optionId AND question_id = :questionId
                            ");
                        } else {
                            $stmt = $pdo->prepare("
                                UPDATE activity_question_options
                                SET option_text = :text, is_correct = :isCorrect
                                WHERE option_id = :optionId AND question_id = :questionId
                            ");
                        }
                        $stmt->bindParam(':text', $text);
                        $stmt->bindParam(':isCorrect', $isCorrect);
                        $stmt->bindParam(':optionId', $optionId);
                        $stmt->bindParam(':questionId', $questionId);
                        $stmt->execute();
                    }
                }

                $pdo->commit();

                // Question updated successfully
                $_SESSION['success'] = "Question and options updated successfully!";

                // Check if this is from an assignment, quiz, or activity
                $stmt = $pdo->prepare("SELECT activity_type FROM activities WHERE activity_id = :activityId");
                $stmt->bindParam(':activityId', $activityId);
                $stmt->execute();
                $activityType = $stmt->fetchColumn();

                if ($activityType == 'assignment') {
                    header("location: assignment_edit.php?id=$activityId#questions");
                } else if ($activityType == 'quiz') {
                    header("location: quiz_edit.php?id=$activityId#questions");
                } else {
                    header("location: activity_edit.php?id=$activityId#questions");
                }
                exit;
            } else {
                // Error occurred
                $pdo->rollBack();
                $error = $result;
            }
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "Failed to update question: " . $e->getMessage();
        }
    }
}

// Set page title
$page_title = "Edit Question";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($question['activity_title']); ?></a></li>
                    <?php
                    // Check if this is from an assignment, quiz, or activity
                    $stmt = $pdo->prepare("SELECT activity_type FROM activities WHERE activity_id = :activityId");
                    $stmt->bindParam(':activityId', $activityId);
                    $stmt->execute();
                    $activityType = $stmt->fetchColumn();

                    if ($activityType == 'assignment'): ?>
                    <li class="breadcrumb-item"><a href="assignment_edit.php?id=<?php echo $activityId; ?>">Edit Assignment</a></li>
                    <?php elseif ($activityType == 'quiz'): ?>
                    <li class="breadcrumb-item"><a href="quiz_edit.php?id=<?php echo $activityId; ?>">Edit Quiz</a></li>
                    <?php else: ?>
                    <li class="breadcrumb-item"><a href="activity_edit.php?id=<?php echo $activityId; ?>">Edit Activity</a></li>
                    <?php endif; ?>
                    <li class="breadcrumb-item active">Edit Question</li>
                </ol>
            </nav>

            <h1 class="mb-4">Edit Question</h1>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $questionId); ?>" method="post">
                        <div class="form-group">
                            <label for="question_text">Question Text <span class="text-danger">*</span></label>
                            <textarea name="question_text" id="question_text" class="form-control <?php echo (!empty($questionText_err)) ? 'is-invalid' : ''; ?>" rows="3" required><?php echo htmlspecialchars($questionText); ?></textarea>
                            <span class="invalid-feedback"><?php echo $questionText_err; ?></span>
                        </div>

                        <div class="form-group">
                            <label>Question Type</label>
                            <input type="text" class="form-control" value="<?php echo ucfirst(str_replace('_', ' ', $questionType)); ?>" readonly>
                            <small class="form-text text-muted">Question type cannot be changed after creation.</small>
                        </div>

                        <div class="form-group">
                            <label for="points">Points <span class="text-danger">*</span></label>
                            <input type="number" name="points" id="points" class="form-control <?php echo (!empty($points_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $points; ?>" min="1" required>
                            <span class="invalid-feedback"><?php echo $points_err; ?></span>
                        </div>

                        <?php if ($questionType == 'multiple_choice' || $questionType == 'true_false'): ?>
                        <div class="form-group mt-4">
                            <h4>Question Options</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Option Text</th>
                                            <th width="120">Correct</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (count($options) > 0): ?>
                                            <?php foreach ($options as $option): ?>
                                            <tr>
                                                <td>
                                                    <input type="text" name="option_text[<?php echo $option['option_id']; ?>]" class="form-control" value="<?php echo htmlspecialchars($option['option_text']); ?>" required>
                                                </td>
                                                <td class="text-center">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="correct_<?php echo $option['option_id']; ?>" name="is_correct[]" value="<?php echo $option['option_id']; ?>" <?php echo $option['is_correct'] ? 'checked' : ''; ?>>
                                                        <label class="custom-control-label" for="correct_<?php echo $option['option_id']; ?>"></label>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="2" class="text-center">No options added yet.
                                                <?php if ($isQuizQuestion): ?>
                                                <a href="quiz_question_options.php?id=<?php echo $questionId; ?>">Add options</a>
                                                <?php else: ?>
                                                <a href="activity_question_options.php?id=<?php echo $questionId; ?>">Add options</a>
                                                <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <small class="form-text text-muted">
                                To add more options or delete existing ones, use the
                                <?php if ($isQuizQuestion): ?>
                                <a href="quiz_question_options.php?id=<?php echo $questionId; ?>">Manage Question Options</a>
                                <?php else: ?>
                                <a href="activity_question_options.php?id=<?php echo $questionId; ?>">Manage Question Options</a>
                                <?php endif; ?> page.
                            </small>
                        </div>
                        <?php endif; ?>

                        <div class="form-group text-right">
                            <?php if ($activityType == 'assignment'): ?>
                            <a href="assignment_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-secondary mr-2">Cancel</a>
                            <?php elseif ($activityType == 'quiz'): ?>
                            <a href="quiz_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-secondary mr-2">Cancel</a>
                            <?php else: ?>
                            <a href="activity_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-secondary mr-2">Cancel</a>
                            <?php endif; ?>
                            <button type="submit" class="btn btn-primary">Update Question</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
