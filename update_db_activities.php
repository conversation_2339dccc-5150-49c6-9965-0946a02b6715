<?php
// Include configuration file
require_once 'includes/config.php';

// Get the database connection
global $pdo;

// Create activities table if it doesn't exist
try {
    $pdo->beginTransaction();
    
    // Check if activities table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activities'");
    if ($stmt->rowCount() == 0) {
        // Create activities table
        $sql = "CREATE TABLE activities (
            activity_id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            activity_type ENUM('material', 'assignment', 'quiz', 'question') NOT NULL,
            points INT DEFAULT 0,
            due_date DATETIME,
            is_published TINYINT(1) DEFAULT 1,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OR<PERSON><PERSON>N KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "Activities table created successfully.<br>";
    } else {
        echo "Activities table already exists.<br>";
    }
    
    // Check if quizzes table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'quiz_questions'");
    if ($stmt->rowCount() == 0) {
        // Create quiz_questions table
        $sql = "CREATE TABLE quiz_questions (
            question_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            question_text TEXT NOT NULL,
            question_type ENUM('multiple_choice', 'short_answer', 'paragraph', 'checkbox', 'dropdown') NOT NULL,
            points INT DEFAULT 1,
            position INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "Quiz Questions table created successfully.<br>";
    } else {
        echo "Quiz Questions table already exists.<br>";
    }
    
    // Check if quiz_options table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'quiz_options'");
    if ($stmt->rowCount() == 0) {
        // Create quiz_options table
        $sql = "CREATE TABLE quiz_options (
            option_id INT AUTO_INCREMENT PRIMARY KEY,
            question_id INT NOT NULL,
            option_text TEXT NOT NULL,
            is_correct TINYINT(1) DEFAULT 0,
            position INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (question_id) REFERENCES quiz_questions(question_id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "Quiz Options table created successfully.<br>";
    } else {
        echo "Quiz Options table already exists.<br>";
    }
    
    // Check if activity_submissions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_submissions'");
    if ($stmt->rowCount() == 0) {
        // Create activity_submissions table
        $sql = "CREATE TABLE activity_submissions (
            submission_id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT NOT NULL,
            user_id INT NOT NULL,
            submission_content TEXT,
            submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            grade FLOAT DEFAULT NULL,
            feedback TEXT,
            graded_by INT DEFAULT NULL,
            graded_at TIMESTAMP NULL,
            FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (graded_by) REFERENCES users(user_id) ON DELETE SET NULL
        )";
        $pdo->exec($sql);
        echo "Activity Submissions table created successfully.<br>";
    } else {
        echo "Activity Submissions table already exists.<br>";
    }
    
    // Check if quiz_answers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'quiz_answers'");
    if ($stmt->rowCount() == 0) {
        // Create quiz_answers table
        $sql = "CREATE TABLE quiz_answers (
            answer_id INT AUTO_INCREMENT PRIMARY KEY,
            submission_id INT NOT NULL,
            question_id INT NOT NULL,
            answer_text TEXT,
            is_correct TINYINT(1) DEFAULT NULL,
            points_earned FLOAT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (submission_id) REFERENCES activity_submissions(submission_id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES quiz_questions(question_id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        echo "Quiz Answers table created successfully.<br>";
    } else {
        echo "Quiz Answers table already exists.<br>";
    }
    
    $pdo->commit();
    echo "Database update completed successfully.";
} catch (PDOException $e) {
    $pdo->rollBack();
    echo "Error: " . $e->getMessage();
}
?>
