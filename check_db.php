<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Database Structure Check</h1>";

// Check table structure
try {
    // Check if database exists
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([DB_NAME]);
    if ($stmt->rowCount() > 0) {
        echo "<p>Database '" . DB_NAME . "' exists.</p>";
    } else {
        echo "<p>Database '" . DB_NAME . "' does not exist!</p>";
        die();
    }

    // Get all tables
    $stmt = $pdo->prepare("SHOW TABLES");
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (count($tables) > 0) {
        echo "<p>Found " . count($tables) . " tables in the database.</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>" . $table . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No tables found in the database!</p>";
    }

    // Check roles table
    echo "<h2>Roles Table</h2>";
    if (in_array('roles', $tables)) {
        echo "<p>Roles table exists.</p>";

        // Check roles table structure
        $stmt = $pdo->prepare("DESCRIBE roles");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Check roles data
        $stmt = $pdo->prepare("SELECT * FROM roles");
        $stmt->execute();
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($roles) > 0) {
            echo "<h3>Roles Data</h3>";
            echo "<table border='1'>";
            echo "<tr>";
            foreach (array_keys($roles[0]) as $key) {
                echo "<th>" . htmlspecialchars($key) . "</th>";
            }
            echo "</tr>";

            foreach ($roles as $role) {
                echo "<tr>";
                foreach ($role as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No roles found in the database.</p>";

            // Create roles if they don't exist
            echo "<h3>Creating default roles</h3>";
            try {
                $stmt = $pdo->prepare("INSERT INTO roles (role_id, role_name) VALUES (1, 'admin'), (2, 'teacher'), (3, 'student')");
                $stmt->execute();
                echo "<p>Default roles created successfully.</p>";
            } catch (PDOException $e) {
                echo "<p>Error creating roles: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>Roles table does NOT exist!</p>";

        // Create roles table
        echo "<h3>Creating roles table</h3>";
        try {
            $stmt = $pdo->prepare("
                CREATE TABLE roles (
                    role_id INT PRIMARY KEY AUTO_INCREMENT,
                    role_name VARCHAR(50) NOT NULL UNIQUE
                )
            ");
            $stmt->execute();
            echo "<p>Roles table created successfully.</p>";

            // Insert default roles
            $stmt = $pdo->prepare("INSERT INTO roles (role_id, role_name) VALUES (1, 'admin'), (2, 'teacher'), (3, 'student')");
            $stmt->execute();
            echo "<p>Default roles created successfully.</p>";
        } catch (PDOException $e) {
            echo "<p>Error creating roles table: " . $e->getMessage() . "</p>";
        }
    }

    // Check users table structure
    echo "<h2>Users Table Structure</h2>";
    if (in_array('users', $tables)) {
        $stmt = $pdo->prepare("DESCRIBE users");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }

        echo "</table>";

        // Check if role_id column exists
        $roleIdExists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'role_id') {
                $roleIdExists = true;
                break;
            }
        }

        echo "<h3>Role ID Column Status</h3>";
        if ($roleIdExists) {
            echo "<p>role_id column exists in the users table.</p>";
        } else {
            echo "<p>role_id column does NOT exist in the users table!</p>";

            // Add role_id column
            echo "<h3>Adding role_id Column</h3>";
            try {
                $stmt = $pdo->prepare("ALTER TABLE users ADD COLUMN role_id INT NOT NULL DEFAULT 3 AFTER password");
                $stmt->execute();
                echo "<p>role_id column added successfully.</p>";

                // Add foreign key constraint
                $stmt = $pdo->prepare("ALTER TABLE users ADD CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES roles(role_id)");
                $stmt->execute();
                echo "<p>Foreign key constraint added successfully.</p>";
            } catch (PDOException $e) {
                echo "<p>Error adding role_id column: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>Users table does not exist!</p>";
    }

    // Check if setup_db.php has been run
    echo "<h2>Database Setup Status</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row['count'] > 0) {
        echo "<p>Admin user exists. Setup has been run.</p>";
    } else {
        echo "<p>Admin user does not exist. Setup may not have been run.</p>";
    }

    // Check sample users
    echo "<h2>Sample Users (if any)</h2>";
    try {
        $stmt = $pdo->prepare("SELECT user_id, first_name, last_name, username, email, role_id, is_active FROM users LIMIT 5");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($users) > 0) {
            echo "<table border='1'>";
            echo "<tr>";
            foreach (array_keys($users[0]) as $key) {
                echo "<th>" . htmlspecialchars($key) . "</th>";
            }
            echo "</tr>";

            foreach ($users as $user) {
                echo "<tr>";
                foreach ($user as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No users found in the database.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error querying users: " . $e->getMessage() . "</p>";
    }

    // Check submissions table structure
    echo "<h2>Submissions Table Structure</h2>";
    if (in_array('submissions', $tables)) {
        $stmt = $pdo->prepare("DESCRIBE submissions");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Show sample submissions
        echo "<h3>Sample Submissions</h3>";
        try {
            $stmt = $pdo->prepare("SELECT * FROM submissions LIMIT 5");
            $stmt->execute();
            $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (count($submissions) > 0) {
                echo "<table border='1'>";
                echo "<tr>";
                foreach (array_keys($submissions[0]) as $key) {
                    echo "<th>" . htmlspecialchars($key) . "</th>";
                }
                echo "</tr>";

                foreach ($submissions as $submission) {
                    echo "<tr>";
                    foreach ($submission as $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No submissions found in the database.</p>";
            }
        } catch (PDOException $e) {
            echo "<p>Error querying submissions: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Submissions table does not exist!</p>";
    }

    // Check activities table structure
    echo "<h2>Activities Table Structure</h2>";
    if (in_array('activities', $tables)) {
        $stmt = $pdo->prepare("DESCRIBE activities");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Show sample activities
        echo "<h3>Sample Activities</h3>";
        try {
            $stmt = $pdo->prepare("SELECT * FROM activities LIMIT 5");
            $stmt->execute();
            $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (count($activities) > 0) {
                echo "<table border='1'>";
                echo "<tr>";
                foreach (array_keys($activities[0]) as $key) {
                    echo "<th>" . htmlspecialchars($key) . "</th>";
                }
                echo "</tr>";

                foreach ($activities as $activity) {
                    echo "<tr>";
                    foreach ($activity as $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No activities found in the database.</p>";
            }
        } catch (PDOException $e) {
            echo "<p>Error querying activities: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Activities table does not exist!</p>";
    }

} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
