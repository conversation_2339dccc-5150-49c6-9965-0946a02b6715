<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Adding Profile Picture Support</h1>";

try {
    // Check if the profile_picture column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'profile_picture'");
    $profilePictureExists = $stmt->rowCount() > 0;
    
    // Add profile_picture column if it doesn't exist
    if (!$profilePictureExists) {
        $pdo->exec("ALTER TABLE users ADD COLUMN profile_picture VARCHAR(255) AFTER phone_number");
        echo "<p>Added profile_picture column to users table.</p>";
    } else {
        echo "<p>Profile picture column already exists.</p>";
    }
    
    // Create uploads directory if it doesn't exist
    if (!file_exists('uploads/profile_pics')) {
        mkdir('uploads/profile_pics', 0777, true);
        echo "<p>Created uploads/profile_pics directory.</p>";
    } else {
        echo "<p>Uploads directory already exists.</p>";
    }
    
    echo "<p>Profile picture support added successfully.</p>";
    echo "<p><a href='profile.php'>Go to Profile Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error updating users table: " . $e->getMessage() . "</p>";
}
?>
