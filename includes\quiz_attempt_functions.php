<?php
/**
 * Quiz Attempt Functions
 * 
 * This file contains functions related to quiz attempts and responses.
 */

require_once 'config.php';
require_once 'quiz_functions.php';

/**
 * Function to start a quiz attempt
 * 
 * @param int $quizId The quiz ID
 * @param int $userId The user ID
 * @return bool|string Attempt ID if successful, error message otherwise
 */
function startQuizAttempt($quizId, $userId) {
    global $pdo;
    
    try {
        // Check if the quiz exists
        $stmt = $pdo->prepare("SELECT quiz_id, time_limit FROM quizzes WHERE quiz_id = :quizId");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return "Quiz not found.";
        }
        
        // Check if there's an unfinished attempt
        $stmt = $pdo->prepare("
            SELECT attempt_id 
            FROM quiz_attempts 
            WHERE quiz_id = :quizId AND user_id = :userId AND end_time IS NULL
        ");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $attempt = $stmt->fetch();
            return $attempt['attempt_id'];
        }
        
        // Create a new attempt
        $stmt = $pdo->prepare("
            INSERT INTO quiz_attempts (quiz_id, user_id) 
            VALUES (:quizId, :userId)
        ");
        
        $stmt->bindParam(':quizId', $quizId);
        $stmt->bindParam(':userId', $userId);
        
        $stmt->execute();
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to start quiz attempt: " . $e->getMessage();
    }
}

/**
 * Function to submit a quiz response
 * 
 * @param int $attemptId The attempt ID
 * @param int $questionId The question ID
 * @param int|null $optionId The selected option ID (for multiple choice and true/false questions)
 * @param string|null $textResponse The text response (for short answer questions)
 * @return bool|string True if submission successful, error message otherwise
 */
function submitQuizResponse($attemptId, $questionId, $optionId = null, $textResponse = null) {
    global $pdo;
    
    try {
        // Check if the attempt exists and is not finished
        $stmt = $pdo->prepare("
            SELECT a.attempt_id, a.user_id, a.end_time, q.question_type
            FROM quiz_attempts a
            JOIN quiz_questions q ON q.question_id = :questionId
            WHERE a.attempt_id = :attemptId
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return "Attempt or question not found.";
        }
        
        $attempt = $stmt->fetch();
        
        // Check if the attempt belongs to the current user
        if ($attempt['user_id'] != $_SESSION['user_id']) {
            return "You are not authorized to submit responses for this attempt.";
        }
        
        // Check if the attempt is already finished
        if ($attempt['end_time'] !== null) {
            return "This quiz attempt has already been submitted.";
        }
        
        // Determine if the response is correct
        $isCorrect = false;
        
        if ($attempt['question_type'] == 'multiple_choice' || $attempt['question_type'] == 'true_false') {
            if ($optionId !== null) {
                $stmt = $pdo->prepare("
                    SELECT is_correct 
                    FROM quiz_options 
                    WHERE option_id = :optionId AND question_id = :questionId
                ");
                $stmt->bindParam(':optionId', $optionId);
                $stmt->bindParam(':questionId', $questionId);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $option = $stmt->fetch();
                    $isCorrect = $option['is_correct'];
                }
            }
        } else if ($attempt['question_type'] == 'short_answer') {
            // For short answer questions, teacher will grade manually
            $isCorrect = null;
        }
        
        // Check if a response already exists for this question
        $stmt = $pdo->prepare("
            SELECT response_id 
            FROM quiz_responses 
            WHERE attempt_id = :attemptId AND question_id = :questionId
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Update the existing response
            $stmt = $pdo->prepare("
                UPDATE quiz_responses 
                SET option_id = :optionId, text_response = :textResponse, is_correct = :isCorrect 
                WHERE attempt_id = :attemptId AND question_id = :questionId
            ");
        } else {
            // Create a new response
            $stmt = $pdo->prepare("
                INSERT INTO quiz_responses (attempt_id, question_id, option_id, text_response, is_correct) 
                VALUES (:attemptId, :questionId, :optionId, :textResponse, :isCorrect)
            ");
        }
        
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->bindParam(':questionId', $questionId);
        $stmt->bindParam(':optionId', $optionId);
        $stmt->bindParam(':textResponse', $textResponse);
        $stmt->bindParam(':isCorrect', $isCorrect, PDO::PARAM_BOOL);
        $stmt->execute();
        
        return true;
    } catch (PDOException $e) {
        return "Failed to submit response: " . $e->getMessage();
    }
}

/**
 * Function to finish a quiz attempt
 * 
 * @param int $attemptId The attempt ID
 * @return bool|string True if finish successful, error message otherwise
 */
function finishQuizAttempt($attemptId) {
    global $pdo;
    
    try {
        // Check if the attempt exists and is not finished
        $stmt = $pdo->prepare("
            SELECT a.attempt_id, a.user_id, a.quiz_id, a.end_time, q.passing_score
            FROM quiz_attempts a
            JOIN quizzes q ON a.quiz_id = q.quiz_id
            WHERE a.attempt_id = :attemptId
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return "Attempt not found.";
        }
        
        $attempt = $stmt->fetch();
        
        // Check if the attempt belongs to the current user
        if ($attempt['user_id'] != $_SESSION['user_id']) {
            return "You are not authorized to finish this attempt.";
        }
        
        // Check if the attempt is already finished
        if ($attempt['end_time'] !== null) {
            return "This quiz attempt has already been submitted.";
        }
        
        // Calculate the score
        $stmt = $pdo->prepare("
            SELECT SUM(q.points) as total_points, SUM(CASE WHEN r.is_correct = 1 THEN q.points ELSE 0 END) as earned_points
            FROM quiz_questions q
            LEFT JOIN quiz_responses r ON q.question_id = r.question_id AND r.attempt_id = :attemptId
            WHERE q.quiz_id = :quizId
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->bindParam(':quizId', $attempt['quiz_id']);
        $stmt->execute();
        
        $points = $stmt->fetch();
        
        $totalPoints = $points['total_points'] > 0 ? $points['total_points'] : 1; // Avoid division by zero
        $earnedPoints = $points['earned_points'] !== null ? $points['earned_points'] : 0;
        $score = ($earnedPoints / $totalPoints) * 100;
        
        // Update the attempt
        $stmt = $pdo->prepare("
            UPDATE quiz_attempts 
            SET end_time = CURRENT_TIMESTAMP, score = :score 
            WHERE attempt_id = :attemptId
        ");
        
        $stmt->bindParam(':score', $score);
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->execute();
        
        return true;
    } catch (PDOException $e) {
        return "Failed to finish quiz attempt: " . $e->getMessage();
    }
}

/**
 * Function to get all questions for a quiz
 * 
 * @param int $quizId The quiz ID
 * @return array|string Array of questions if successful, error message otherwise
 */
function getQuizQuestions($quizId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT question_id, question_text, question_type, points
            FROM quiz_questions
            WHERE quiz_id = :quizId
            ORDER BY question_id
        ");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve questions: " . $e->getMessage();
    }
}

/**
 * Function to get all options for a question
 * 
 * @param int $questionId The question ID
 * @return array|string Array of options if successful, error message otherwise
 */
function getQuestionOptions($questionId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT option_id, option_text, is_correct
            FROM quiz_options
            WHERE question_id = :questionId
            ORDER BY option_id
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve options: " . $e->getMessage();
    }
}

/**
 * Function to get a quiz attempt
 * 
 * @param int $attemptId The attempt ID
 * @return array|string Attempt data if successful, error message otherwise
 */
function getQuizAttempt($attemptId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT a.attempt_id, a.quiz_id, a.user_id, a.start_time, a.end_time, a.score,
                   q.title as quiz_title, q.passing_score
            FROM quiz_attempts a
            JOIN quizzes q ON a.quiz_id = q.quiz_id
            WHERE a.attempt_id = :attemptId
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->execute();
        
        if ($stmt->rowCount() == 1) {
            $attempt = $stmt->fetch();
            
            // Check if the attempt belongs to the current user or if the user is a teacher/admin
            if ($attempt['user_id'] != $_SESSION['user_id'] && !isTeacher() && !isAdmin()) {
                return "You are not authorized to view this attempt.";
            }
            
            return $attempt;
        } else {
            return "Attempt not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve attempt: " . $e->getMessage();
    }
}

/**
 * Function to get all responses for a quiz attempt
 * 
 * @param int $attemptId The attempt ID
 * @return array|string Array of responses if successful, error message otherwise
 */
function getQuizResponses($attemptId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT r.response_id, r.question_id, r.option_id, r.text_response, r.is_correct,
                   q.question_text, q.question_type, q.points,
                   o.option_text
            FROM quiz_responses r
            JOIN quiz_questions q ON r.question_id = q.question_id
            LEFT JOIN quiz_options o ON r.option_id = o.option_id
            WHERE r.attempt_id = :attemptId
            ORDER BY q.question_id
        ");
        $stmt->bindParam(':attemptId', $attemptId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve responses: " . $e->getMessage();
    }
}

/**
 * Function to get all attempts for a quiz
 * Admin can view all attempts, teachers can only view attempts for their own quizzes
 * 
 * @param int $quizId The quiz ID
 * @return array|string Array of attempts if successful, error message otherwise
 */
function getQuizAttemptsByQuiz($quizId) {
    global $pdo;
    
    try {
        // Check if user is authorized to view these attempts
        if (!isAdmin() && !isTeacher()) {
            return "You are not authorized to view these attempts.";
        }
        
        if (!isAdmin()) {
            // Check if the quiz belongs to a course created by this teacher
            $stmt = $pdo->prepare("
                SELECT c.created_by 
                FROM quizzes q
                JOIN lessons l ON q.lesson_id = l.lesson_id
                JOIN modules m ON l.module_id = m.module_id
                JOIN courses c ON m.course_id = c.course_id
                WHERE q.quiz_id = :quizId
            ");
            $stmt->bindParam(':quizId', $quizId);
            $stmt->execute();
            
            if ($stmt->rowCount() == 1) {
                $quiz = $stmt->fetch();
                
                if ($quiz['created_by'] != $_SESSION['user_id']) {
                    return "You are not authorized to view these attempts.";
                }
            } else {
                return "Quiz not found.";
            }
        }
        
        // Get the attempts
        $stmt = $pdo->prepare("
            SELECT a.attempt_id, a.user_id, u.username, u.first_name, u.last_name, 
                   a.start_time, a.end_time, a.score, q.passing_score
            FROM quiz_attempts a
            JOIN users u ON a.user_id = u.user_id
            JOIN quizzes q ON a.quiz_id = q.quiz_id
            WHERE a.quiz_id = :quizId
            ORDER BY a.start_time DESC
        ");
        $stmt->bindParam(':quizId', $quizId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve attempts: " . $e->getMessage();
    }
}

/**
 * Function to get all attempts for a user
 * 
 * @param int $userId The user ID
 * @return array|string Array of attempts if successful, error message otherwise
 */
function getQuizAttemptsByUser($userId) {
    global $pdo;
    
    try {
        // Check if user is authorized to view these attempts
        if (!isAdmin() && $_SESSION['user_id'] != $userId) {
            return "You are not authorized to view these attempts.";
        }
        
        // Get the attempts
        $stmt = $pdo->prepare("
            SELECT a.attempt_id, a.quiz_id, q.title as quiz_title, 
                   a.start_time, a.end_time, a.score, q.passing_score
            FROM quiz_attempts a
            JOIN quizzes q ON a.quiz_id = q.quiz_id
            WHERE a.user_id = :userId
            ORDER BY a.start_time DESC
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve attempts: " . $e->getMessage();
    }
}
?>
