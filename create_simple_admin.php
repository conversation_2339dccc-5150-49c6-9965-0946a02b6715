<?php
// <PERSON><PERSON><PERSON> to create a new admin user with a simple password
require_once 'includes/config.php';
global $pdo;

// New admin user details
$username = 'simpleadmin';
$password = '123456';
$email = '<EMAIL>';
$firstName = 'Simple';
$lastName = 'Admin';
$roleId = 1; // Admin role

try {
    // Check if username already exists
    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "Username already exists. Please choose a different username.<br>";
    } else {
        // Hash the password with a simple algorithm
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert the new admin user
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, first_name, last_name, role_id, is_active) VALUES (:username, :password, :email, :firstName, :lastName, :roleId, 1)");
        
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':firstName', $firstName);
        $stmt->bindParam(':lastName', $lastName);
        $stmt->bindParam(':roleId', $roleId);
        
        $stmt->execute();
        
        echo "New admin user created successfully!<br>";
        echo "Username: $username<br>";
        echo "Password: $password<br>";
        echo "You can now log in with these credentials.<br>";
        
        // Display the hashed password for verification
        echo "<br>Technical details (for debugging):<br>";
        echo "Hashed password: $hashedPassword<br>";
        
        // Verify the password hash works
        if (password_verify($password, $hashedPassword)) {
            echo "Password verification test: SUCCESS<br>";
        } else {
            echo "Password verification test: FAILED<br>";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
