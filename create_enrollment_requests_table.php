<?php
// Include configuration file
require_once 'includes/config.php';

// Function to create the enrollment_requests table
function createEnrollmentRequestsTable() {
    global $pdo;
    
    try {
        // Check if the table already exists
        $tableExists = false;
        try {
            $stmt = $pdo->query("SELECT 1 FROM enrollment_requests LIMIT 1");
            $tableExists = true;
        } catch (PDOException $e) {
            // Table doesn't exist
        }
        
        if ($tableExists) {
            // Drop the existing table
            $pdo->exec("DROP TABLE enrollment_requests");
            echo "<div class='alert alert-warning'>Existing enrollment_requests table dropped.</div>";
        }
        
        // Create the enrollment_requests table
        $sql = "CREATE TABLE enrollment_requests (
            request_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_by INT NULL,
            processed_date TIMESTAMP NULL,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIG<PERSON> KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL
        )";
        
        $pdo->exec($sql);
        
        echo "<div class='alert alert-success'>Enrollment requests table created successfully.</div>";
        return true;
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Error creating enrollment requests table: " . $e->getMessage() . "</div>";
        return false;
    }
}

// Set page title
$page_title = "Create Enrollment Requests Table";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Create Enrollment Requests Table</h4>
                </div>
                <div class="card-body">
                    <p>This script will create the enrollment_requests table in the database.</p>
                    
                    <?php
                    // Create the table
                    $result = createEnrollmentRequestsTable();
                    ?>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
