/* Modern Dark Theme for Classroom */
:root {
    /* Primary colors - same as light theme */
    --primary-color: #4285F4;
    --primary-hover: #3367D6;

    /* Background colors - dark versions */
    --background-color: #202124;
    --sidebar-bg: #292A2D;
    --sidebar-active-bg: rgba(66, 133, 244, 0.12);
    --sidebar-active-color: #4285F4;
    --header-bg: #292A2D;
    --card-bg: #292A2D;

    /* Text colors - inverted for dark mode */
    --text-color: #E8EAED;
    --text-secondary: #9AA0A6;

    /* Accent colors - same as light theme */
    --secondary-color: #9AA0A6;
    --border-color: #5F6368;
    --success-color: #0F9D58;
    --warning-color: #F4B400;
    --danger-color: #DB4437;
    --info-color: #4285F4;
    --light-hover: #3C4043;

    /* Shadows - darker for dark mode */
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 4px 8px 3px rgba(0, 0, 0, 0.15);
    --button-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);

    /* Form elements */
    --input-bg: #3C4043;
    --input-text: #E8EAED;
}

body.dark-mode {
    font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Header */
body.dark-mode .navbar {
    background-color: var(--header-bg);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 1.5rem;
    height: 64px;
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

body.dark-mode .navbar-brand {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 500;
    font-size: 1.25rem;
    padding: 0;
    margin-right: 2rem;
}

body.dark-mode .navbar-brand i {
    color: var(--primary-color);
    margin-right: 12px;
    font-size: 24px;
}

body.dark-mode .navbar-light .navbar-brand {
    color: var(--text-color);
}

body.dark-mode .navbar-light .navbar-nav .nav-link {
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

body.dark-mode .navbar-light .navbar-nav .nav-link:hover,
body.dark-mode .navbar-light .navbar-nav .nav-link:focus {
    color: var(--text-color);
}

body.dark-mode .dropdown-menu {
    background-color: var(--card-bg);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 8px 0;
    margin-top: 8px;
}

body.dark-mode .dropdown-item {
    color: var(--text-color);
    padding: 8px 16px;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

body.dark-mode .dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background-color: var(--light-hover);
    color: var(--text-color);
}

body.dark-mode .dropdown-divider {
    border-top: 1px solid var(--border-color);
    margin: 4px 0;
}

/* Sidebar */
body.dark-mode .sidebar {
    position: fixed;
    top: 64px;
    bottom: 0;
    left: 0;
    width: 256px;
    background-color: var(--sidebar-bg);
    z-index: 100;
    overflow-y: auto;
    border-right: 1px solid var(--border-color);
    padding-top: 16px;
}

body.dark-mode .sidebar .nav-link {
    color: var(--text-color);
    padding: 12px 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    border-radius: 0 24px 24px 0;
    margin: 4px 0;
    transition: background-color 0.2s ease, color 0.2s ease;
}

body.dark-mode .sidebar .nav-link i {
    margin-right: 16px;
    width: 24px;
    text-align: center;
    font-size: 18px;
    color: var(--text-secondary);
}

body.dark-mode .sidebar .nav-link:hover {
    background-color: var(--light-hover);
    color: var(--text-color);
    text-decoration: none;
}

body.dark-mode .sidebar .nav-link.active {
    background-color: var(--sidebar-active-bg);
    color: var(--sidebar-active-color);
    font-weight: 500;
    border-left: 3px solid var(--primary-color);
}

body.dark-mode .sidebar .nav-link.active i {
    color: var(--sidebar-active-color);
}

/* Main content */
body.dark-mode .main-content {
    margin-left: 256px;
    padding: 24px;
    padding-top: 88px; /* Add padding to account for fixed navbar */
}

/* Fix for container padding inside main content */
body.dark-mode .main-content .container-fluid {
    padding-top: 1.5rem !important;
}

/* Page header */
body.dark-mode .page-header {
    margin-bottom: 24px;
}

body.dark-mode .page-header h1 {
    font-size: 24px;
    font-weight: 500;
    margin: 0;
    color: var(--text-color);
}

/* Cards */
body.dark-mode .card {
    background-color: var(--card-bg);
    border: none;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

body.dark-mode .card:hover {
    box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.3), 0 8px 12px 4px rgba(0, 0, 0, 0.2);
}

body.dark-mode .card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 16px 20px;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

body.dark-mode .card-header h5 {
    margin: 0;
    font-weight: 500;
    font-size: 16px;
    color: var(--text-color);
}

body.dark-mode .card-body {
    padding: 20px;
    background-color: var(--card-bg);
}

body.dark-mode .card-footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 12px 20px;
    display: flex;
    align-items: center;
}

/* Buttons */
body.dark-mode .btn {
    font-weight: 500;
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.2s ease;
    box-shadow: var(--button-shadow);
    text-transform: none;
    letter-spacing: 0.25px;
}

body.dark-mode .btn:focus,
body.dark-mode .btn:active {
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

body.dark-mode .btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
}

body.dark-mode .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

body.dark-mode .btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 4px 8px 3px rgba(0, 0, 0, 0.15);
}

body.dark-mode .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

body.dark-mode .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

body.dark-mode .btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

body.dark-mode .btn-success:hover {
    background-color: #0b8043;
    border-color: #0b8043;
}

body.dark-mode .btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

body.dark-mode .btn-danger:hover {
    background-color: #c53929;
    border-color: #c53929;
}

body.dark-mode .btn-secondary {
    background-color: #3C4043;
    border-color: #3C4043;
    color: var(--text-color);
}

body.dark-mode .btn-secondary:hover {
    background-color: #4A4D51;
    border-color: #4A4D51;
    color: var(--text-color);
}

/* Forms */
body.dark-mode .form-control {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px 12px;
    height: auto;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: var(--input-bg);
    color: var(--input-text);
}

body.dark-mode .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
    background-color: var(--input-bg);
    color: var(--input-text);
}

body.dark-mode .form-group label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 14px;
}

body.dark-mode .custom-control-label {
    font-weight: normal;
    color: var(--text-color);
}

body.dark-mode .custom-control-input:checked ~ .custom-control-label::before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

body.dark-mode .custom-checkbox .custom-control-label::before {
    border-radius: 2px;
}

body.dark-mode .form-text {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Tables */
body.dark-mode .table {
    color: var(--text-color);
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--card-bg);
}

body.dark-mode .table thead th {
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
    color: var(--text-secondary);
    background-color: var(--sidebar-bg);
    padding: 12px 16px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

body.dark-mode .table td {
    border-top: 1px solid var(--border-color);
    padding: 16px;
    vertical-align: middle;
    font-size: 14px;
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: var(--light-hover);
}

body.dark-mode .table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Status badges */
body.dark-mode .badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 16px;
    font-size: 12px;
    letter-spacing: 0.25px;
}

body.dark-mode .badge-success {
    background-color: var(--success-color);
    color: white;
}

body.dark-mode .badge-danger {
    background-color: var(--danger-color);
    color: white;
}

body.dark-mode .badge-warning {
    background-color: var(--warning-color);
    color: #212529;
}

body.dark-mode .badge-info {
    background-color: var(--info-color);
    color: white;
}

body.dark-mode .badge-secondary {
    background-color: #3C4043;
    color: var(--text-color);
}

/* Empty state */
body.dark-mode .empty-state {
    text-align: center;
    padding: 64px 0;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    margin: 24px 0;
}

body.dark-mode .empty-state-icon {
    font-size: 72px;
    color: var(--text-secondary);
    margin-bottom: 24px;
    opacity: 0.7;
}

body.dark-mode .empty-state-text {
    color: var(--text-secondary);
    margin-bottom: 24px;
    font-size: 16px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

body.dark-mode .empty-state h3 {
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--text-color);
}

/* User avatar */
body.dark-mode .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.2s ease;
}

body.dark-mode .user-avatar:hover {
    transform: scale(1.05);
}

/* Alerts */
body.dark-mode .alert {
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 24px;
    border: none;
    box-shadow: var(--card-shadow);
}

body.dark-mode .alert-success {
    background-color: rgba(15, 157, 88, 0.1);
    color: #81C995;
}

body.dark-mode .alert-danger {
    background-color: rgba(219, 68, 55, 0.1);
    color: #F28B82;
}

body.dark-mode .alert-warning {
    background-color: rgba(244, 180, 0, 0.1);
    color: #FDD663;
}

body.dark-mode .alert-info {
    background-color: rgba(66, 133, 244, 0.1);
    color: #8AB4F8;
}

/* List groups */
body.dark-mode .list-group-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
    transition: all 0.2s ease;
}

body.dark-mode .list-group-item-action:hover {
    background-color: var(--light-hover);
}

body.dark-mode .list-group-item.active {
    background-color: var(--sidebar-active-bg);
    border-left: 3px solid var(--primary-color);
    color: var(--primary-color);
}

/* Calendar */
body.dark-mode .calendar-day {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

body.dark-mode .calendar-event {
    background-color: var(--light-hover);
    border-left: 4px solid var(--primary-color);
}

body.dark-mode .calendar-event:hover {
    background-color: rgba(66, 133, 244, 0.15);
}

body.dark-mode .calendar-event.done {
    opacity: 0.7;
    border-left-color: var(--success-color);
}

/* Chart tabs */
body.dark-mode .chart-tabs .btn {
    min-width: 100px;
    transition: all 0.2s ease;
}

body.dark-mode .chart-tabs .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Utilities */
body.dark-mode .text-muted {
    color: var(--text-secondary) !important;
}

body.dark-mode .border-top {
    border-top: 1px solid var(--border-color) !important;
}

body.dark-mode .border-bottom {
    border-bottom: 1px solid var(--border-color) !important;
}

/* Print styles for dark mode */
@media print {
    body.dark-mode .navbar,
    body.dark-mode .sidebar,
    body.dark-mode .footer,
    body.dark-mode button,
    body.dark-mode .btn,
    body.dark-mode .dropdown-toggle {
        display: none !important;
    }

    /* Hide admin role alert box from printing */
    body.dark-mode .alert-info {
        display: none !important;
    }

    body.dark-mode .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    body.dark-mode .dashboard-section {
        page-break-after: always;
    }

    body.dark-mode .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        background-color: white !important;
        color: #333 !important;
    }

    body.dark-mode {
        background-color: white !important;
        color: #333 !important;
    }

    /* Print header styling for dark mode */
    body.dark-mode .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #333;
        color: #333 !important;
    }

    body.dark-mode .print-date {
        display: block !important;
        font-size: 12px;
        color: #666 !important;
        margin-top: 5px;
    }

    /* Ensure dashboard title is prominent in print */
    body.dark-mode h1 {
        font-size: 24px !important;
        margin-bottom: 10px !important;
        color: #333 !important;
    }

    body.dark-mode .text-muted {
        color: #666 !important;
    }
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    body.dark-mode .sidebar {
        width: 100%;
        position: relative;
        top: 0;
        height: auto;
        padding-top: 0;
        margin-top: 64px; /* Account for navbar height */
    }

    body.dark-mode .main-content {
        margin-left: 0;
        padding-top: 88px; /* Ensure content is below navbar on mobile */
    }

    /* Fix navbar on mobile */
    body.dark-mode .navbar {
        z-index: 1050;
    }

    /* Course grid for mobile */
    body.dark-mode .course-grid {
        grid-template-columns: 1fr;
    }
}
