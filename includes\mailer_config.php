<?php
/**
 * Mailer Configuration
 * 
 * This file contains the configuration for sending emails.
 * Update these settings with your SMTP server details.
 */

// SMTP Configuration
define('SMTP_HOST', 'smtp.gmail.com');  // SMTP server address
define('SMTP_PORT', 587);               // SMTP port (usually 587 for TLS, 465 for SSL)
define('SMTP_SECURE', 'tls');           // Security protocol: 'tls' or 'ssl'
define('SMTP_AUTH', true);              // Whether to use SMTP authentication
define('SMTP_USERNAME', '');            // Your SMTP username (usually your email address)
define('SMTP_PASSWORD', '');            // Your SMTP password or app password

// Email Settings
define('MAIL_FROM_EMAIL', APP_EMAIL);   // Sender email address
define('MAIL_FROM_NAME', APP_NAME);     // Sender name

// Debug Settings
define('MAIL_DEBUG', 0);                // Debug level (0 = off, 1 = client messages, 2 = client and server messages)
define('MAIL_CHARSET', 'UTF-8');        // Email character set
define('MAIL_ENCODING', '8bit');        // Email encoding

/**
 * For Gmail users:
 * 1. You need to enable "Less secure app access" or
 * 2. Create an "App Password" if you have 2-factor authentication enabled
 *    (Go to your Google Account > Security > App passwords)
 * 
 * For other email providers, check their documentation for SMTP settings.
 */
?>
