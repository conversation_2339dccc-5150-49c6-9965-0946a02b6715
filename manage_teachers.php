<?php
/**
 * Manage Teachers Page
 *
 * This page allows administrators to manage teacher accounts.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Process URL actions
$message = '';
$messageType = '';

// Handle teacher activation/deactivation/deletion via URL
if (isset($_GET['action']) && isset($_GET['id'])) {
    $teacherId = intval($_GET['id']);
    $action = $_GET['action'];

    // Verify the teacher exists and is actually a teacher
    try {
        $stmt = $pdo->prepare("
            SELECT u.* FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = ? AND r.role_name = 'teacher'
        ");
        $stmt->execute([$teacherId]);

        if ($stmt->rowCount() === 0) {
            $message = 'Invalid teacher ID';
            $messageType = 'danger';
        } else {
            $teacher = $stmt->fetch();

            if ($action === 'activate') {
                // Activate teacher
                $stmt = $pdo->prepare("UPDATE users SET is_active = 1 WHERE user_id = ?");
                $stmt->execute([$teacherId]);
                $message = 'Teacher account activated successfully';
                $messageType = 'success';
            } elseif ($action === 'deactivate') {
                // Deactivate teacher
                $stmt = $pdo->prepare("UPDATE users SET is_active = 0 WHERE user_id = ?");
                $stmt->execute([$teacherId]);
                $message = 'Teacher account deactivated successfully';
                $messageType = 'success';
            } elseif ($action === 'delete') {
                // Delete teacher
                $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
                $stmt->execute([$teacherId]);
                $message = 'Teacher account deleted successfully';
                $messageType = 'success';
            }
        }
    } catch (PDOException $e) {
        $message = 'Database error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle teacher creation
if (isset($_POST['create_teacher'])) {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $firstName = trim($_POST['first_name']);
    $lastName = trim($_POST['last_name']);

    // Validate input
    if (empty($username) || empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
        $message = 'All fields are required';
        $messageType = 'danger';
    } else {
        try {
            // Check if username or email already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetchColumn() > 0) {
                $message = 'Username or email already exists';
                $messageType = 'danger';
            } else {
                // Get teacher role ID
                $stmt = $pdo->prepare("SELECT role_id FROM roles WHERE role_name = 'teacher'");
                $stmt->execute();
                $teacherRoleId = $stmt->fetchColumn();

                // Create new teacher account
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, role_id, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $teacherRoleId]);

                $message = 'Teacher account created successfully';
                $messageType = 'success';
            }
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Handle teacher update
if (isset($_POST['update_teacher'])) {
    $teacherId = $_POST['teacher_id'];
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $firstName = trim($_POST['first_name']);
    $lastName = trim($_POST['last_name']);

    // Validate input
    if (empty($username) || empty($email) || empty($firstName) || empty($lastName)) {
        $message = 'All fields except password are required';
        $messageType = 'danger';
    } else {
        try {
            // Check if teacher exists and is actually a teacher
            $stmt = $pdo->prepare("
                SELECT u.* FROM users u
                JOIN roles r ON u.role_id = r.role_id
                WHERE u.user_id = ? AND r.role_name = 'teacher'
            ");
            $stmt->execute([$teacherId]);
            if ($stmt->rowCount() === 0) {
                $message = 'Invalid teacher ID';
                $messageType = 'danger';
            } else {
                // Check if username or email already exists for other users
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE (username = ? OR email = ?) AND user_id != ?");
                $stmt->execute([$username, $email, $teacherId]);
                if ($stmt->fetchColumn() > 0) {
                    $message = 'Username or email already exists for another user';
                    $messageType = 'danger';
                } else {
                    // Update teacher information
                    if (!empty($password)) {
                        // Update with new password
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("
                            UPDATE users
                            SET username = ?, email = ?, password = ?, first_name = ?, last_name = ?
                            WHERE user_id = ?
                        ");
                        $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $teacherId]);
                    } else {
                        // Update without changing password
                        $stmt = $pdo->prepare("
                            UPDATE users
                            SET username = ?, email = ?, first_name = ?, last_name = ?
                            WHERE user_id = ?
                        ");
                        $stmt->execute([$username, $email, $firstName, $lastName, $teacherId]);
                    }

                    $message = 'Teacher account updated successfully';
                    $messageType = 'success';
                }
            }
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Handle teacher deletion
if (isset($_POST['delete_teacher'])) {
    $teacherId = $_POST['teacher_id'];

    try {
        // Check if teacher exists and is actually a teacher
        $stmt = $pdo->prepare("
            SELECT u.* FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = ? AND r.role_name = 'teacher'
        ");
        $stmt->execute([$teacherId]);
        if ($stmt->rowCount() === 0) {
            $message = 'Invalid teacher ID';
            $messageType = 'danger';
        } else {
            // Delete teacher
            $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
            $stmt->execute([$teacherId]);

            $message = 'Teacher account deleted successfully';
            $messageType = 'success';
        }
    } catch (PDOException $e) {
        $message = 'Database error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle teacher activation/deactivation
if (isset($_POST['toggle_teacher_status'])) {
    $teacherId = $_POST['teacher_id'];
    $newStatus = $_POST['new_status'];

    try {
        // Check if teacher exists and is actually a teacher
        $stmt = $pdo->prepare("
            SELECT u.* FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = ? AND r.role_name = 'teacher'
        ");
        $stmt->execute([$teacherId]);
        if ($stmt->rowCount() === 0) {
            $message = 'Invalid teacher ID';
            $messageType = 'danger';
        } else {
            // Update teacher status
            $stmt = $pdo->prepare("UPDATE users SET is_active = ? WHERE user_id = ?");
            $stmt->execute([$newStatus, $teacherId]);

            $statusText = $newStatus ? 'activated' : 'deactivated';
            $message = "Teacher account {$statusText} successfully";
            $messageType = 'success';
        }
    } catch (PDOException $e) {
        $message = 'Database error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get all teachers
try {
    $stmt = $pdo->prepare("
        SELECT u.* FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE r.role_name = 'teacher'
        ORDER BY u.last_name, u.first_name
    ");
    $stmt->execute();
    $teachers = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = 'Database error: ' . $e->getMessage();
    $messageType = 'danger';
    $teachers = [];
}

// Page title
$pageTitle = 'Manage Teachers';

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-0">Manage Teachers</h1>
            <p class="text-muted">Create, edit, and manage teacher accounts</p>
        </div>
        <div class="col-md-4 text-right">
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addTeacherModal">
                <i class="fas fa-plus"></i> Add New Teacher
            </button>
        </div>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-white">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Teacher Accounts</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" id="teacherSearch" class="form-control" placeholder="Search teachers...">
                        <div class="input-group-append">
                            <button type="button" id="teacherSearchBtn" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="teacherTableBody">
                    <?php if (count($teachers) > 0): ?>
                        <?php foreach ($teachers as $teacher): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></td>
                                <td><?php echo htmlspecialchars($teacher['username']); ?></td>
                                <td><?php echo htmlspecialchars($teacher['email']); ?></td>
                                <td>
                                    <?php if ($teacher['is_active']): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M d, Y', strtotime($teacher['created_at'])); ?></td>
                                <td>
                                    <div style="display: flex; gap: 5px;">
                                        <a href="user_edit.php?id=<?php echo $teacher['user_id']; ?>&role=teacher" class="btn btn-sm btn-primary" title="Edit" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($teacher['is_active']): ?>
                                        <a href="manage_teachers.php?action=deactivate&id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-warning" title="Deactivate" onclick="return confirm('Are you sure you want to deactivate this teacher?');" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-user-slash"></i>
                                        </a>
                                        <?php else: ?>
                                        <a href="manage_teachers.php?action=activate&id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-success" title="Activate" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-user-check"></i>
                                        </a>
                                        <?php endif; ?>
                                        <a href="manage_teachers.php?action=delete&id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this teacher? This action cannot be undone.');" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">No teacher accounts found</td>
                        </tr>
                    <?php endif; ?>
                    <tr id="noTeacherResults" style="display: none;">
                        <td colspan="6" class="text-center py-4">No matching teacher accounts found</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Teacher Modal -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" role="dialog" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTeacherModalLabel">Add New Teacher</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="manage_teachers.php" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" required>
                    </div>
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" required>
                    </div>
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_teacher" class="btn btn-primary">Create Teacher</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Teacher Modal -->
<div class="modal fade" id="editTeacherModal" tabindex="-1" role="dialog" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTeacherModalLabel">Edit Teacher</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="manage_teachers.php" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_teacher_id" name="teacher_id">
                    <div class="form-group">
                        <label for="edit_first_name">First Name</label>
                        <input type="text" class="form-control" id="edit_first_name" name="first_name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_last_name">Last Name</label>
                        <input type="text" class="form-control" id="edit_last_name" name="last_name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_username">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_email">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_password">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_teacher" class="btn btn-primary">Update Teacher</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Teacher Modal -->
<div class="modal fade" id="deleteTeacherModal" tabindex="-1" role="dialog" aria-labelledby="deleteTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteTeacherModalLabel">Delete Teacher</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the teacher account for <strong id="deleteTeacherName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <form action="manage_teachers.php" method="post">
                    <input type="hidden" id="deleteTeacherId" name="teacher_id">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_teacher" class="btn btn-danger">Delete Teacher</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1" role="dialog" aria-labelledby="toggleStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleStatusModalLabel">Change Account Status</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="toggleStatusMessage"></p>
            </div>
            <div class="modal-footer">
                <form action="manage_teachers.php" method="post">
                    <input type="hidden" id="toggleStatusId" name="teacher_id">
                    <input type="hidden" id="toggleStatusValue" name="new_status">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="toggle_teacher_status" id="toggleStatusButton" class="btn">Confirm</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Handle edit teacher button clicks
        $(document).on('click', '.edit-teacher', function() {
            var teacherId = $(this).data('id');
            var firstName = $(this).data('firstname');
            var lastName = $(this).data('lastname');
            var username = $(this).data('username');
            var email = $(this).data('email');

            $('#edit_teacher_id').val(teacherId);
            $('#edit_first_name').val(firstName);
            $('#edit_last_name').val(lastName);
            $('#edit_username').val(username);
            $('#edit_email').val(email);
            $('#edit_password').val(''); // Clear password field

            $('#editTeacherModal').modal('show');
        });

        // Handle delete teacher button clicks
        $(document).on('click', '.delete-teacher', function() {
            var teacherId = $(this).data('id');
            var teacherName = $(this).data('name');

            $('#deleteTeacherId').val(teacherId);
            $('#deleteTeacherName').text(teacherName);
            $('#deleteTeacherModal').modal('show');
        });

        // Handle toggle status button clicks
        $(document).on('click', '.toggle-status', function() {
            var teacherId = $(this).data('id');
            var teacherName = $(this).data('name');
            var currentStatus = $(this).data('status');
            var newStatus = currentStatus == 1 ? 0 : 1;
            var statusText = newStatus == 1 ? 'activate' : 'deactivate';
            var buttonClass = newStatus == 1 ? 'btn-success' : 'btn-warning';

            $('#toggleStatusId').val(teacherId);
            $('#toggleStatusValue').val(newStatus);
            $('#toggleStatusMessage').html('Are you sure you want to <strong>' + statusText + '</strong> the account for <strong>' + teacherName + '</strong>?');
            $('#toggleStatusButton').removeClass('btn-success btn-warning').addClass(buttonClass).text(newStatus == 1 ? 'Activate' : 'Deactivate');
            $('#toggleStatusModal').modal('show');
        });

        // Search functionality is now in js/search.js
    });
</script>

<?php
require_once 'includes/footer.php';
?>
