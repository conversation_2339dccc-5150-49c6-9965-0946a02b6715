<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/quiz_functions.php';

// Include header
require_once 'includes/header.php';

// Check if the user is logged in and is an admin or teacher
if (!isLoggedIn() || (!isAdmin() && !isTeacher())) {
    echo "<div class='alert alert-danger'>You do not have permission to access this page.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Set activity ID
$activityId = 7; // Change this to your quiz ID

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists and is a quiz
if (is_string($activity)) {
    echo "<div class='alert alert-danger'>$activity</div>";
    require_once 'includes/footer.php';
    exit;
}

if ($activity['activity_type'] != 'quiz') {
    echo "<div class='alert alert-danger'>This activity is not a quiz.</div>";
    require_once 'includes/footer.php';
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    echo "<div class='alert alert-danger'>$course</div>";
    require_once 'includes/footer.php';
    exit;
}

// Check if user is authorized to edit this quiz
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        echo "<div class='alert alert-danger'>You are not authorized to manage questions for this quiz.</div>";
        require_once 'includes/footer.php';
        exit;
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['add_test_question'])) {
        // Add a test question
        $questionText = "Test question added directly";
        $questionType = "multiple_choice";
        $points = 1;
        
        // Add the question
        $result = addQuizActivityQuestion($activityId, $questionText, $questionType, $points);
        
        if (is_numeric($result)) {
            $questionId = $result;
            echo "<div class='alert alert-success'>Question added successfully with ID: $questionId</div>";
            
            // Add options
            $optionA = "Option A";
            $optionB = "Option B";
            $optionC = "Option C";
            $optionD = "Option D";
            
            // Add options with option A as correct
            $optionResult = addQuizOption($questionId, $optionA, true);
            if (!is_numeric($optionResult)) {
                echo "<div class='alert alert-danger'>Failed to add option A: $optionResult</div>";
            } else {
                echo "<div class='alert alert-success'>Option A added successfully</div>";
            }
            
            $optionResult = addQuizOption($questionId, $optionB, false);
            if (!is_numeric($optionResult)) {
                echo "<div class='alert alert-danger'>Failed to add option B: $optionResult</div>";
            } else {
                echo "<div class='alert alert-success'>Option B added successfully</div>";
            }
            
            $optionResult = addQuizOption($questionId, $optionC, false);
            if (!is_numeric($optionResult)) {
                echo "<div class='alert alert-danger'>Failed to add option C: $optionResult</div>";
            } else {
                echo "<div class='alert alert-success'>Option C added successfully</div>";
            }
            
            $optionResult = addQuizOption($questionId, $optionD, false);
            if (!is_numeric($optionResult)) {
                echo "<div class='alert alert-danger'>Failed to add option D: $optionResult</div>";
            } else {
                echo "<div class='alert alert-success'>Option D added successfully</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>Failed to add question: $result</div>";
        }
    }
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Test Add Question</h1>
            <p>This page tests adding a question directly to quiz ID <?php echo $activityId; ?>.</p>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add Test Question</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="form-group text-right">
                            <input type="hidden" name="add_test_question" value="1">
                            <button type="submit" class="btn btn-success">Add Test Question</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <a href="quiz_edit.php?id=<?php echo $activityId; ?>#questions" class="btn btn-primary">Return to Quiz Edit Page</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
