<?php
// People tab content - shows teachers and students
?>
<div id="people-tab" class="course-tab-content">
    <!-- Teachers Section -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Instructors</h5>
            <?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#assignInstructorModal">
                <i class="fas fa-user-plus"></i> Add Instructor
            </button>
            <?php endif; ?>
        </div>
        <div class="list-group list-group-flush">
            <!-- Course Creator -->
            <div class="list-group-item d-flex align-items-center">
                <div class="user-avatar mr-3">
                    <?php echo strtoupper(substr($course['creator_name'], 0, 1)); ?>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-0"><?php echo htmlspecialchars($course['creator_name']); ?></h6>
                    <small class="text-muted">Course Creator</small>
                </div>
                <span class="badge badge-info">Creator</span>
            </div>

            <!-- Course Instructors -->
            <?php if (count($instructors) > 0): ?>
                <?php foreach ($instructors as $instructor): ?>
                <div class="list-group-item d-flex align-items-center">
                    <div class="user-avatar mr-3">
                        <?php echo strtoupper(substr($instructor['first_name'], 0, 1)); ?>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0"><?php echo htmlspecialchars($instructor['first_name'] . ' ' . $instructor['last_name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($instructor['email']); ?></small>
                    </div>
                    <span class="badge badge-success mr-2">Instructor</span>
                    <?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
                    <form action="course_instructor_remove.php" method="post" class="d-inline">
                        <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                        <input type="hidden" name="instructor_id" value="<?php echo $instructor['user_id']; ?>">
                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                onclick="return confirm('Are you sure you want to remove this instructor?');">
                            <i class="fas fa-user-minus"></i>
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Enrollment Requests Section -->
    <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
    <?php
    // Get count of pending enrollment requests
    $requestCount = countPendingEnrollmentRequests($courseId);
    ?>
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Enrollment Requests</h5>
            <a href="course_enrollment_requests.php?id=<?php echo $courseId; ?>" class="btn btn-sm btn-warning">
                <i class="fas fa-user-slash"></i> View Requests
                <?php if ($requestCount > 0): ?>
                <span class="badge badge-light ml-1"><?php echo $requestCount; ?></span>
                <?php endif; ?>
            </a>
        </div>
        <div class="card-body">
            <p class="text-muted mb-0">
                <?php if ($requestCount > 0): ?>
                There are <strong><?php echo $requestCount; ?></strong> pending enrollment requests for this course.
                <?php else: ?>
                There are no pending enrollment requests for this course.
                <?php endif; ?>
            </p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Students Section -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Students</h5>
            <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#inviteStudentModal">
                <i class="fas fa-user-plus"></i> Invite Students
            </button>
            <?php endif; ?>
        </div>
        <div class="list-group list-group-flush">
            <?php if (count($students) > 0): ?>
                <?php foreach ($students as $student): ?>
                <div class="list-group-item d-flex align-items-center">
                    <div class="user-avatar mr-3">
                        <?php echo strtoupper(substr($student['first_name'], 0, 1)); ?>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                    </div>
                    <?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
                    <form action="course_student_remove.php" method="post" class="d-inline">
                        <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                        <input type="hidden" name="student_id" value="<?php echo $student['user_id']; ?>">
                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                onclick="return confirm('Are you sure you want to remove this student from the course?');">
                            <i class="fas fa-user-minus"></i>
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="list-group-item text-center py-4">
                    <p class="text-muted mb-0">No students enrolled yet</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
<!-- Assign Instructor Modal -->
<div class="modal fade" id="assignInstructorModal" tabindex="-1" role="dialog" aria-labelledby="assignInstructorModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignInstructorModalLabel">Assign Instructor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="course_instructor_assign.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                    <div class="form-group">
                        <label for="instructor_id">Select Teacher</label>
                        <select name="instructor_id" id="instructor_id" class="form-control" required>
                            <option value="">Select a teacher...</option>
                            <?php
                            // Get all teachers
                            $teachers = getUsersByRole('teacher');
                            if (!is_string($teachers)) {
                                foreach ($teachers as $teacher) {
                                    // Skip teachers who are already assigned
                                    $alreadyAssigned = false;
                                    foreach ($instructors as $instructor) {
                                        if ($instructor['user_id'] == $teacher['user_id']) {
                                            $alreadyAssigned = true;
                                            break;
                                        }
                                    }

                                    // Skip the course creator
                                    if ($teacher['user_id'] == $course['created_by']) {
                                        continue;
                                    }

                                    if (!$alreadyAssigned) {
                                        echo '<option value="' . $teacher['user_id'] . '">' .
                                            htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']) .
                                            ' (' . htmlspecialchars($teacher['email']) . ')' .
                                            '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId))): ?>
<!-- Invite Student Modal -->
<div class="modal fade" id="inviteStudentModal" tabindex="-1" role="dialog" aria-labelledby="inviteStudentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inviteStudentModalLabel">Invite Students</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="course_student_invite.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                    <div class="form-group">
                        <label for="student_ids">Select Students</label>
                        <select name="student_ids[]" id="student_ids" class="form-control" multiple required>
                            <?php
                            // Get all students
                            $allStudents = getUsersByRole('student');
                            if (!is_string($allStudents)) {
                                foreach ($allStudents as $student) {
                                    // Skip students who are already enrolled
                                    $alreadyEnrolled = false;
                                    foreach ($students as $enrolledStudent) {
                                        if ($enrolledStudent['user_id'] == $student['user_id']) {
                                            $alreadyEnrolled = true;
                                            break;
                                        }
                                    }

                                    if (!$alreadyEnrolled) {
                                        echo '<option value="' . $student['user_id'] . '">' .
                                            htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) .
                                            ' (' . htmlspecialchars($student['email']) . ')' .
                                            '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small class="form-text text-muted">Hold Ctrl (or Cmd on Mac) to select multiple students</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Invite</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
