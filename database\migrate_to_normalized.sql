-- Data Migration Script: Old Tables to Normalized Tables
-- This script migrates data from existing tables to the new normalized structure

-- =====================================================
-- STEP 1: MIGRATE USERS DATA
-- =====================================================

-- Migrate users to normalized structure
INSERT INTO users_normalized (
    user_id, username, email, password_hash, role_id, last_login, created_at, updated_at
)
SELECT 
    u.user_id,
    u.username,
    u.email,
    u.password,
    CASE 
        WHEN u.role = 'admin' THEN 1
        WHEN u.role = 'teacher' OR u.role = 'instructor' THEN 2
        ELSE 3
    END as role_id,
    u.last_login,
    u.created_at,
    u.updated_at
FROM users u
WHERE NOT EXISTS (SELECT 1 FROM users_normalized un WHERE un.user_id = u.user_id);

-- Migrate user profiles
INSERT INTO user_profiles (
    user_id, first_name, last_name, gender, date_of_birth, phone_number, 
    profile_picture_url, created_at, updated_at
)
SELECT 
    u.user_id,
    u.first_name,
    u.last_name,
    u.gender,
    u.birthday,
    u.phone_number,
    u.profile_picture,
    u.created_at,
    u.updated_at
FROM users u
WHERE NOT EXISTS (SELECT 1 FROM user_profiles up WHERE up.user_id = u.user_id);

-- =====================================================
-- STEP 2: MIGRATE COURSES DATA
-- =====================================================

-- Migrate courses to normalized structure
INSERT INTO courses_normalized (
    course_id, course_code, title, description, instructor_id, 
    capacity_limit, is_published, is_archived, created_at, updated_at
)
SELECT 
    c.course_id,
    CONCAT('COURSE_', c.course_id) as course_code,
    c.title,
    c.description,
    c.created_by,
    c.capacity,
    c.is_active,
    COALESCE(c.is_archived, 0),
    c.created_at,
    c.updated_at
FROM courses c
WHERE NOT EXISTS (SELECT 1 FROM courses_normalized cn WHERE cn.course_id = c.course_id);

-- Migrate course instructors (if course_instructors table exists)
INSERT IGNORE INTO course_instructors (course_id, instructor_id, role_type, assigned_at, assigned_by)
SELECT 
    c.course_id,
    c.created_by,
    'primary',
    c.created_at,
    c.created_by
FROM courses c
WHERE NOT EXISTS (
    SELECT 1 FROM course_instructors ci 
    WHERE ci.course_id = c.course_id AND ci.instructor_id = c.created_by
);

-- =====================================================
-- STEP 3: MIGRATE ENROLLMENTS DATA
-- =====================================================

-- Migrate enrollments to normalized structure
INSERT INTO enrollments_normalized (
    student_id, course_id, enrollment_status, enrollment_date, 
    completion_date, final_grade, is_archived
)
SELECT 
    e.user_id,
    e.course_id,
    CASE 
        WHEN e.completion_status = 'completed' THEN 'completed'
        WHEN e.completion_status = 'in_progress' THEN 'active'
        ELSE 'active'
    END as enrollment_status,
    e.enrollment_date,
    CASE WHEN e.completion_status = 'completed' THEN e.enrollment_date ELSE NULL END,
    NULL as final_grade,
    COALESCE(e.is_archived, 0)
FROM enrollments e
WHERE NOT EXISTS (
    SELECT 1 FROM enrollments_normalized en 
    WHERE en.student_id = e.user_id AND en.course_id = e.course_id
);

-- =====================================================
-- STEP 4: MIGRATE ACTIVITIES DATA
-- =====================================================

-- Migrate activities to normalized structure
INSERT INTO activities_normalized (
    activity_id, course_id, type_id, title, description, instructions,
    points_possible, due_date, is_published, is_archived, created_by, created_at, updated_at
)
SELECT 
    a.activity_id,
    a.course_id,
    CASE 
        WHEN a.activity_type = 'material' THEN 1
        WHEN a.activity_type = 'assignment' THEN 2
        WHEN a.activity_type = 'quiz' THEN 3
        WHEN a.activity_type = 'question' THEN 3
        ELSE 2
    END as type_id,
    a.title,
    a.description,
    a.instructions,
    COALESCE(a.points, 0),
    a.due_date,
    COALESCE(a.is_published, 1),
    COALESCE(a.is_archived, 0),
    a.created_by,
    a.created_at,
    a.updated_at
FROM activities a
WHERE NOT EXISTS (SELECT 1 FROM activities_normalized an WHERE an.activity_id = a.activity_id);

-- =====================================================
-- STEP 5: MIGRATE QUESTIONS DATA
-- =====================================================

-- Migrate quiz questions to normalized structure
INSERT INTO questions_normalized (
    question_id, activity_id, type_id, question_text, points_possible, 
    position_order, correct_answer_text, created_at, updated_at
)
SELECT 
    qq.question_id,
    qq.activity_id,
    CASE 
        WHEN qq.question_type = 'multiple_choice' THEN 1
        WHEN qq.question_type = 'multiple_select' THEN 2
        WHEN qq.question_type = 'true_false' THEN 3
        WHEN qq.question_type = 'short_answer' THEN 4
        WHEN qq.question_type = 'essay' THEN 5
        ELSE 4
    END as type_id,
    qq.question_text,
    COALESCE(qq.points, 1),
    COALESCE(qq.position, 0),
    qq.correct_answer,
    qq.created_at,
    qq.updated_at
FROM quiz_questions qq
WHERE NOT EXISTS (SELECT 1 FROM questions_normalized qn WHERE qn.question_id = qq.question_id);

-- Migrate quiz options to normalized structure
INSERT INTO question_options (
    option_id, question_id, option_text, is_correct, position_order, created_at
)
SELECT 
    qo.option_id,
    qo.question_id,
    qo.option_text,
    COALESCE(qo.is_correct, 0),
    COALESCE(qo.position, 0),
    qo.created_at
FROM quiz_options qo
WHERE NOT EXISTS (SELECT 1 FROM question_options qon WHERE qon.option_id = qo.option_id);

-- =====================================================
-- STEP 6: MIGRATE SUBMISSIONS DATA
-- =====================================================

-- Migrate activity submissions to normalized structure
INSERT INTO submissions_normalized (
    submission_id, activity_id, student_id, submission_status, submitted_at,
    graded_at, graded_by, points_earned, points_possible, feedback, is_late, created_at, updated_at
)
SELECT 
    s.submission_id,
    s.activity_id,
    s.user_id,
    CASE 
        WHEN s.status = 'submitted' THEN 'submitted'
        WHEN s.status = 'graded' THEN 'graded'
        ELSE 'draft'
    END as submission_status,
    s.submitted_at,
    s.graded_at,
    s.graded_by,
    s.score,
    s.total_points,
    s.feedback,
    COALESCE(s.is_late, 0),
    s.created_at,
    s.updated_at
FROM activity_submissions s
WHERE NOT EXISTS (SELECT 1 FROM submissions_normalized sn WHERE sn.submission_id = s.submission_id);

-- =====================================================
-- STEP 7: MIGRATE ANNOUNCEMENTS DATA
-- =====================================================

-- Migrate announcements to normalized structure
INSERT INTO announcements_normalized (
    announcement_id, course_id, title, content, is_pinned, is_published,
    publish_date, created_by, created_at, updated_at
)
SELECT 
    a.announcement_id,
    a.course_id,
    a.title,
    a.content,
    COALESCE(a.is_pinned, 0),
    COALESCE(a.is_published, 1),
    COALESCE(a.publish_date, a.created_at),
    a.created_by,
    a.created_at,
    a.updated_at
FROM announcements a
WHERE NOT EXISTS (SELECT 1 FROM announcements_normalized an WHERE an.announcement_id = a.announcement_id);

-- =====================================================
-- STEP 8: MIGRATE SYSTEM SETTINGS
-- =====================================================

-- Migrate system settings to normalized structure
INSERT INTO system_settings_normalized (
    setting_key, setting_value, setting_type, setting_description, is_public, created_at, updated_at
)
SELECT 
    s.setting_key,
    s.setting_value,
    'string' as setting_type,
    s.setting_description,
    COALESCE(s.is_public, 0),
    s.created_at,
    s.updated_at
FROM system_settings s
WHERE NOT EXISTS (SELECT 1 FROM system_settings_normalized sn WHERE sn.setting_key = s.setting_key);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Show migration results
SELECT 'Users Migration' as migration_step, 
       (SELECT COUNT(*) FROM users) as original_count,
       (SELECT COUNT(*) FROM users_normalized) as migrated_count;

SELECT 'User Profiles Migration' as migration_step,
       (SELECT COUNT(*) FROM users) as original_count,
       (SELECT COUNT(*) FROM user_profiles) as migrated_count;

SELECT 'Courses Migration' as migration_step,
       (SELECT COUNT(*) FROM courses) as original_count,
       (SELECT COUNT(*) FROM courses_normalized) as migrated_count;

SELECT 'Enrollments Migration' as migration_step,
       (SELECT COUNT(*) FROM enrollments) as original_count,
       (SELECT COUNT(*) FROM enrollments_normalized) as migrated_count;

SELECT 'Activities Migration' as migration_step,
       (SELECT COUNT(*) FROM activities) as original_count,
       (SELECT COUNT(*) FROM activities_normalized) as migrated_count;
