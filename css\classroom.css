/* Google Classroom-inspired CSS */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #5f6368;
    --accent-color: #1967d2;
    --background-color: #f1f3f4;
    --card-color: #ffffff;
    --border-color: #dadce0;
    --success-color: #34a853;
    --warning-color: #fbbc05;
    --danger-color: #ea4335;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --header-height: 64px;
    --sidebar-width: 256px;
}

body {
    font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

/* Header */
.classroom-header {
    height: var(--header-height);
    background-color: var(--card-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 16px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
}

.classroom-logo {
    display: flex;
    align-items: center;
    font-size: 22px;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    margin-right: 24px;
}

.classroom-logo i {
    margin-right: 8px;
    color: var(--primary-color);
}

.classroom-header-right {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.classroom-header-right .btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    color: var(--secondary-color);
    background: transparent;
    border: none;
    cursor: pointer;
}

.classroom-header-right .btn-icon:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    cursor: pointer;
}

/* Main container */
.classroom-container {
    display: flex;
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

/* Sidebar */
.classroom-sidebar {
    width: var(--sidebar-width);
    background-color: var(--card-color);
    border-right: 1px solid var(--border-color);
    padding: 16px 0;
    position: fixed;
    top: var(--header-height);
    bottom: 0;
    left: 0;
    overflow-y: auto;
}

.classroom-sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.classroom-sidebar-item {
    padding: 0 24px;
    margin: 2px 0;
}

.classroom-sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 0 24px 24px 0;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s;
}

.classroom-sidebar-link:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

.classroom-sidebar-link.active {
    background-color: #e8f0fe;
    color: var(--primary-color);
}

.classroom-sidebar-link i {
    margin-right: 16px;
    width: 24px;
    text-align: center;
}

/* Main content */
.classroom-content {
    flex: 1;
    padding: 24px;
    margin-left: var(--sidebar-width);
}

/* Course cards */
.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

.course-card {
    background-color: var(--card-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    transition: box-shadow 0.2s;
    position: relative;
}

.course-card:hover {
    box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
}

.course-card-header {
    height: 100px;
    background-color: var(--primary-color);
    padding: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.course-card-title {
    color: white;
    font-size: 20px;
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
}

.course-card-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0;
}

.course-card-body {
    padding: 16px;
}

.course-card-footer {
    padding: 8px 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

/* Activity completion badge */
.badge-success {
    background-color: var(--success-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Create menu styles */
.create-menu-container {
    position: relative;
}

.create-menu {
    position: absolute;
    z-index: 9999;
    top: 40px;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    min-width: 200px;
    display: none;
}

.create-menu-item {
    display: block;
    padding: 8px 16px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s ease;
}

.create-menu-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #007bff;
}

.dropdown-divider {
    border-top: 1px solid #ddd;
    margin: 4px 0;
}

/* Bootstrap dropdown fixes */
.dropdown-menu {
    display: none;
}

.dropdown-menu.show {
    display: block !important;
    z-index: 1050 !important;
    position: absolute !important;
}

.btn-group.show .dropdown-menu {
    display: block !important;
    z-index: 1050 !important;
    position: absolute !important;
}

/* Create button styling - Google Classroom style */
.btn-group .btn-primary.dropdown-toggle {
    background-color: #4285f4;
    border-color: #4285f4;
    border-radius: 4px;
    font-weight: 500;
    padding: 8px 16px;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
    color: white;
    font-size: 14px;
    min-width: 110px;
    text-align: center;
}

.btn-group .btn-primary.dropdown-toggle .fas {
    margin-right: 4px;
    font-size: 14px;
}

.btn-group .btn-primary.dropdown-toggle:hover {
    background-color: #1a73e8;
    border-color: #1a73e8;
    box-shadow: 0 1px 3px rgba(60, 64, 67, 0.3);
}

.btn-group .btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

/* Dropdown menu styling - Google Classroom style */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: none;
    padding: 8px 0;
    min-width: 200px;
    background-color: white;
}

.dropdown-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #3c4043;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.dropdown-item i {
    width: 24px;
    margin-right: 16px;
    font-size: 18px;
    color: #5f6368;
    text-align: center;
}

.dropdown-item:hover {
    background-color: #f1f3f4;
    text-decoration: none;
}

.dropdown-divider {
    margin: 4px 0;
    border-top: 1px solid #e0e0e0;
}

/* Buttons */
.btn-classroom {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-classroom:hover {
    background-color: var(--accent-color);
}

.btn-classroom-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 8px 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-classroom-outline:hover {
    background-color: rgba(26, 115, 232, 0.04);
}

/* Course view */
.course-header {
    background-color: var(--primary-color);
    color: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
}

.course-title {
    font-size: 32px;
    margin: 0 0 8px 0;
}

.course-description {
    opacity: 0.8;
    margin: 0;
}

.course-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.course-tab {
    padding: 16px 24px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
}

.course-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Stream items */
.stream-item {
    background-color: var(--card-color);
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3);
    margin-bottom: 16px;
    overflow: hidden;
}

.stream-item-header {
    padding: 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.stream-item-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.stream-item-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}

.stream-item-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 4px 0 0 0;
}

.stream-item-body {
    padding: 16px;
}

.stream-item-footer {
    padding: 8px 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}
