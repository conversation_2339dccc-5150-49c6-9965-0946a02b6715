<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit quiz options.";
    header("location: index.php");
    exit;
}

// Check if question ID is provided
if (!isset($_GET['question_id']) || empty($_GET['question_id'])) {
    header("location: index.php");
    exit;
}

$questionId = intval($_GET['question_id']);

// Get question details
$stmt = $pdo->prepare("
    SELECT q.*, a.activity_id, a.title as activity_title, a.course_id
    FROM quiz_questions q
    JOIN activities a ON q.activity_id = a.activity_id
    WHERE q.question_id = :questionId
");
$stmt->bindParam(':questionId', $questionId);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    $_SESSION['error'] = "Question not found.";
    header("location: index.php");
    exit;
}

$question = $stmt->fetch();
$activityId = $question['activity_id'];
$courseId = $question['course_id'];

// Check if question type supports options
if (!in_array($question['question_type'], ['multiple_choice', 'checkbox', 'dropdown'])) {
    $_SESSION['error'] = "This question type does not support options.";
    header("location: quiz_edit.php?id=$activityId");
    exit;
}

// Check if user is authorized to edit this quiz
if (!isAdmin()) {
    $stmt = $pdo->prepare("
        SELECT c.created_by 
        FROM courses c
        WHERE c.course_id = :courseId
    ");
    $stmt->bindParam(':courseId', $courseId);
    $stmt->execute();
    
    if ($stmt->rowCount() == 1) {
        $course = $stmt->fetch();
        $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
        
        if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
            $_SESSION['error'] = "You are not authorized to edit quizzes for this course.";
            header("location: course_view_full.php?id=$courseId");
            exit;
        }
    } else {
        $_SESSION['error'] = "Course not found.";
        header("location: index.php");
        exit;
    }
}

// Get existing options
$stmt = $pdo->prepare("
    SELECT * FROM quiz_options
    WHERE question_id = :questionId
    ORDER BY position, option_id
");
$stmt->bindParam(':questionId', $questionId);
$stmt->execute();
$options = $stmt->fetchAll();

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Check which form was submitted
    if (isset($_POST['add_option'])) {
        // Add a new option
        $optionText = trim($_POST['option_text']);
        $isCorrect = isset($_POST['is_correct']) ? true : false;
        
        if (empty($optionText)) {
            $error = "Option text cannot be empty.";
        } else {
            $result = addQuizOption($questionId, $optionText, $isCorrect);
            
            if (is_numeric($result)) {
                $success = "Option added successfully.";
                
                // Refresh options
                $stmt = $pdo->prepare("
                    SELECT * FROM quiz_options
                    WHERE question_id = :questionId
                    ORDER BY position, option_id
                ");
                $stmt->bindParam(':questionId', $questionId);
                $stmt->execute();
                $options = $stmt->fetchAll();
            } else {
                $error = $result;
            }
        }
    } elseif (isset($_POST['delete_option']) && isset($_POST['option_id'])) {
        // Delete an option
        $optionId = intval($_POST['option_id']);
        
        $stmt = $pdo->prepare("DELETE FROM quiz_options WHERE option_id = :optionId");
        $stmt->bindParam(':optionId', $optionId);
        $stmt->execute();
        
        $success = "Option deleted successfully.";
        
        // Refresh options
        $stmt = $pdo->prepare("
            SELECT * FROM quiz_options
            WHERE question_id = :questionId
            ORDER BY position, option_id
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();
        $options = $stmt->fetchAll();
    } elseif (isset($_POST['update_option']) && isset($_POST['option_id'])) {
        // Update an option
        $optionId = intval($_POST['option_id']);
        $optionText = trim($_POST['option_text']);
        $isCorrect = isset($_POST['is_correct']) ? true : false;
        
        if (empty($optionText)) {
            $error = "Option text cannot be empty.";
        } else {
            $stmt = $pdo->prepare("
                UPDATE quiz_options 
                SET option_text = :optionText, is_correct = :isCorrect
                WHERE option_id = :optionId
            ");
            $stmt->bindParam(':optionText', $optionText);
            $isCorrectInt = $isCorrect ? 1 : 0;
            $stmt->bindParam(':isCorrect', $isCorrectInt);
            $stmt->bindParam(':optionId', $optionId);
            $stmt->execute();
            
            $success = "Option updated successfully.";
            
            // Refresh options
            $stmt = $pdo->prepare("
                SELECT * FROM quiz_options
                WHERE question_id = :questionId
                ORDER BY position, option_id
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();
            $options = $stmt->fetchAll();
        }
    }
}

// Set page title
$page_title = "Edit Options - " . htmlspecialchars($question['question_text']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>">Course</a></li>
                    <li class="breadcrumb-item"><a href="activity_view.php?id=<?php echo $activityId; ?>"><?php echo htmlspecialchars($question['activity_title']); ?></a></li>
                    <li class="breadcrumb-item"><a href="quiz_edit.php?id=<?php echo $activityId; ?>">Edit Quiz</a></li>
                    <li class="breadcrumb-item active">Edit Options</li>
                </ol>
            </nav>
            
            <h1 class="mb-4">Edit Options</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h4 mb-0">Question</h2>
                </div>
                <div class="card-body">
                    <p class="lead"><?php echo htmlspecialchars($question['question_text']); ?></p>
                    <div class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></div>
                    <div class="badge badge-primary"><?php echo $question['points']; ?> points</div>
                </div>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h4 mb-0">Add New Option</h2>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?question_id=' . $questionId); ?>" method="post">
                        <div class="form-group">
                            <label for="option_text">Option Text <span class="text-danger">*</span></label>
                            <input type="text" name="option_text" id="option_text" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_correct" name="is_correct">
                                <label class="custom-control-label" for="is_correct">This is the correct answer</label>
                                <?php if ($question['question_type'] == 'checkbox'): ?>
                                <small class="form-text text-muted">For multiple choice (multiple answers), you can mark multiple options as correct.</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="form-group text-right">
                            <button type="submit" name="add_option" class="btn btn-primary">Add Option</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="h4 mb-0">Options</h2>
                </div>
                <div class="card-body">
                    <?php if (empty($options)): ?>
                    <div class="alert alert-info">
                        No options have been added to this question yet. Use the form above to add options.
                    </div>
                    <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($options as $index => $option): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="option-text <?php echo $option['is_correct'] ? 'text-success font-weight-bold' : ''; ?>">
                                        <?php if ($question['question_type'] == 'multiple_choice'): ?>
                                        <i class="far fa-circle mr-2"></i>
                                        <?php elseif ($question['question_type'] == 'checkbox'): ?>
                                        <i class="far fa-square mr-2"></i>
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                    </span>
                                    <?php if ($option['is_correct']): ?>
                                    <span class="badge badge-success ml-2">Correct</span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="collapse" data-target="#editOption<?php echo $option['option_id']; ?>">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    
                                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?question_id=' . $questionId); ?>" method="post" class="d-inline">
                                        <input type="hidden" name="option_id" value="<?php echo $option['option_id']; ?>">
                                        <button type="submit" name="delete_option" class="btn btn-sm btn-danger ml-2" onclick="return confirm('Are you sure you want to delete this option?');">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="collapse mt-3" id="editOption<?php echo $option['option_id']; ?>">
                                <div class="card card-body">
                                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?question_id=' . $questionId); ?>" method="post">
                                        <input type="hidden" name="option_id" value="<?php echo $option['option_id']; ?>">
                                        
                                        <div class="form-group">
                                            <label for="option_text_<?php echo $option['option_id']; ?>">Option Text <span class="text-danger">*</span></label>
                                            <input type="text" name="option_text" id="option_text_<?php echo $option['option_id']; ?>" class="form-control" value="<?php echo htmlspecialchars($option['option_text']); ?>" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="is_correct_<?php echo $option['option_id']; ?>" name="is_correct" <?php echo $option['is_correct'] ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="is_correct_<?php echo $option['option_id']; ?>">This is the correct answer</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group text-right">
                                            <button type="button" class="btn btn-secondary" data-toggle="collapse" data-target="#editOption<?php echo $option['option_id']; ?>">Cancel</button>
                                            <button type="submit" name="update_option" class="btn btn-primary">Update Option</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="quiz_edit.php?id=<?php echo $activityId; ?>" class="btn btn-secondary">Back to Quiz Editor</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
