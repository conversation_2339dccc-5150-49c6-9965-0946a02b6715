<?php
/**
 * Email Helper Functions
 *
 * This file contains functions related to sending emails.
 */

require_once 'phpmailer_wrapper.php';

/**
 * Send an email using PHPMailer
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $headers Email headers (not used with PHPMailer)
 * @return bool True if email was sent successfully, false otherwise
 */
function sendEmail($to, $subject, $message, $headers = '') {
    // Use our PHPMailer wrapper to send the email
    return PHPMailerWrapper::send($to, $subject, $message, false);
}

/**
 * Send a verification code email for account recovery
 *
 * @param string $to Recipient email address
 * @param string $username User's username
 * @param string $verificationCode The verification code
 * @return bool True if email was sent successfully, false otherwise
 */
function sendVerificationCodeEmail($to, $username, $verificationCode) {
    $subject = APP_NAME . " - Account Recovery";

    $message = "Hello,\n\n";
    $message .= "You have requested to recover your account at " . APP_NAME . ".\n\n";
    $message .= "Your username is: " . $username . "\n\n";
    $message .= "To reset your password, use the following verification code:\n";
    $message .= $verificationCode . "\n\n";
    $message .= "This code will expire in 1 hour.\n\n";
    $message .= "If you did not request this, please ignore this email.\n\n";
    $message .= "Regards,\n" . APP_NAME . " Team";

    $headers = "From: " . APP_EMAIL . "\r\n";

    return sendEmail($to, $subject, $message, $headers);
}

/**
 * Generate a random verification code
 *
 * @return string 6-digit verification code
 */
function generateVerificationCode() {
    return sprintf("%06d", mt_rand(0, 999999));
}

/**
 * Mask an email address for display
 *
 * @param string $email The email address to mask
 * @return string The masked email address (e.g., j***@example.com)
 */
function maskEmail($email) {
    if (empty($email)) {
        return '';
    }

    $parts = explode('@', $email);
    if (count($parts) != 2) {
        return $email; // Not a valid email format
    }

    $username = $parts[0];
    $domain = $parts[1];

    // Show only the first character of the username
    $maskedUsername = substr($username, 0, 1) . str_repeat('*', strlen($username) - 1);

    return $maskedUsername . '@' . $domain;
}
