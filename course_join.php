<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is a student
if (!isStudent()) {
    $_SESSION['error'] = "Only students can join courses.";
    header("location: index.php");
    exit;
}

// Initialize variables
$classCode = "";
$class_code_err = "";
$success = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate class code
    if (empty(trim($_POST["class_code"]))) {
        $class_code_err = "Please enter a class code.";
    } else {
        $classCode = trim($_POST["class_code"]);
    }

    // Check input errors before creating enrollment request
    if (empty($class_code_err)) {
        try {
            // Check if the enrollment_requests table exists
            $tableExists = false;
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE 'enrollment_requests'");
                $tableExists = $stmt->rowCount() > 0;
            } catch (PDOException $e) {
                // Error checking table
            }

            if (!$tableExists) {
                // Create the enrollment_requests table
                $sql = "CREATE TABLE enrollment_requests (
                    request_id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    course_id INT NOT NULL,
                    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_by INT NULL,
                    processed_date TIMESTAMP NULL,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
                    FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL
                )";

                $pdo->exec($sql);
            }

            // Create enrollment request using class code
            $result = enrollStudentByClassCode($_SESSION['user_id'], $classCode);

            if ($result === true) {
                // Request created successfully
                $success = "Your enrollment request has been submitted. You will be notified when it is approved by the instructor.";
                // Clear form fields
                $classCode = "";
            } else {
                // Error occurred
                $class_code_err = $result;
            }
        } catch (PDOException $e) {
            $class_code_err = "Database error: " . $e->getMessage();
        }
    }
}

// Set page title
$page_title = "Join Course";

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Join a Course</h1>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success">
    <?php echo $success; ?>
    <div class="mt-2">
        <p class="small text-muted">
            <i class="fas fa-info-circle"></i> Your request will be reviewed by the course instructor. You will be enrolled in the course once your request is approved.
        </p>
        <a href="index.php" class="btn-classroom-outline btn-sm">Go to Home</a>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Join with Class Code</h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">Enter the class code provided by your instructor to join a course.</p>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div class="form-group">
                        <label for="class_code">Class Code <span class="text-danger">*</span></label>
                        <input type="text" name="class_code" id="class_code" class="form-control <?php echo (!empty($class_code_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $classCode; ?>" placeholder="Enter 6-character code">
                        <span class="invalid-feedback"><?php echo $class_code_err; ?></span>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-classroom">Join Course</button>
                        <a href="index.php" class="btn-classroom-outline ml-2">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">How to Join a Course</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6><i class="fas fa-info-circle text-primary mr-2"></i> Using a Class Code</h6>
                    <p>Ask your instructor for the class code, then enter it here to join the course.</p>
                </div>

                <div class="mb-4">
                    <h6><i class="fas fa-envelope text-primary mr-2"></i> Invitation Email</h6>
                    <p>If you received an invitation email, click the link in the email to join the course automatically.</p>
                </div>

                <div>
                    <h6><i class="fas fa-question-circle text-primary mr-2"></i> Need Help?</h6>
                    <p>If you're having trouble joining a course, contact your instructor or the system administrator for assistance.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
