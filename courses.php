<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is admin or teacher
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("location: index.php");
    exit;
}

// Get courses based on user role
if (isAdmin()) {
    $courses = getAllCourses(true); // Get all courses including inactive ones
} else {
    $courses = getCoursesByTeacher($_SESSION['user_id']);
}

// Check if courses is an error message
if (is_string($courses)) {
    $error = $courses;
    $courses = [];
}

// Set page title
$page_title = "Manage Courses";

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Manage Courses</h1>
    <a href="course_add.php" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create Course
    </a>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (count($courses) > 0): ?>
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Created By</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($courses as $course): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($course['title']); ?></td>
                        <td><?php echo htmlspecialchars(substr($course['description'], 0, 50) . (strlen($course['description']) > 50 ? '...' : '')); ?></td>
                        <td><?php echo isset($course['creator_name']) ? htmlspecialchars($course['creator_name']) : 'N/A'; ?></td>
                        <td>
                            <?php if ($course['is_active']): ?>
                            <span class="badge badge-success">Active</span>
                            <?php else: ?>
                            <span class="badge badge-danger">Inactive</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($course['created_at'])); ?></td>
                        <td>
                            <a href="course_view.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-info" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if (isAdmin() || (isTeacher() && $course['created_by'] == $_SESSION['user_id'])): ?>
                            <a href="course_edit.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="course_delete.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this course?');">
                                <i class="fas fa-trash"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php else: ?>
<div class="empty-state">
    <div class="empty-state-icon">
        <i class="fas fa-book"></i>
    </div>
    <h3>No courses yet</h3>
    <p class="empty-state-text">Create your first course to get started</p>
    <a href="course_add.php" class="btn btn-primary">Create Course</a>
</div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
