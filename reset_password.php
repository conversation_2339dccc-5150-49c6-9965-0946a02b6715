<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is in the reset password process
if (!isset($_SESSION['reset_user_id'])) {
    header("location: forgot_account.php");
    exit;
}

$userId = $_SESSION['reset_user_id'];

// Initialize variables
$new_password = $confirm_password = "";
$new_password_err = $confirm_password_err = "";
$error_msg = "";
$success = false;

// Get user information
global $pdo;
try {
    $stmt = $pdo->prepare("SELECT username, email FROM users WHERE user_id = :userId");
    $stmt->bindParam(':userId', $userId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        $username = $user['username'];
        $email = $user['email'];
    } else {
        // User not found
        header("location: forgot_account.php");
        exit;
    }
} catch (PDOException $e) {
    $error_msg = "An error occurred. Please try again later.";
    error_log("Reset password error: " . $e->getMessage());
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate new password
    if (empty(trim($_POST["new_password"]))) {
        $new_password_err = "Please enter the new password.";
    } elseif (strlen(trim($_POST["new_password"])) < 6) {
        $new_password_err = "Password must have at least 6 characters.";
    } else {
        $new_password = trim($_POST["new_password"]);
    }
    
    // Validate confirm password
    if (empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "Please confirm the password.";
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if (empty($new_password_err) && ($new_password != $confirm_password)) {
            $confirm_password_err = "Passwords did not match.";
        }
    }
    
    // Check input errors before updating the password
    if (empty($new_password_err) && empty($confirm_password_err)) {
        try {
            // Hash the new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // Update the password
            $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :userId");
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':userId', $userId);
            $stmt->execute();
            
            // Delete the reset code
            $stmt = $pdo->prepare("DELETE FROM password_reset WHERE user_id = :userId");
            $stmt->bindParam(':userId', $userId);
            $stmt->execute();
            
            // Set success flag
            $success = true;
            
            // Clear session variables
            unset($_SESSION['recovery_email']);
            unset($_SESSION['reset_user_id']);
            
            // Set success message for login page
            $_SESSION['success_message'] = "Your password has been reset successfully. You can now log in with your new password.";
            
            // Redirect to login page after 3 seconds
            header("refresh:3;url=login.php");
        } catch (PDOException $e) {
            $error_msg = "An error occurred. Please try again later.";
            error_log("Reset password error: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/AdobeStock_271791778.jpeg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .reset-container {
            width: 448px;
            padding: 48px 40px 36px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .reset-logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .reset-logo h1 {
            color: var(--primary-color);
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            font-size: 28px;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .reset-logo h1 i {
            margin-right: 12px;
            font-size: 32px;
        }

        .reset-logo p {
            color: var(--text-secondary);
            margin-top: 12px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-control {
            height: 56px;
            padding: 16px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.25);
            transform: translateY(-2px);
        }

        .btn-submit {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:active {
            transform: translateY(1px);
        }

        .alert {
            margin-bottom: 24px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        /* Password toggle styles */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .user-info {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .user-info p {
            margin: 0;
            padding: 5px 0;
        }

        .user-info strong {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-logo">
            <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
            <p>Reset Your Password</p>
        </div>

        <?php
        if (!empty($error_msg)) {
            echo '<div class="alert alert-danger">' . $error_msg . '</div>';
        }

        if ($success) {
            echo '<div class="alert alert-success">Your password has been reset successfully! Redirecting to login page...</div>';
        } else {
        ?>

        <div class="user-info">
            <p><strong>Username:</strong> <?php echo htmlspecialchars($username); ?></p>
            <p><strong>Email:</strong> <?php echo htmlspecialchars($email); ?></p>
        </div>

        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <div class="password-container">
                    <input type="password" name="new_password" id="new_password" placeholder="New Password" class="form-control <?php echo (!empty($new_password_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $new_password; ?>">
                    <span class="password-toggle" onclick="togglePassword('new_password')">
                        <i class="fas fa-eye" id="new_password-eye"></i>
                    </span>
                </div>
                <span class="invalid-feedback"><?php echo $new_password_err; ?></span>
            </div>
            <div class="form-group">
                <div class="password-container">
                    <input type="password" name="confirm_password" id="confirm_password" placeholder="Confirm Password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>">
                    <span class="password-toggle" onclick="togglePassword('confirm_password')">
                        <i class="fas fa-eye" id="confirm_password-eye"></i>
                    </span>
                </div>
                <span class="invalid-feedback"><?php echo $confirm_password_err; ?></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-submit">Reset Password</button>
            </div>
        </form>
        <?php } ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const eyeIcon = document.getElementById(inputId + '-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
