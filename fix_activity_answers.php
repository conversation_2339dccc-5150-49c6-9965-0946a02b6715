<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Set the activity ID
$activityId = 6;

echo "<h1>Fixing Activity Answers for Activity ID: $activityId</h1>";

try {
    // Check if activity exists
    $stmt = $pdo->prepare("SELECT * FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $activity = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h2>Activity Details</h2>";
        echo "<p>Title: " . htmlspecialchars($activity['title']) . "</p>";
        echo "<p>Type: " . htmlspecialchars($activity['activity_type']) . "</p>";
        
        // Check if activity_answers table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
        $activityAnswersExists = $stmt->rowCount() > 0;
        
        if (!$activityAnswersExists) {
            echo "<h2>Creating activity_answers table</h2>";
            
            // Create activity_answers table
            $sql = "CREATE TABLE IF NOT EXISTS `activity_answers` (
                `answer_id` int(11) NOT NULL AUTO_INCREMENT,
                `submission_id` int(11) NOT NULL,
                `question_id` int(11) NOT NULL,
                `answer_text` text DEFAULT NULL,
                `selected_option_id` int(11) DEFAULT NULL,
                `is_correct` tinyint(1) DEFAULT NULL,
                `points_earned` decimal(10,2) DEFAULT NULL,
                PRIMARY KEY (`answer_id`),
                KEY `submission_id` (`submission_id`),
                KEY `question_id` (`question_id`),
                KEY `selected_option_id` (`selected_option_id`),
                CONSTRAINT `activity_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            
            $pdo->exec($sql);
            echo "<p>activity_answers table created successfully.</p>";
        } else {
            echo "<p>activity_answers table already exists.</p>";
        }
        
        // Check if we need to modify the submitActivity function
        echo "<h2>Checking submitActivity function</h2>";
        
        // Get the file content
        $filePath = 'includes/activity_functions.php';
        $fileContent = file_get_contents($filePath);
        
        // Check if the function already handles activity_answers
        if (strpos($fileContent, 'INSERT INTO activity_answers') === false) {
            echo "<p>Need to update submitActivity function to handle activity_answers.</p>";
            
            // Create a backup of the file
            $backupPath = 'includes/activity_functions.php.bak';
            file_put_contents($backupPath, $fileContent);
            echo "<p>Backup created at $backupPath</p>";
            
            // Find the position where we need to make changes
            $searchString = "// Save the answer
                \$stmt = \$pdo->prepare(\"
                    INSERT INTO quiz_answers (submission_id, question_id, answer_text, is_correct, points_earned)
                    VALUES (:submissionId, :questionId, :answerText, :isCorrect, :pointsEarned)
                \");";
            
            $replaceString = "// Save the answer
                if (\$activity['activity_type'] == 'activity') {
                    // For activities, use activity_answers table
                    \$stmt = \$pdo->prepare(\"
                        INSERT INTO activity_answers (submission_id, question_id, answer_text, selected_option_id, is_correct, points_earned)
                        VALUES (:submissionId, :questionId, :answerText, :selectedOptionId, :isCorrect, :pointsEarned)
                    \");
                    \$stmt->bindParam(':submissionId', \$submissionId);
                    \$stmt->bindParam(':questionId', \$questionId);
                    \$stmt->bindParam(':answerText', \$answerText);
                    \$selectedOptionId = isset(\$selectedOptions[0]) ? \$selectedOptions[0] : null;
                    \$stmt->bindParam(':selectedOptionId', \$selectedOptionId);
                    \$stmt->bindParam(':isCorrect', \$isCorrect);
                    \$stmt->bindParam(':pointsEarned', \$pointsEarned);
                } else {
                    // For quizzes, use quiz_answers table
                    \$stmt = \$pdo->prepare(\"
                        INSERT INTO quiz_answers (submission_id, question_id, answer_text, is_correct, points_earned)
                        VALUES (:submissionId, :questionId, :answerText, :isCorrect, :pointsEarned)
                    \");
                    \$stmt->bindParam(':submissionId', \$submissionId);
                    \$stmt->bindParam(':questionId', \$questionId);
                    \$stmt->bindParam(':answerText', \$answerText);
                    \$stmt->bindParam(':isCorrect', \$isCorrect);
                    \$stmt->bindParam(':pointsEarned', \$pointsEarned);
                }";
            
            // Replace the string in the file content
            $newContent = str_replace($searchString, $replaceString, $fileContent);
            
            // Save the modified file
            file_put_contents($filePath, $newContent);
            echo "<p>submitActivity function updated successfully.</p>";
            
            echo "<p>Please try submitting the activity again.</p>";
        } else {
            echo "<p>submitActivity function already handles activity_answers.</p>";
        }
        
    } else {
        echo "<p>Activity not found.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
