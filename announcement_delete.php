<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/announcement_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to delete announcements.";
    header("location: index.php");
    exit;
}

// Check if announcement ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Announcement ID is required.";
    header("location: index.php");
    exit;
}

$announcementId = intval($_GET['id']);

// Get announcement details
$announcement = getAnnouncementById($announcementId);

// Check if announcement exists
if (is_string($announcement)) {
    $_SESSION['error'] = $announcement;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $announcement['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to delete this announcement
if (!isAdmin() && $announcement['created_by'] != $_SESSION['user_id'] && $course['created_by'] != $_SESSION['user_id']) {
    $_SESSION['error'] = "You are not authorized to delete this announcement.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Delete the announcement
$result = deleteAnnouncement($announcementId);

if ($result === true) {
    // Announcement deleted successfully
    $_SESSION['success'] = "Announcement deleted successfully.";
    header("location: course_view_full.php?id=$courseId&tab=stream");
    exit;
} else {
    // Error deleting announcement
    $_SESSION['error'] = $result;
    header("location: announcement_view.php?id=$announcementId");
    exit;
}
?>
