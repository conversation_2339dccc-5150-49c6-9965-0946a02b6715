<?php
/**
 * Database Configuration File
 *
 * This file contains the database connection settings for the e-learning system.
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Attempt to connect to MySQL database
try {
    $pdo = new PDO("mysql:host=" . DB_SERVER . ";dbname=" . DB_NAME, DB_USERNAME, DB_PASSWORD);

    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Set default fetch mode to associative array
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Set character set to utf8mb4
    $pdo->exec("SET NAMES utf8mb4");
} catch(PDOException $e) {
    die("ERROR: Could not connect to the database. " . $e->getMessage());
}

// Application settings
define('APP_NAME', 'Classroom');
define('BASE_URL', 'http://localhost/final/');
define('ADMIN_EMAIL', '<EMAIL>');
define('APP_EMAIL', '<EMAIL>');

// Development settings
define('DEVELOPMENT_MODE', true); // Set to false in production

// Session settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Function to check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Function to check if user is an admin
 *
 * @return bool True if user is an admin, false otherwise
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * Function to check if user is a teacher
 *
 * @return bool True if user is a teacher, false otherwise
 */
function isTeacher() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'teacher';
}

/**
 * Function to check if user is a student
 *
 * @return bool True if user is a student, false otherwise
 */
function isStudent() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'student';
}

/**
 * Function to redirect to a specific page
 *
 * @param string $page The page to redirect to
 * @return void
 */
function redirect($page) {
    header("Location: " . BASE_URL . $page);
    exit;
}

/**
 * Function to sanitize user input
 *
 * @param string $data The data to sanitize
 * @return string The sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Function to format comment text
 *
 * @param string $text The text to format
 * @return string The formatted text
 */
function formatCommentText($text) {
    if (empty($text)) {
        return '';
    }

    // Convert newlines to <br>
    $text = nl2br($text);

    return $text;
}
