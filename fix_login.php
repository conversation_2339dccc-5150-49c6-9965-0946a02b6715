<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Login Fix Script</h1>";

try {
    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    $rolesTableExists = $stmt->rowCount() > 0;

    if (!$rolesTableExists) {
        echo "<p>Creating roles table...</p>";
        
        // Create roles table
        $pdo->exec("
            CREATE TABLE roles (
                role_id INT PRIMARY KEY AUTO_INCREMENT,
                role_name VARCHAR(50) NOT NULL UNIQUE
            )
        ");
        
        // Insert default roles
        $pdo->exec("INSERT INTO roles (role_id, role_name) VALUES (1, 'admin'), (2, 'teacher'), (3, 'student')");
        
        echo "<p>Roles table created and populated successfully.</p>";
    } else {
        echo "<p>Roles table already exists.</p>";
        
        // Check if roles table has data
        $stmt = $pdo->query("SELECT COUNT(*) FROM roles");
        $roleCount = $stmt->fetchColumn();
        
        if ($roleCount == 0) {
            echo "<p>Populating empty roles table...</p>";
            $pdo->exec("INSERT INTO roles (role_id, role_name) VALUES (1, 'admin'), (2, 'teacher'), (3, 'student')");
            echo "<p>Roles table populated successfully.</p>";
        } else {
            echo "<p>Roles table already has data.</p>";
        }
    }
    
    // Check users table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Check if role_id column exists
    $roleIdExists = in_array('role_id', $columns);
    $roleExists = in_array('role', $columns);
    
    if (!$roleIdExists) {
        echo "<p>Adding role_id column to users table...</p>";
        
        // Add role_id column
        if ($roleExists) {
            // If role column exists, add role_id and migrate data
            $pdo->exec("ALTER TABLE users ADD COLUMN role_id INT NOT NULL DEFAULT 3 AFTER password");
            
            // Update role_id based on role column
            $pdo->exec("UPDATE users SET role_id = 1 WHERE role = 'admin'");
            $pdo->exec("UPDATE users SET role_id = 2 WHERE role = 'teacher'");
            $pdo->exec("UPDATE users SET role_id = 3 WHERE role = 'student'");
            
            echo "<p>Migrated role data to role_id column.</p>";
        } else {
            // If role column doesn't exist, just add role_id
            $pdo->exec("ALTER TABLE users ADD COLUMN role_id INT NOT NULL DEFAULT 3 AFTER password");
        }
        
        // Add foreign key constraint
        try {
            $pdo->exec("ALTER TABLE users ADD CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES roles(role_id)");
            echo "<p>Foreign key constraint added successfully.</p>";
        } catch (PDOException $e) {
            echo "<p>Warning: Could not add foreign key constraint: " . $e->getMessage() . "</p>";
        }
        
        echo "<p>role_id column added successfully.</p>";
    } else {
        echo "<p>role_id column already exists in users table.</p>";
    }
    
    // Check if user_id column exists (instead of id)
    $userIdExists = in_array('user_id', $columns);
    $idExists = in_array('id', $columns);
    
    if (!$userIdExists && $idExists) {
        echo "<p>Renaming id column to user_id...</p>";
        
        // Rename id column to user_id
        $pdo->exec("ALTER TABLE users CHANGE COLUMN id user_id INT AUTO_INCREMENT");
        
        echo "<p>id column renamed to user_id successfully.</p>";
    } elseif (!$userIdExists && !$idExists) {
        echo "<p>Error: Neither user_id nor id column exists in users table!</p>";
    } else {
        echo "<p>user_id column already exists in users table.</p>";
    }
    
    // Create a test user if none exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        echo "<p>Creating test admin user...</p>";
        
        // Hash password
        $hashedPassword = password_hash("admin123", PASSWORD_DEFAULT);
        
        // Insert admin user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, first_name, last_name, role_id, is_active) 
            VALUES ('admin', :password, '<EMAIL>', 'Admin', 'User', 1, 1)
        ");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->execute();
        
        echo "<p>Test admin user created successfully. Username: admin, Password: admin123</p>";
    }
    
    echo "<h2>Fix Complete</h2>";
    echo "<p>The login system should now be working correctly.</p>";
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
