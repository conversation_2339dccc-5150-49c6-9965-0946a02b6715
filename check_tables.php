<?php
// Include configuration file
require_once 'includes/config.php';

// Check tables structure
try {
    // Check if activity_questions table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'activity_questions'");
    $stmt->execute();
    $activityQuestionsExists = $stmt->rowCount() > 0;

    // Check if quiz_questions table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'quiz_questions'");
    $stmt->execute();
    $quizQuestionsExists = $stmt->rowCount() > 0;

    echo "<h2>Question Tables Check</h2>";

    if ($activityQuestionsExists) {
        // Get activity_questions table structure
        $stmt = $pdo->prepare("DESCRIBE activity_questions");
        $stmt->execute();
        echo "<h3>activity_questions Table Structure</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";

        // Get sample data
        $stmt = $pdo->prepare("SELECT * FROM activity_questions LIMIT 5");
        $stmt->execute();
        echo "<h3>activity_questions Sample Data</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";
    } else {
        echo "<p>activity_questions table does not exist</p>";
    }

    if ($quizQuestionsExists) {
        // Get quiz_questions table structure
        $stmt = $pdo->prepare("DESCRIBE quiz_questions");
        $stmt->execute();
        echo "<h3>quiz_questions Table Structure</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";

        // Get sample data
        $stmt = $pdo->prepare("SELECT * FROM quiz_questions LIMIT 5");
        $stmt->execute();
        echo "<h3>quiz_questions Sample Data</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";
    } else {
        echo "<p>quiz_questions table does not exist</p>";
    }

    // Check if activity_question_options table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'activity_question_options'");
    $stmt->execute();
    $activityOptionsExists = $stmt->rowCount() > 0;

    if ($activityOptionsExists) {
        // Get activity_question_options table structure
        $stmt = $pdo->prepare("DESCRIBE activity_question_options");
        $stmt->execute();
        echo "<h3>activity_question_options Table Structure</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";
    } else {
        echo "<p>activity_question_options table does not exist</p>";
    }

    // Check if quiz_question_options table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'quiz_question_options'");
    $stmt->execute();
    $quizOptionsExists = $stmt->rowCount() > 0;

    if ($quizOptionsExists) {
        // Get quiz_question_options table structure
        $stmt = $pdo->prepare("DESCRIBE quiz_question_options");
        $stmt->execute();
        echo "<h3>quiz_question_options Table Structure</h3>";
        echo "<pre>";
        print_r($stmt->fetchAll(PDO::FETCH_ASSOC));
        echo "</pre>";
    } else {
        echo "<p>quiz_question_options table does not exist</p>";
    }

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
