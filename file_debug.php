<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}

// Set page title
$page_title = "File System Debug";

// Include header
require_once 'includes/header.php';

// Function to check if a directory is writable
function checkDirectoryPermissions($dir) {
    if (!file_exists($dir)) {
        return [
            'exists' => false,
            'writable' => false,
            'message' => 'Directory does not exist'
        ];
    }
    
    return [
        'exists' => true,
        'writable' => is_writable($dir),
        'message' => is_writable($dir) ? 'Directory is writable' : 'Directory is not writable'
    ];
}

// Get file information from database
$fileInfo = [];
if (isset($_GET['file_id']) && !empty($_GET['file_id'])) {
    $fileId = intval($_GET['file_id']);
    
    try {
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM activity_files WHERE file_id = :fileId");
        $stmt->bindParam(':fileId', $fileId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $fileInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Check uploads directory
$uploadsDir = 'uploads/';
$uploadsDirCheck = checkDirectoryPermissions($uploadsDir);

// Check materials directory
$materialsDir = 'uploads/materials/';
$materialsDirCheck = checkDirectoryPermissions($materialsDir);

// Check assignments directory
$assignmentsDir = 'uploads/assignments/';
$assignmentsDirCheck = checkDirectoryPermissions($assignmentsDir);

// Check quizzes directory
$quizzesDir = 'uploads/quizzes/';
$quizzesDirCheck = checkDirectoryPermissions($quizzesDir);

// Get recent files from database
$recentFiles = [];
try {
    global $pdo;
    $stmt = $pdo->prepare("
        SELECT af.*, a.title as activity_title, a.activity_type
        FROM activity_files af
        JOIN activities a ON af.activity_id = a.activity_id
        ORDER BY af.uploaded_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $recentFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}
?>

<div class="container mt-4">
    <h1>File System Debug</h1>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Directory Permissions</h5>
        </div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>Directory</th>
                        <th>Exists</th>
                        <th>Writable</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><?php echo $uploadsDir; ?></td>
                        <td><?php echo $uploadsDirCheck['exists'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $uploadsDirCheck['writable'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $uploadsDirCheck['message']; ?></td>
                    </tr>
                    <tr>
                        <td><?php echo $materialsDir; ?></td>
                        <td><?php echo $materialsDirCheck['exists'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $materialsDirCheck['writable'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $materialsDirCheck['message']; ?></td>
                    </tr>
                    <tr>
                        <td><?php echo $assignmentsDir; ?></td>
                        <td><?php echo $assignmentsDirCheck['exists'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $assignmentsDirCheck['writable'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $assignmentsDirCheck['message']; ?></td>
                    </tr>
                    <tr>
                        <td><?php echo $quizzesDir; ?></td>
                        <td><?php echo $quizzesDirCheck['exists'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $quizzesDirCheck['writable'] ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'; ?></td>
                        <td><?php echo $quizzesDirCheck['message']; ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if (!empty($fileInfo)): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">File Details (ID: <?php echo $fileInfo['file_id']; ?>)</h5>
        </div>
        <div class="card-body">
            <table class="table">
                <tr>
                    <th>File ID</th>
                    <td><?php echo $fileInfo['file_id']; ?></td>
                </tr>
                <tr>
                    <th>Activity ID</th>
                    <td><?php echo $fileInfo['activity_id']; ?></td>
                </tr>
                <tr>
                    <th>File Name</th>
                    <td><?php echo htmlspecialchars($fileInfo['file_name']); ?></td>
                </tr>
                <tr>
                    <th>File Path</th>
                    <td><?php echo htmlspecialchars($fileInfo['file_path']); ?></td>
                </tr>
                <tr>
                    <th>File Type</th>
                    <td><?php echo htmlspecialchars($fileInfo['file_type']); ?></td>
                </tr>
                <tr>
                    <th>File Size</th>
                    <td><?php echo number_format($fileInfo['file_size']); ?> bytes</td>
                </tr>
                <tr>
                    <th>File Exists</th>
                    <td>
                        <?php 
                        $paths = [
                            $fileInfo['file_path'],
                            './' . $fileInfo['file_path'],
                            '../' . $fileInfo['file_path'],
                            $_SERVER['DOCUMENT_ROOT'] . '/' . $fileInfo['file_path'],
                            dirname(__FILE__) . '/' . $fileInfo['file_path']
                        ];
                        
                        $existsInAnyPath = false;
                        foreach ($paths as $path) {
                            if (file_exists($path)) {
                                echo '<div class="text-success">Yes, at path: ' . htmlspecialchars($path) . '</div>';
                                $existsInAnyPath = true;
                            }
                        }
                        
                        if (!$existsInAnyPath) {
                            echo '<div class="text-danger">File not found in any checked path</div>';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>Actions</th>
                    <td>
                        <a href="file_viewer.php?file=<?php echo urlencode($fileInfo['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">View File</a>
                        <a href="file_viewer.php?file=<?php echo urlencode($fileInfo['file_path']); ?>&debug=1" class="btn btn-info btn-sm" target="_blank">Debug File</a>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Recent Files</h5>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Activity</th>
                        <th>Type</th>
                        <th>File Name</th>
                        <th>File Path</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentFiles as $file): ?>
                    <tr>
                        <td><?php echo $file['file_id']; ?></td>
                        <td><?php echo htmlspecialchars($file['activity_title']); ?></td>
                        <td><?php echo htmlspecialchars($file['activity_type']); ?></td>
                        <td><?php echo htmlspecialchars($file['file_name']); ?></td>
                        <td><?php echo htmlspecialchars($file['file_path']); ?></td>
                        <td>
                            <a href="file_debug.php?file_id=<?php echo $file['file_id']; ?>" class="btn btn-info btn-sm">Details</a>
                            <a href="file_viewer.php?file=<?php echo urlencode($file['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">View</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
