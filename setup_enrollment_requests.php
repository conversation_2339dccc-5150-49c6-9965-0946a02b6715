<?php
// Include configuration file
require_once 'includes/config.php';

// For setup purposes, we're allowing this script to run without admin login
// Comment this back in after setup is complete
/*
if (!isLoggedIn() || !isAdmin()) {
    header("location: login.php");
    exit;
}
*/

// Create the enrollment_requests table
try {
    global $pdo;

    // Check if the table already exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'enrollment_requests'");
    if ($stmt->rowCount() > 0) {
        // Drop the existing table to recreate it
        $pdo->exec("DROP TABLE enrollment_requests");
        $message = "The enrollment_requests table has been dropped and will be recreated.";
        $messageType = "warning";
    }

    // Create the table
    $sql = "CREATE TABLE enrollment_requests (
        request_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        course_id INT NOT NULL,
        request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        processed_by INT,
        processed_date TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
        FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL,
        UNIQUE KEY unique_request (user_id, course_id)
    )";

    $pdo->exec($sql);
    $message = "The enrollment_requests table has been created successfully.";
    $messageType = "success";
} catch (PDOException $e) {
    $message = "Error creating enrollment_requests table: " . $e->getMessage();
    $messageType = "danger";
}

// Set page title
$page_title = "Setup Enrollment Requests";

// Include header
require_once 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h1 class="h3 mb-0">Setup Enrollment Requests</h1>
                </div>
                <div class="card-body">
                    <?php if (isset($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo $message; ?>
                    </div>
                    <?php endif; ?>

                    <p>This script creates the enrollment_requests table in the database.</p>

                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
