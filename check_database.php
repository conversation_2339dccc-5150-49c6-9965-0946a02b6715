<?php
// Include configuration file
require_once 'includes/config.php';

// Set page title
$page_title = "Database Check";

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Database Connection Check</h4>
                </div>
                <div class="card-body">
                    <h5>Database Connection</h5>
                    <?php
                    try {
                        // Check if we can execute a simple query
                        $stmt = $pdo->query("SELECT 1");
                        echo '<div class="alert alert-success">Database connection successful to database: ' . DB_NAME . '</div>';
                    } catch (PDOException $e) {
                        echo '<div class="alert alert-danger">Database connection failed: ' . $e->getMessage() . '</div>';
                    }
                    ?>

                    <h5 class="mt-4">Tables Check</h5>
                    <?php
                    try {
                        // Get all tables
                        $stmt = $pdo->query("SHOW TABLES");
                        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        echo '<div class="alert alert-info">Found ' . count($tables) . ' tables in the database.</div>';
                        
                        echo '<ul class="list-group">';
                        foreach ($tables as $table) {
                            echo '<li class="list-group-item">' . $table . '</li>';
                        }
                        echo '</ul>';
                        
                        // Check specifically for enrollment_requests table
                        if (in_array('enrollment_requests', $tables)) {
                            echo '<div class="alert alert-success mt-3">The enrollment_requests table exists.</div>';
                            
                            // Check the structure of the enrollment_requests table
                            $stmt = $pdo->query("DESCRIBE enrollment_requests");
                            $columns = $stmt->fetchAll();
                            
                            echo '<h6 class="mt-3">Enrollment Requests Table Structure:</h6>';
                            echo '<table class="table table-bordered table-striped">';
                            echo '<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>';
                            echo '<tbody>';
                            foreach ($columns as $column) {
                                echo '<tr>';
                                echo '<td>' . $column['Field'] . '</td>';
                                echo '<td>' . $column['Type'] . '</td>';
                                echo '<td>' . $column['Null'] . '</td>';
                                echo '<td>' . $column['Key'] . '</td>';
                                echo '<td>' . $column['Default'] . '</td>';
                                echo '<td>' . $column['Extra'] . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                        } else {
                            echo '<div class="alert alert-warning mt-3">The enrollment_requests table does not exist.</div>';
                            
                            // Create the enrollment_requests table
                            echo '<div class="mt-3">';
                            echo '<h6>Create Enrollment Requests Table</h6>';
                            echo '<form action="setup_enrollment_requests.php" method="post">';
                            echo '<button type="submit" class="btn btn-primary">Create Table</button>';
                            echo '</form>';
                            echo '</div>';
                        }
                    } catch (PDOException $e) {
                        echo '<div class="alert alert-danger">Error checking tables: ' . $e->getMessage() . '</div>';
                    }
                    ?>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
