<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "Activity ID is required.";
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    echo "<h1>Error: Activity not found</h1>";
    echo "<p>" . $activity . "</p>";
    echo "<p>Available activities:</p>";

    try {
        // List available activities
        $stmt = $pdo->query("SELECT activity_id, title, activity_type FROM activities ORDER BY activity_id");
        $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($activities) > 0) {
            echo "<ul>";
            foreach ($activities as $act) {
                echo "<li><a href='debug_questions.php?id=" . $act['activity_id'] . "'>" .
                     "ID: " . $act['activity_id'] . " - " .
                     htmlspecialchars($act['title']) . " (" . $act['activity_type'] . ")</a></li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No activities found in the database.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Database error: " . $e->getMessage() . "</p>";
    }

    exit;
}

// Get activity questions
$questions = getActivityQuestions($activityId);
if (is_string($questions)) {
    echo "Error retrieving questions: " . $questions;
    exit;
}

echo "<h1>Debug Questions for Activity ID: $activityId</h1>";
echo "<h2>Activity Type: " . $activity['activity_type'] . "</h2>";
echo "<h2>Questions Count: " . count($questions) . "</h2>";

echo "<pre>";
print_r($questions);
echo "</pre>";

// Check the database tables
echo "<h2>Database Tables</h2>";

try {
    // Check activity_questions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
    $activityQuestionsExists = $stmt->rowCount() > 0;

    echo "activity_questions table exists: " . ($activityQuestionsExists ? "Yes" : "No") . "<br>";

    if ($activityQuestionsExists) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $count = $stmt->fetchColumn();

        echo "Number of questions in activity_questions table for activity $activityId: $count<br>";

        if ($count > 0) {
            $stmt = $pdo->prepare("SELECT * FROM activity_questions WHERE activity_id = :activityId");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->execute();
            $rawQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h3>Raw Questions Data:</h3>";
            echo "<pre>";
            print_r($rawQuestions);
            echo "</pre>";

            // Check options for each question
            foreach ($rawQuestions as $question) {
                $questionId = $question['question_id'];
                $stmt = $pdo->prepare("SELECT * FROM activity_question_options WHERE question_id = :questionId");
                $stmt->bindParam(':questionId', $questionId);
                $stmt->execute();
                $options = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<h4>Options for Question ID: $questionId</h4>";
                echo "<pre>";
                print_r($options);
                echo "</pre>";
            }
        }
    }

    // Check quiz_questions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'quiz_questions'");
    $quizQuestionsExists = $stmt->rowCount() > 0;

    echo "quiz_questions table exists: " . ($quizQuestionsExists ? "Yes" : "No") . "<br>";

    if ($quizQuestionsExists) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM quiz_questions WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $count = $stmt->fetchColumn();

        echo "Number of questions in quiz_questions table for activity $activityId: $count<br>";

        if ($count > 0) {
            $stmt = $pdo->prepare("SELECT * FROM quiz_questions WHERE activity_id = :activityId");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->execute();
            $rawQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h3>Raw Quiz Questions Data:</h3>";
            echo "<pre>";
            print_r($rawQuestions);
            echo "</pre>";

            // Check options for each question
            foreach ($rawQuestions as $question) {
                $questionId = $question['question_id'];
                $stmt = $pdo->prepare("SELECT * FROM quiz_options WHERE question_id = :questionId");
                $stmt->bindParam(':questionId', $questionId);
                $stmt->execute();
                $options = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<h4>Options for Quiz Question ID: $questionId</h4>";
                echo "<pre>";
                print_r($options);
                echo "</pre>";
            }
        }
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
