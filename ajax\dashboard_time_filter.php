<?php
/**
 * AJAX endpoint for dashboard time range filtering
 */

require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/dashboard_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

// Get time range from request
$timeRange = isset($_GET['timeRange']) ? $_GET['timeRange'] : 'month';

// Validate time range
$validTimeRanges = ['week', 'month', 'quarter', 'year', 'custom'];
if (!in_array($timeRange, $validTimeRanges)) {
    $timeRange = 'month';
}

try {
    // Get dashboard counts with time range filtering
    $dashboardCounts = getDashboardCountsWithTimeRange(null, $timeRange);
    
    // Calculate growth percentages based on time range
    $growthData = calculateGrowthData($timeRange);
    
    // Get enrollment trends data for the time range
    $trendsData = getEnrollmentTrendsForTimeRange($timeRange);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'timeRange' => $timeRange,
        'counts' => $dashboardCounts,
        'growth' => $growthData,
        'trends' => $trendsData
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch dashboard data: ' . $e->getMessage()
    ]);
}

/**
 * Calculate growth data based on time range
 */
function calculateGrowthData($timeRange) {
    // Simulate growth data based on time range
    // In a real application, this would compare current period with previous period
    
    switch ($timeRange) {
        case 'week':
            return [
                'active_users' => '5.2%',
                'students' => '6.8%',
                'teachers' => '3.0%',
                'courses' => '4.5%'
            ];
        case 'month':
            return [
                'active_users' => '10.5%',
                'students' => '12.5%',
                'teachers' => '5.0%',
                'courses' => '8.3%'
            ];
        case 'quarter':
            return [
                'active_users' => '15.8%',
                'students' => '18.2%',
                'teachers' => '12.0%',
                'courses' => '14.7%'
            ];
        case 'year':
            return [
                'active_users' => '25.3%',
                'students' => '28.7%',
                'teachers' => '20.5%',
                'courses' => '22.1%'
            ];
        default:
            return [
                'active_users' => '10.5%',
                'students' => '12.5%',
                'teachers' => '5.0%',
                'courses' => '8.3%'
            ];
    }
}

/**
 * Get enrollment trends for specific time range
 */
function getEnrollmentTrendsForTimeRange($timeRange) {
    // Generate sample data based on time range
    // In a real application, this would query the database for actual enrollment data
    
    switch ($timeRange) {
        case 'week':
            return [
                'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                'enrollments' => [2, 3, 1, 4, 5, 2, 1],
                'completions' => [1, 2, 1, 2, 3, 1, 0]
            ];
        case 'month':
            $days = [];
            $enrollments = [];
            $completions = [];
            for ($i = 1; $i <= 30; $i++) {
                $days[] = $i;
                $enrollments[] = rand(0, 5);
                $completions[] = rand(0, 3);
            }
            return [
                'labels' => $days,
                'enrollments' => $enrollments,
                'completions' => $completions
            ];
        case 'quarter':
            return [
                'labels' => ['Month 1', 'Month 2', 'Month 3'],
                'enrollments' => [45, 52, 38],
                'completions' => [25, 30, 22]
            ];
        case 'year':
            return [
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                'enrollments' => [30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140],
                'completions' => [15, 25, 35, 45, 50, 60, 65, 70, 75, 80, 85, 90]
            ];
        default:
            return getEnrollmentTrends();
    }
}
?>
