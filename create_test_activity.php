<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/utility_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin or teacher
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You are not authorized to create activities.";
    header("location: index.php");
    exit;
}

echo "<h1>Creating Test Activity</h1>";

try {
    // Check if the activity_questions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
    $activityQuestionsExists = $stmt->rowCount() > 0;

    if (!$activityQuestionsExists) {
        echo "<p>Creating activity_questions table...</p>";

        // Create activity_questions table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_questions` (
            `question_id` int(11) NOT NULL AUTO_INCREMENT,
            `activity_id` int(11) NOT NULL,
            `question_text` text NOT NULL,
            `question_type` varchar(50) NOT NULL,
            `points` decimal(10,2) NOT NULL DEFAULT 1.00,
            `position` int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY (`question_id`),
            KEY `activity_id` (`activity_id`),
            CONSTRAINT `activity_questions_ibfk_1` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`activity_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $pdo->exec($sql);
        echo "<p>activity_questions table created successfully.</p>";
    } else {
        echo "<p>activity_questions table already exists.</p>";
    }

    // Check if the activity_question_options table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_question_options'");
    $activityQuestionOptionsExists = $stmt->rowCount() > 0;

    if (!$activityQuestionOptionsExists) {
        echo "<p>Creating activity_question_options table...</p>";

        // Create activity_question_options table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_question_options` (
            `option_id` int(11) NOT NULL AUTO_INCREMENT,
            `question_id` int(11) NOT NULL,
            `option_text` text NOT NULL,
            `is_correct` tinyint(1) NOT NULL DEFAULT 0,
            `position` int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY (`option_id`),
            KEY `question_id` (`question_id`),
            CONSTRAINT `activity_question_options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `activity_questions` (`question_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $pdo->exec($sql);
        echo "<p>activity_question_options table created successfully.</p>";
    } else {
        echo "<p>activity_question_options table already exists.</p>";
    }

    // Check if the activity_answers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
    $activityAnswersExists = $stmt->rowCount() > 0;

    if (!$activityAnswersExists) {
        echo "<p>Creating activity_answers table...</p>";

        // Create activity_answers table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_answers` (
            `answer_id` int(11) NOT NULL AUTO_INCREMENT,
            `submission_id` int(11) NOT NULL,
            `question_id` int(11) NOT NULL,
            `answer_text` text DEFAULT NULL,
            `selected_option_id` int(11) DEFAULT NULL,
            `is_correct` tinyint(1) DEFAULT NULL,
            `points_earned` decimal(10,2) DEFAULT NULL,
            PRIMARY KEY (`answer_id`),
            KEY `submission_id` (`submission_id`),
            KEY `question_id` (`question_id`),
            KEY `selected_option_id` (`selected_option_id`),
            CONSTRAINT `activity_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $pdo->exec($sql);
        echo "<p>activity_answers table created successfully.</p>";
    } else {
        echo "<p>activity_answers table already exists.</p>";
    }

    // Get the first course ID (for testing purposes)
    $stmt = $pdo->query("SELECT course_id FROM courses LIMIT 1");
    $course = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$course) {
        echo "<p>No courses found. Please create a course first.</p>";
        exit;
    }

    $courseId = $course['course_id'];

    // Create a test activity
    $title = "Test Activity " . date('Y-m-d H:i:s');
    $description = "This is a test activity created for testing the activity submission functionality.";
    $activityType = "activity";
    $points = 10;
    $dueDate = date('Y-m-d H:i:s', strtotime('+1 week'));
    $isPublished = true;
    $createdBy = $_SESSION['user_id'];
    $allowLateSubmissions = true;

    $activityId = createActivity($courseId, $title, $description, $activityType, $points, $dueDate, $isPublished, $createdBy, $allowLateSubmissions);

    if (is_numeric($activityId)) {
        echo "<p>Activity created successfully with ID: $activityId</p>";

        // Create some test questions
        $questionText1 = "What is the capital of France?";
        $questionType1 = "multiple_choice";
        $points1 = 5;

        $questionId1 = addActivityQuestion($activityId, $questionText1, $questionType1, $points1);

        if (is_numeric($questionId1)) {
            echo "<p>Question 1 created successfully with ID: $questionId1</p>";

            // Add options for question 1
            $options1 = [
                ["Paris", true],
                ["London", false],
                ["Berlin", false],
                ["Madrid", false]
            ];

            foreach ($options1 as $index => $option) {
                $optionText = $option[0];
                $isCorrect = $option[1];
                $position = $index + 1;

                $optionId = addActivityQuestionOption($questionId1, $optionText, $isCorrect, $position);

                if (is_numeric($optionId)) {
                    echo "<p>Option '$optionText' added to Question 1 with ID: $optionId</p>";
                } else {
                    echo "<p>Error adding option to Question 1: $optionId</p>";
                }
            }
        } else {
            echo "<p>Error creating Question 1: $questionId1</p>";
        }

        $questionText2 = "Is the Earth flat?";
        $questionType2 = "true_false";
        $points2 = 5;

        $questionId2 = addActivityQuestion($activityId, $questionText2, $questionType2, $points2);

        if (is_numeric($questionId2)) {
            echo "<p>Question 2 created successfully with ID: $questionId2</p>";

            // Add options for question 2
            $options2 = [
                ["True", false],
                ["False", true]
            ];

            foreach ($options2 as $index => $option) {
                $optionText = $option[0];
                $isCorrect = $option[1];
                $position = $index + 1;

                $optionId = addActivityQuestionOption($questionId2, $optionText, $isCorrect, $position);

                if (is_numeric($optionId)) {
                    echo "<p>Option '$optionText' added to Question 2 with ID: $optionId</p>";
                } else {
                    echo "<p>Error adding option to Question 2: $optionId</p>";
                }
            }
        } else {
            echo "<p>Error creating Question 2: $questionId2</p>";
        }

        echo "<p>Test activity setup complete. <a href='activity_view.php?id=$activityId' class='btn btn-primary'>View Activity</a></p>";
    } else {
        echo "<p>Error creating activity: $activityId</p>";
    }
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
