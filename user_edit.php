<?php
/**
 * User Edit Page
 *
 * This page allows administrators to edit user accounts.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/user_functions.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "User ID is required.";
    header('Location: users.php');
    exit;
}

$userId = intval($_GET['id']);

// Check if role is provided
$role = isset($_GET['role']) ? $_GET['role'] : '';
$returnPage = 'users.php';

if ($role === 'teacher') {
    $returnPage = 'manage_teachers.php';
} elseif ($role === 'student') {
    $returnPage = 'manage_students.php';
}

// Get user details
$user = getUserById($userId);

// Check if user exists
if (is_string($user)) {
    $_SESSION['error'] = $user;
    header('Location: users.php');
    exit;
}

// Get all roles
$roles = getAllRoles();

// Check if roles is an error message
if (is_string($roles)) {
    $error = $roles;
    $roles = [];
}

// Initialize variables
$username = $user['username'];
$email = $user['email'];
$firstName = $user['first_name'];
$lastName = $user['last_name'];
$roleId = $user['role_id'];
$isActive = $user['is_active'];
$username_err = $email_err = $firstName_err = $lastName_err = $roleId_err = $password_err = "";

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } elseif (!filter_var(trim($_POST["email"]), FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email.";
    } else {
        $email = trim($_POST["email"]);
    }

    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $firstName_err = "Please enter a first name.";
    } else {
        $firstName = trim($_POST["first_name"]);
    }

    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $lastName_err = "Please enter a last name.";
    } else {
        $lastName = trim($_POST["last_name"]);
    }

    // Validate role
    if (empty(trim($_POST["role_id"]))) {
        $roleId_err = "Please select a role.";
    } else {
        $roleId = trim($_POST["role_id"]);
    }

    // Get active status
    $isActive = isset($_POST["is_active"]) ? 1 : 0;

    // Check if password is provided (optional)
    $password = trim($_POST["password"]);
    if (!empty($password) && strlen($password) < 6) {
        $password_err = "Password must have at least 6 characters.";
    }

    // Check input errors before updating the user
    if (empty($email_err) && empty($firstName_err) && empty($lastName_err) && empty($roleId_err) && empty($password_err)) {
        // Update user information
        $result = updateUser($userId, $email, $firstName, $lastName, $roleId, $isActive);

        if ($result === true) {
            // If password is provided, update it
            if (!empty($password)) {
                $passwordResult = changePassword($userId, "", $password);
                if ($passwordResult !== true) {
                    $error = $passwordResult;
                }
            }

            if (!isset($error)) {
                $_SESSION['success'] = "User updated successfully.";
                header("Location: $returnPage");
                exit;
            }
        } else {
            $error = $result;
        }
    }
}

// Set page title
$pageTitle = "Edit User";

// Include header
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit User</h1>
    <a href="<?php echo $returnPage; ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to <?php echo ucfirst($role === 'teacher' ? 'Teachers' : ($role === 'student' ? 'Students' : 'Users')); ?>
    </a>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-user-edit"></i> Edit User: <?php echo htmlspecialchars($username); ?></h5>
    </div>
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $userId . ($role ? '&role=' . $role : '')); ?>" method="post">
            <div class="form-group">
                <label>Username</label>
                <input type="text" class="form-control" value="<?php echo htmlspecialchars($username); ?>" disabled>
                <small class="form-text text-muted">Username cannot be changed.</small>
            </div>
            <div class="form-group">
                <label>Email</label>
                <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($email); ?>">
                <span class="invalid-feedback"><?php echo $email_err; ?></span>
            </div>
            <div class="form-group">
                <label>First Name</label>
                <input type="text" name="first_name" class="form-control <?php echo (!empty($firstName_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($firstName); ?>">
                <span class="invalid-feedback"><?php echo $firstName_err; ?></span>
            </div>
            <div class="form-group">
                <label>Last Name</label>
                <input type="text" name="last_name" class="form-control <?php echo (!empty($lastName_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($lastName); ?>">
                <span class="invalid-feedback"><?php echo $lastName_err; ?></span>
            </div>
            <div class="form-group">
                <label>Role</label>
                <select name="role_id" class="form-control <?php echo (!empty($roleId_err)) ? 'is-invalid' : ''; ?>">
                    <?php foreach ($roles as $role): ?>
                    <option value="<?php echo $role['role_id']; ?>" <?php echo ($roleId == $role['role_id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars(ucfirst($role['role_name'])); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
                <span class="invalid-feedback"><?php echo $roleId_err; ?></span>
            </div>
            <div class="form-group">
                <label>Password</label>
                <input type="password" name="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>">
                <small class="form-text text-muted">Leave blank to keep current password.</small>
                <span class="invalid-feedback"><?php echo $password_err; ?></span>
            </div>
            <div class="form-group">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" <?php echo $isActive ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="is_active">Active</label>
                </div>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Update User</button>
                <a href="<?php echo $returnPage; ?>" class="btn btn-secondary ml-2">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
