<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

if (!$foundFile) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found. Please contact the administrator.";
    exit;
}

// Get file information
$fileInfo = pathinfo($filePath);
$fileName = $fileInfo['basename'];
$fileExtension = strtolower($fileInfo['extension']);

// Determine file type
$fileType = 'unknown';
if (in_array($fileExtension, ['doc', 'docx'])) {
    $fileType = 'word';
} elseif (in_array($fileExtension, ['xls', 'xlsx'])) {
    $fileType = 'excel';
} elseif (in_array($fileExtension, ['ppt', 'pptx'])) {
    $fileType = 'powerpoint';
}

// Get file size
$fileSize = filesize($filePath);

// Get absolute URL to the file
$fileUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . "/final/file_viewer.php?file=" . urlencode($filePath);

// Set page title
$page_title = "View File: " . $fileName;

// Don't include the standard header for this page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($fileName); ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        .viewer-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .toolbar {
            background-color: #f8f9fa;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #dee2e6;
        }
        .content-frame {
            flex: 1;
            border: none;
            width: 100%;
            height: calc(100% - 60px);
        }
    </style>
</head>
<body>
    <div class="viewer-container">
        <div class="toolbar">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-file-<?php
                        if ($fileType == 'word') echo 'word';
                        elseif ($fileType == 'excel') echo 'excel';
                        elseif ($fileType == 'powerpoint') echo 'powerpoint';
                        else echo 'alt';
                    ?> mr-2"></i> <?php echo htmlspecialchars($fileName); ?>
                </h5>
            </div>
            <div>
                <?php
                // Get the referer URL if available
                $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

                // Check if referer is from our site
                $isFromOurSite = !empty($referer) && (strpos($referer, $_SERVER['HTTP_HOST']) !== false);

                // If we have a valid referer, use it; otherwise, go to home
                $backUrl = $isFromOurSite ? $referer : 'index.php';
                ?>
                <a href="<?php echo htmlspecialchars($backUrl); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
                <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-primary ml-2">
                    <i class="fas fa-download mr-2"></i> Download
                </a>
            </div>
        </div>

        <?php
        // For Office files, try to use Google Docs Viewer
        $googleDocsUrl = "https://docs.google.com/viewer?url=" . urlencode($fileUrl) . "&embedded=true";

        // Check if we're on localhost - Google Docs viewer won't work with localhost URLs
        $isLocalhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ||
                        strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
        ?>

        <?php if ($isLocalhost): ?>
            <div class="d-flex flex-column align-items-center justify-content-center h-100">
                <div class="text-center p-4">
                    <div class="mb-4">
                        <i class="fas fa-file-<?php
                            if ($fileType == 'word') echo 'word';
                            elseif ($fileType == 'excel') echo 'excel';
                            elseif ($fileType == 'powerpoint') echo 'powerpoint';
                            else echo 'alt';
                        ?> fa-4x text-primary mb-3"></i>
                        <h4><?php echo htmlspecialchars($fileName); ?></h4>
                    </div>

                    <div class="alert alert-warning">
                        <p><strong>Local Development Environment Detected</strong></p>
                        <p>Google Docs Viewer cannot access files on localhost. Please download the file to view it.</p>
                    </div>

                    <div class="mt-4">
                        <a href="download_file.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-primary">
                            <i class="fas fa-download mr-2"></i> Download File
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <iframe src="<?php echo $googleDocsUrl; ?>" class="content-frame" allowfullscreen></iframe>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
<?php
// No footer needed for this page
?>
