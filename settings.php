<?php
/**
 * System Settings Page
 *
 * This page allows administrators to manage system settings.
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/notifications.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Process form submissions
$message = '';
$messageType = '';

// Handle settings update
if (isset($_POST['update_settings'])) {
    $siteName = trim($_POST['site_name']);
    $adminEmail = trim($_POST['admin_email']);
    $adminName = trim($_POST['admin_name']);

    // Get theme setting
    $theme = isset($_POST['theme']) ? $_POST['theme'] : 'light';

    // Get notification settings
    $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
    $commentsOnPosts = isset($_POST['comments_on_posts']) ? 1 : 0;
    $commentsMentions = isset($_POST['comments_mentions']) ? 1 : 0;
    $privateComments = isset($_POST['private_comments']) ? 1 : 0;

    // Calendar notification settings
    $eventCreation = isset($_POST['event_creation']) ? 1 : 0;
    $eventUpdates = isset($_POST['event_updates']) ? 1 : 0;
    $eventReminders = isset($_POST['event_reminders']) ? 1 : 0;
    $statusChanges = isset($_POST['status_changes']) ? 1 : 0;

    // Admin notification settings
    $userRegistrations = isset($_POST['user_registrations']) ? 1 : 0;
    $courseCreation = isset($_POST['course_creation']) ? 1 : 0;
    $accountChanges = isset($_POST['account_changes']) ? 1 : 0;
    $errorAlerts = isset($_POST['error_alerts']) ? 1 : 0;
    $weeklyReports = isset($_POST['weekly_reports']) ? 1 : 0;

    // Validate input
    if (empty($siteName)) {
        $message = 'Site name is required';
        $messageType = 'danger';
    } else if (empty($adminEmail)) {
        $message = 'Admin email is required';
        $messageType = 'danger';
    } else {
        try {
            // Update settings in the database
            // In a real application, you would have a settings table
            // For now, we'll just show a success message

            // Update admin name if changed
            if (!empty($adminName) && $adminName != $_SESSION['first_name'] . ' ' . $_SESSION['last_name']) {
                // In a real app, update the admin's name in the database
                // For now, just update the session
                $nameParts = explode(' ', $adminName, 2);
                $_SESSION['first_name'] = $nameParts[0];
                $_SESSION['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            }

            // Save theme preference as a cookie
            setcookie('theme', $theme, time() + (86400 * 30), "/"); // 30 days expiration

            // Update notification settings in the database
            global $pdo;

            // Check if user_notification_settings table exists
            $tableExists = false;
            $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
            if ($checkTable && $checkTable->rowCount() > 0) {
                $tableExists = true;
            }

            // If table doesn't exist, create it
            if (!$tableExists) {
                $createTable = "CREATE TABLE user_notification_settings (
                    setting_id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    email_notifications TINYINT(1) DEFAULT 1,
                    event_creation TINYINT(1) DEFAULT 1,
                    event_updates TINYINT(1) DEFAULT 1,
                    event_reminders TINYINT(1) DEFAULT 1,
                    status_changes TINYINT(1) DEFAULT 1,
                    system_announcements TINYINT(1) DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
                )";

                $pdo->query($createTable);
            }

            // Check if user has settings
            $checkSettings = $pdo->prepare("SELECT setting_id FROM user_notification_settings WHERE user_id = :user_id");
            $checkSettings->execute([':user_id' => $_SESSION['user_id']]);

            if ($checkSettings->rowCount() > 0) {
                // Update existing settings
                $updateSettings = $pdo->prepare("UPDATE user_notification_settings SET
                    email_notifications = :email_notifications,
                    event_creation = :event_creation,
                    event_updates = :event_updates,
                    event_reminders = :event_reminders,
                    status_changes = :status_changes,
                    system_announcements = :system_announcements
                    WHERE user_id = :user_id");

                $updateSettings->execute([
                    ':email_notifications' => $emailNotifications,
                    ':event_creation' => $eventCreation,
                    ':event_updates' => $eventUpdates,
                    ':event_reminders' => $eventReminders,
                    ':status_changes' => $statusChanges,
                    ':system_announcements' => 1, // Default to enabled
                    ':user_id' => $_SESSION['user_id']
                ]);
            } else {
                // Insert new settings
                $insertSettings = $pdo->prepare("INSERT INTO user_notification_settings
                    (user_id, email_notifications, event_creation, event_updates, event_reminders, status_changes, system_announcements)
                    VALUES (:user_id, :email_notifications, :event_creation, :event_updates, :event_reminders, :status_changes, :system_announcements)");

                $insertSettings->execute([
                    ':user_id' => $_SESSION['user_id'],
                    ':email_notifications' => $emailNotifications,
                    ':event_creation' => $eventCreation,
                    ':event_updates' => $eventUpdates,
                    ':event_reminders' => $eventReminders,
                    ':status_changes' => $statusChanges,
                    ':system_announcements' => 1 // Default to enabled
                ]);
            }

            $message = 'Settings updated successfully';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get current settings
// In a real application, you would fetch these from the database
$settings = [
    'site_name' => 'Classroom',
    'site_description' => 'E-Learning Management System',
    'admin_email' => '<EMAIL>',
    'timezone' => 'UTC',
    'date_format' => 'M d, Y',
    'time_format' => 'h:i A'
];

// Get notification settings from the database
$notificationSettings = [
    'email_notifications' => 1,
    'event_creation' => 1,
    'event_updates' => 1,
    'event_reminders' => 1,
    'status_changes' => 1,
    'system_announcements' => 1
];

try {
    // Check if user_notification_settings table exists
    $tableExists = false;
    $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($checkTable && $checkTable->rowCount() > 0) {
        $tableExists = true;
    }

    if ($tableExists) {
        // Get user's notification settings
        $getSettings = $pdo->prepare("SELECT * FROM user_notification_settings WHERE user_id = :user_id");
        $getSettings->execute([':user_id' => $_SESSION['user_id']]);
        $userSettings = $getSettings->fetch(PDO::FETCH_ASSOC);

        if ($userSettings) {
            $notificationSettings = [
                'email_notifications' => $userSettings['email_notifications'],
                'event_creation' => $userSettings['event_creation'],
                'event_updates' => $userSettings['event_updates'],
                'event_reminders' => $userSettings['event_reminders'],
                'status_changes' => $userSettings['status_changes'],
                'system_announcements' => $userSettings['system_announcements']
            ];
        }
    }
} catch (PDOException $e) {
    // Just use default settings if there's an error
    error_log("Error getting notification settings: " . $e->getMessage());
}

// Page title
$pageTitle = 'System Settings';

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Settings</h1>
        </div>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body">
                    <form action="settings.php" method="post">
                        <!-- Profile Section -->
                        <h4 class="mb-4">Profile</h4>
                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Profile picture</label>
                            <div class="col-sm-9 d-flex align-items-center">
                                <div class="user-avatar mr-3" style="width: 40px; height: 40px; background-color: #1a73e8; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                    <?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?>
                                </div>
                                <a href="#" class="text-primary">Change</a>
                            </div>
                        </div>

                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Account settings</label>
                            <div class="col-sm-9">
                                <p class="mb-1">Change your password and security options</p>
                                <a href="#" class="text-primary">Manage</a>
                            </div>
                        </div>

                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Change name</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control mb-2" id="admin_name" name="admin_name"
                                       value="<?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>">
                                <small class="text-muted">As an admin, you can change your display name</small>
                            </div>
                        </div>

                        <!-- System Settings Section -->
                        <h4 class="mb-4 mt-5">System Settings</h4>

                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Theme</label>
                            <div class="col-sm-9">
                                <div class="form-group">
                                    <label>Choose Theme</label>
                                    <div class="d-flex mt-2">
                                        <div class="custom-control custom-radio mr-4">
                                            <input type="radio" id="lightTheme" name="theme" value="light" class="custom-control-input theme-switch" <?php echo (!isset($_COOKIE['theme']) || $_COOKIE['theme'] === 'light') ? 'checked' : ''; ?>>
                                            <label class="custom-control-label" for="lightTheme">Light Mode</label>
                                        </div>
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="darkTheme" name="theme" value="dark" class="custom-control-input theme-switch" <?php echo (isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark') ? 'checked' : ''; ?>>
                                            <label class="custom-control-label" for="darkTheme">Dark Mode</label>
                                        </div>
                                    </div>
                                    <small class="text-muted">Choose between light and dark theme for the application</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Site Name</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="site_name" name="site_name"
                                       value="<?php echo htmlspecialchars($settings['site_name']); ?>" required>
                                <small class="text-muted">The name of your e-learning platform</small>
                            </div>
                        </div>

                        <div class="form-group row mb-4 pb-3 border-bottom">
                            <label class="col-sm-3 col-form-label">Admin Email</label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" id="admin_email" name="admin_email"
                                       value="<?php echo htmlspecialchars($settings['admin_email']); ?>" required>
                                <small class="text-muted">Primary contact email for system notifications</small>
                            </div>
                        </div>



                        <!-- Notifications Section -->
                        <h4 class="mb-4 mt-5">Notifications</h4>

                        <div class="mb-4">
                            <h5>Email</h5>
                            <p class="text-muted small">These settings apply to the notifications you get by email</p>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Allow email notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="emailNotifications" name="email_notifications" <?php echo $notificationSettings['email_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="emailNotifications"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5>Comments</h5>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Comments on your posts</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="commentsOnPosts" name="comments_on_posts" checked>
                                        <label class="custom-control-label" for="commentsOnPosts"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Comments that mention you</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="commentsMentions" name="comments_mentions" checked>
                                        <label class="custom-control-label" for="commentsMentions"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Private comments on work</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="privateComments" name="private_comments" checked>
                                        <label class="custom-control-label" for="privateComments"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5>Calendar Notifications</h5>
                            <p class="text-muted small">Control which calendar event notifications you receive</p>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Event creation notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="eventCreation" name="event_creation" <?php echo $notificationSettings['event_creation'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="eventCreation"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Event update notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="eventUpdates" name="event_updates" <?php echo $notificationSettings['event_updates'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="eventUpdates"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Event reminder notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="eventReminders" name="event_reminders" <?php echo $notificationSettings['event_reminders'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="eventReminders"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Status change notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="statusChanges" name="status_changes" <?php echo $notificationSettings['status_changes'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="statusChanges"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5>Admin Notifications</h5>
                            <p class="text-muted small">Control which administrative notifications you receive</p>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">New user registrations</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="userRegistrations" name="user_registrations" checked>
                                        <label class="custom-control-label" for="userRegistrations"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Course creation notifications</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="courseCreation" name="course_creation" checked>
                                        <label class="custom-control-label" for="courseCreation"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">User account changes</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="accountChanges" name="account_changes" checked>
                                        <label class="custom-control-label" for="accountChanges"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">System error alerts</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="errorAlerts" name="error_alerts" checked>
                                        <label class="custom-control-label" for="errorAlerts"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row mb-3 align-items-center">
                                <label class="col-sm-9 col-form-label">Weekly system reports</label>
                                <div class="col-sm-3 text-right">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="weeklyReports" name="weekly_reports" checked>
                                        <label class="custom-control-label" for="weeklyReports"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" name="update_settings" class="btn btn-primary mt-3">Save Settings</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.custom-switch .custom-control-label::before {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 1rem;
}

.custom-switch .custom-control-label::after {
    width: calc(1.25rem - 4px);
    height: calc(1.25rem - 4px);
    border-radius: 50%;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1.25rem);
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.text-primary {
    color: #1a73e8 !important;
}

.btn-primary {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.btn-primary:hover {
    background-color: #1765cc;
    border-color: #1765cc;
}
</style>

<script>
    // Theme switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeRadios = document.querySelectorAll('.theme-switch');

        themeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                const theme = this.value;

                // Set cookie immediately
                document.cookie = `theme=${theme}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days

                // Apply theme immediately
                const themeLink = document.querySelector('link[href^="css/"][href$="-theme.css"]');
                if (themeLink) {
                    themeLink.href = `css/${theme}-theme.css`;
                }

                // Add or remove dark-mode class from body
                if (theme === 'dark') {
                    document.body.classList.add('dark-mode');
                } else {
                    document.body.classList.remove('dark-mode');
                }
            });
        });
    });
</script>

<?php
require_once 'includes/footer.php';
?>
