<?php
// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "<br>");
}

echo "<h1>Database Fix Script</h1>";

// Check if both role and role_id columns exist
$result_role = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
$result_role_id = $conn->query("SHOW COLUMNS FROM users LIKE 'role_id'");

if ($result_role->num_rows > 0 && $result_role_id->num_rows > 0) {
    // Both columns exist, update role values and drop role_id
    echo "Both role and role_id columns exist<br>";

    // Update the role column based on role_id
    // Assuming role_id 1 = admin, 2 = teacher, 3 = student
    $sql = "UPDATE users SET role = 'admin' WHERE role_id = 1";
    $conn->query($sql);

    $sql = "UPDATE users SET role = 'teacher' WHERE role_id = 2";
    $conn->query($sql);

    $sql = "UPDATE users SET role = 'student' WHERE role_id = 3";
    $conn->query($sql);

    echo "Updated role values based on role_id<br>";

    // Now drop the role_id column
    $sql = "ALTER TABLE users DROP COLUMN role_id";

    if ($conn->query($sql) === TRUE) {
        echo "Successfully dropped role_id column<br>";
    } else {
        echo "Error dropping role_id column: " . $conn->error . "<br>";
    }
} elseif ($result_role_id->num_rows > 0) {
    // Only role_id exists, add role column
    $sql = "ALTER TABLE users ADD COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student' AFTER password";

    if ($conn->query($sql) === TRUE) {
        echo "Successfully added role column<br>";

        // Update the role column based on role_id
        // Assuming role_id 1 = admin, 2 = teacher, 3 = student
        $sql = "UPDATE users SET role = 'admin' WHERE role_id = 1";
        $conn->query($sql);

        $sql = "UPDATE users SET role = 'teacher' WHERE role_id = 2";
        $conn->query($sql);

        $sql = "UPDATE users SET role = 'student' WHERE role_id = 3";
        $conn->query($sql);

        echo "Updated role values based on role_id<br>";

        // Now drop the role_id column
        $sql = "ALTER TABLE users DROP COLUMN role_id";

        if ($conn->query($sql) === TRUE) {
            echo "Successfully dropped role_id column<br>";
        } else {
            echo "Error dropping role_id column: " . $conn->error . "<br>";
        }
    } else {
        echo "Error adding role column: " . $conn->error . "<br>";
    }
} elseif ($result_role->num_rows == 0) {
    // Neither column exists, add role column
    $sql = "ALTER TABLE users ADD COLUMN role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student' AFTER password";

    if ($conn->query($sql) === TRUE) {
        echo "Successfully added role column<br>";
    } else {
        echo "Error adding role column: " . $conn->error . "<br>";
    }
} else {
    echo "Role column already exists<br>";
}

// Show the current table structure
$result = $conn->query("SHOW COLUMNS FROM users");
if ($result) {
    echo "<h2>Current Users Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }

    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

// Create admin user if it doesn't exist
$stmt = $conn->prepare("SELECT user_id FROM users WHERE username = 'admin'");
if ($stmt) {
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows == 0) {
        // Create admin user
        $admin_password = password_hash("admin123", PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, email, first_name, last_name, role, is_active)
                VALUES ('admin', '$admin_password', '<EMAIL>', 'Admin', 'User', 'admin', 1)";

        if ($conn->query($sql) === TRUE) {
            echo "<p>Admin user created successfully</p>";
        } else {
            echo "<p>Error creating admin user: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Admin user already exists</p>";
    }
    $stmt->close();
}

echo "<p>Database fix complete. You can now <a href='register.php'>register</a> or <a href='login.php'>login</a>.</p>";

$conn->close();
?>
