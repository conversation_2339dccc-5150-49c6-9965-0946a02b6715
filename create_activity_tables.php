<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin
if (!isAdmin()) {
    echo "You must be an admin to run this script.";
    exit;
}

// Create activity_questions table if it doesn't exist
try {
    $pdo->beginTransaction();
    
    // Check if activity_questions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
    $activityQuestionsExists = $stmt->rowCount() > 0;
    
    if (!$activityQuestionsExists) {
        echo "Creating activity_questions table...<br>";
        
        $pdo->exec("
            CREATE TABLE activity_questions (
                question_id INT AUTO_INCREMENT PRIMARY KEY,
                activity_id INT NOT NULL,
                question_text TEXT NOT NULL,
                question_type VARCHAR(50) NOT NULL,
                points INT DEFAULT 1,
                position INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE
            )
        ");
        
        echo "activity_questions table created successfully.<br>";
    } else {
        echo "activity_questions table already exists.<br>";
    }
    
    // Check if activity_question_options table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_question_options'");
    $activityQuestionOptionsExists = $stmt->rowCount() > 0;
    
    if (!$activityQuestionOptionsExists) {
        echo "Creating activity_question_options table...<br>";
        
        $pdo->exec("
            CREATE TABLE activity_question_options (
                option_id INT AUTO_INCREMENT PRIMARY KEY,
                question_id INT NOT NULL,
                option_text TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                position INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (question_id) REFERENCES activity_questions(question_id) ON DELETE CASCADE
            )
        ");
        
        echo "activity_question_options table created successfully.<br>";
    } else {
        echo "activity_question_options table already exists.<br>";
    }
    
    // Create a sample question for activity ID 6
    $activityId = 6;
    
    // Check if activity exists
    $stmt = $pdo->prepare("SELECT activity_id FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "Creating sample questions for activity ID $activityId...<br>";
        
        // Create a multiple choice question
        $stmt = $pdo->prepare("
            INSERT INTO activity_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, 'What is the capital of France?', 'multiple_choice', 5, 0)
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $mcQuestionId = $pdo->lastInsertId();
        
        // Add options for the multiple choice question
        $stmt = $pdo->prepare("
            INSERT INTO activity_question_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, 'London', 0, 0),
                   (:questionId, 'Paris', 1, 1),
                   (:questionId, 'Berlin', 0, 2),
                   (:questionId, 'Madrid', 0, 3)
        ");
        $stmt->bindParam(':questionId', $mcQuestionId);
        $stmt->execute();
        
        // Create a true/false question
        $stmt = $pdo->prepare("
            INSERT INTO activity_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, 'The Earth is flat.', 'true_false', 3, 1)
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $tfQuestionId = $pdo->lastInsertId();
        
        // Add options for the true/false question
        $stmt = $pdo->prepare("
            INSERT INTO activity_question_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, 'True', 0, 0),
                   (:questionId, 'False', 1, 1)
        ");
        $stmt->bindParam(':questionId', $tfQuestionId);
        $stmt->execute();
        
        // Create a short answer question
        $stmt = $pdo->prepare("
            INSERT INTO activity_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, 'What is the chemical symbol for water?', 'short_answer', 4, 2)
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();
        $saQuestionId = $pdo->lastInsertId();
        
        // Add correct answer for the short answer question
        $stmt = $pdo->prepare("
            INSERT INTO activity_question_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, 'H2O', 1, 0)
        ");
        $stmt->bindParam(':questionId', $saQuestionId);
        $stmt->execute();
        
        echo "Sample questions created successfully.<br>";
    } else {
        echo "Activity ID $activityId does not exist. Sample questions not created.<br>";
    }
    
    $pdo->commit();
    echo "All operations completed successfully.";
} catch (PDOException $e) {
    $pdo->rollBack();
    echo "Error: " . $e->getMessage();
}
