<?php
// Include configuration file
require_once 'includes/config.php';

// Get the activity ID from the URL
$activityId = isset($_GET['id']) ? intval($_GET['id']) : 1;

try {
    // First, check the current activity type
    $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        echo "<h2>Activity not found</h2>";
        echo "<p>No activity found with ID: $activityId</p>";
        exit;
    }
    
    $activity = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentType = $activity['activity_type'];
    
    echo "<h2>Activity Details</h2>";
    echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
    echo "<p><strong>Current Type:</strong> " . htmlspecialchars($currentType) . "</p>";
    
    // Check if the activity_type column in the database includes 'activity'
    $stmt = $pdo->query("SHOW COLUMNS FROM activities LIKE 'activity_type'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($column) {
        $type = $column['Type'];
        echo "<p>Current activity_type definition: " . htmlspecialchars($type) . "</p>";
        
        // Check if 'activity' is already in the enum
        if (strpos($type, "'activity'") === false) {
            // Add 'activity' to the enum
            $newType = str_replace("enum(", "enum('activity',", $type);
            
            // Alter the table
            $pdo->exec("ALTER TABLE activities MODIFY COLUMN activity_type " . $newType);
            
            echo "<p>✓ Successfully added 'activity' to the activity_type enum.</p>";
        } else {
            echo "<p>The 'activity' type is already included in the activity_type enum.</p>";
        }
    }
    
    // Update the activity type to 'activity'
    $newType = 'activity';
    $updateStmt = $pdo->prepare("UPDATE activities SET activity_type = :newType WHERE activity_id = :activityId");
    $updateStmt->bindParam(':newType', $newType);
    $updateStmt->bindParam(':activityId', $activityId);
    
    if ($updateStmt->execute()) {
        echo "<p style='color: green;'>Activity type updated from '{$currentType}' to '{$newType}'.</p>";
        
        // Force refresh the page by redirecting
        echo "<script>
            setTimeout(function() {
                window.location.href = 'activity_edit.php?id={$activityId}&tab=questions';
            }, 2000);
        </script>";
        
        echo "<p>Redirecting to the activity edit page in 2 seconds...</p>";
        echo "<p>If you are not redirected, <a href='activity_edit.php?id=$activityId&tab=questions'>click here</a>.</p>";
    } else {
        echo "<p style='color: red;'>Error updating activity type.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
