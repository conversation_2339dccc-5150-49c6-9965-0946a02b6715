<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';

echo "<h1>Login Debug</h1>";
echo "<p>This script helps debug login issues by testing the login functionality directly.</p>";

// Function to test a login
function testLogin($username, $password) {
    global $pdo;

    echo "<h2>Testing login for username: $username</h2>";

    try {
        // Check if user exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM users
            WHERE username = :username
        ");
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['count'] > 0) {
            echo "<p style='color: green;'>✓ User exists in the database</p>";
        } else {
            echo "<p style='color: red;'>✗ User does not exist in the database</p>";
            return;
        }

        // Get user data
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.password, u.email, u.first_name, u.last_name, u.is_active, u.role_id, r.role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.role_id
            WHERE u.username = :username
        ");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            echo "<h3>User Data:</h3>";
            echo "<table border='1'>";
            foreach ($user as $key => $value) {
                if ($key !== 'password') {
                    echo "<tr><td>$key</td><td>" . htmlspecialchars($value) . "</td></tr>";
                } else {
                    echo "<tr><td>$key</td><td>[HIDDEN]</td></tr>";
                }
            }
            echo "</table>";

            // Check if account is active
            if (!$user['is_active']) {
                echo "<p style='color: red;'>✗ Account is not active</p>";
                return;
            } else {
                echo "<p style='color: green;'>✓ Account is active</p>";
            }

            // Verify password
            if (password_verify($password, $user['password'])) {
                echo "<p style='color: green;'>✓ Password is correct</p>";
            } else {
                echo "<p style='color: red;'>✗ Password is incorrect</p>";

                // Debug password hash
                echo "<p>Debug info:</p>";
                echo "<ul>";
                echo "<li>Stored password hash: " . substr($user['password'], 0, 10) . "...</li>";
                echo "<li>Password hash algorithm: " . password_get_info($user['password'])['algoName'] . "</li>";
                echo "</ul>";

                // Generate a new hash for comparison
                $newHash = password_hash($password, PASSWORD_DEFAULT);
                echo "<p>New hash of the same password: " . substr($newHash, 0, 10) . "...</p>";

                // Check if the new hash would verify
                if (password_verify($password, $newHash)) {
                    echo "<p style='color: green;'>✓ New hash verification works</p>";
                    echo "<p>This suggests the password_verify function is working correctly, but the stored hash in the database may be incorrect or using a different algorithm.</p>";
                } else {
                    echo "<p style='color: red;'>✗ New hash verification failed</p>";
                    echo "<p>This suggests there may be an issue with the password_verify function or PHP configuration.</p>";
                }
            }

            // Check role
            if (empty($user['role_name'])) {
                echo "<p style='color: red;'>✗ Role is missing or invalid</p>";

                // Check if role_id exists in roles table
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM roles WHERE role_id = :role_id");
                $stmt->bindParam(':role_id', $user['role_id']);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($result['count'] > 0) {
                    echo "<p style='color: orange;'>⚠ Role ID exists in roles table but join failed</p>";
                } else {
                    echo "<p style='color: red;'>✗ Role ID does not exist in roles table</p>";
                }
            } else {
                echo "<p style='color: green;'>✓ Role is valid: " . htmlspecialchars($user['role_name']) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Failed to retrieve user data</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

// Add a section to test the actual loginUser function from auth.php
echo "<hr>";
echo "<h2>Testing loginUser() Function</h2>";

// Test with admin user
echo "<h3>Testing admin login:</h3>";
$result = loginUser("admin", "admin123");
if ($result === true) {
    echo "<p style='color: green;'>✓ Login successful using loginUser() function!</p>";
} else {
    echo "<p style='color: red;'>✗ Login failed using loginUser() function: " . $result . "</p>";
}

// Test with a non-existent user
echo "<h3>Testing non-existent user:</h3>";
$result = loginUser("nonexistentuser", "password");
if ($result === true) {
    echo "<p style='color: red;'>✗ Login unexpectedly succeeded for non-existent user!</p>";
} else {
    echo "<p style='color: green;'>✓ Login correctly failed: " . $result . "</p>";
}

// Test with the test user created in fix_passwords.php
echo "<h3>Testing test user:</h3>";
$stmt = $pdo->query("SELECT username FROM users WHERE username LIKE 'testuser%' ORDER BY user_id DESC LIMIT 1");
if ($stmt->rowCount() > 0) {
    $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
    $testUsername = $testUser['username'];
    echo "<p>Found test user: " . $testUsername . "</p>";
    $result = loginUser($testUsername, "password123");
    if ($result === true) {
        echo "<p style='color: green;'>✓ Login successful for test user using loginUser() function!</p>";
    } else {
        echo "<p style='color: red;'>✗ Login failed for test user using loginUser() function: " . $result . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ No test user found. Please run fix_passwords.php first.</p>";
}

// Test with detailed debugging
echo "<hr>";
echo "<h2>Detailed Login Debugging</h2>";

// Test with admin user
echo "<h3>Admin user:</h3>";
testLogin("admin", "admin123");

// Test with a non-existent user
echo "<h3>Non-existent user:</h3>";
testLogin("nonexistentuser", "password");

// Create a form to test custom login
echo "<hr>";
echo "<h2>Test Custom Login</h2>";
echo "<form method='post'>";
echo "<div style='margin-bottom: 10px;'><label>Username: <input type='text' name='username' required></label></div>";
echo "<div style='margin-bottom: 10px;'><label>Password: <input type='password' name='password' required></label></div>";
echo "<div><button type='submit'>Test Login</button></div>";
echo "</form>";

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['username']) && isset($_POST['password'])) {
    echo "<hr>";
    testLogin($_POST['username'], $_POST['password']);
}
?>
