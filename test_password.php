<?php
// Test password hashing and verification
$password = 'admin123';
$hashedPassword = password_hash($password, PASSWORD_DEFAULT);

echo "Original password: $password<br>";
echo "Hashed password: $hashedPassword<br>";

// Get the stored hash from the database
require_once 'includes/config.php';
global $pdo;

try {
    $stmt = $pdo->prepare("SELECT password FROM users WHERE username = 'admin'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if ($user) {
        echo "Stored hash in database: " . $user['password'] . "<br>";
        
        // Verify the password against the stored hash
        if (password_verify($password, $user['password'])) {
            echo "Password verification successful!<br>";
        } else {
            echo "Password verification failed!<br>";
        }
    } else {
        echo "User not found in database.<br>";
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}
?>
