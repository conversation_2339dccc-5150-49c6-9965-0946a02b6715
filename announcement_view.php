<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';
require_once 'includes/announcement_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if announcement ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Announcement ID is required.";
    header("location: index.php");
    exit;
}

$announcementId = intval($_GET['id']);

// Get announcement details
$announcement = getAnnouncementById($announcementId);

// Check if announcement exists
if (is_string($announcement)) {
    $_SESSION['error'] = $announcement;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $announcement['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this course
if (!isAdmin() && !isTeacher() && !isEnrolled($_SESSION['user_id'], $courseId)) {
    $_SESSION['error'] = "You do not have access to this announcement.";
    header("location: index.php");
    exit;
}

// Get announcement files
$files = getAnnouncementFiles($announcementId);
if (is_string($files)) {
    $files = [];
}

// Get announcement comments
$comments = getAnnouncementComments($announcementId);
if (is_string($comments)) {
    $comments = [];
}

// Process comment form submission
$comment_content = "";
$comment_err = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'add_comment') {
    // Validate comment content
    if (empty(trim($_POST["comment_content"]))) {
        $comment_err = "Please enter a comment.";
    } else {
        $comment_content = trim($_POST["comment_content"]);
    }

    // Check input errors before adding the comment
    if (empty($comment_err)) {
        // Add the comment
        $result = addAnnouncementComment($announcementId, $_SESSION['user_id'], $comment_content);

        if (is_numeric($result)) {
            // Comment added successfully
            $_SESSION['success'] = "Comment added successfully.";
            header("location: announcement_view.php?id=$announcementId");
            exit;
        } else {
            // Error adding comment
            $_SESSION['error'] = $result;
        }
    }
}

// Process reply form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'add_reply') {
    // Validate reply content
    if (empty(trim($_POST["reply_content"]))) {
        $_SESSION['error'] = "Reply content cannot be empty.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }

    // Validate comment ID
    if (!isset($_POST['comment_id']) || empty($_POST['comment_id'])) {
        $_SESSION['error'] = "Invalid comment ID.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }

    $commentId = intval($_POST['comment_id']);
    $replyContent = trim($_POST['reply_content']);

    // Add the reply
    $result = addCommentReply($commentId, $_SESSION['user_id'], $replyContent);

    if (is_numeric($result)) {
        // Reply added successfully
        $_SESSION['success'] = "Reply added successfully.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    } else {
        // Error adding reply
        $_SESSION['error'] = $result;
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }
}

// Process comment/reply edit form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'edit_comment') {
    // Validate content
    if (empty(trim($_POST["edit_content"]))) {
        $_SESSION['error'] = "Content cannot be empty.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }

    // Validate comment/reply ID
    if (!isset($_POST['item_id']) || empty($_POST['item_id']) || !isset($_POST['item_type']) || empty($_POST['item_type'])) {
        $_SESSION['error'] = "Invalid item information.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }

    $itemId = intval($_POST['item_id']);
    $itemType = $_POST['item_type'];
    $content = trim($_POST['edit_content']);

    // Edit the comment/reply
    if ($itemType == 'comment') {
        $result = editAnnouncementComment($itemId, $_SESSION['user_id'], $content);
    } else {
        $result = editCommentReply($itemId, $_SESSION['user_id'], $content);
    }

    if ($result === true) {
        // Edit successful
        $_SESSION['success'] = ucfirst($itemType) . " updated successfully.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    } else {
        // Error editing
        $_SESSION['error'] = $result;
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }
}

// Process comment/reply delete form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'delete_item') {
    // Validate item ID and type
    if (!isset($_POST['item_id']) || empty($_POST['item_id']) || !isset($_POST['item_type']) || empty($_POST['item_type'])) {
        $_SESSION['error'] = "Invalid item information.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }

    $itemId = intval($_POST['item_id']);
    $itemType = $_POST['item_type'];

    // Delete the comment/reply
    if ($itemType == 'comment') {
        $result = deleteAnnouncementComment($itemId, $_SESSION['user_id']);
    } else {
        $result = deleteCommentReply($itemId, $_SESSION['user_id']);
    }

    if ($result === true) {
        // Delete successful
        $_SESSION['success'] = ucfirst($itemType) . " deleted successfully.";
        header("location: announcement_view.php?id=$announcementId");
        exit;
    } else {
        // Error deleting
        $_SESSION['error'] = $result;
        header("location: announcement_view.php?id=$announcementId");
        exit;
    }
}

// Set page title
$page_title = $announcement['title'] . " - Announcement";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=stream" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Announcement card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo htmlspecialchars($announcement['title']); ?></h4>
            <span><?php echo date('F j, Y', strtotime($announcement['announcement_date'])); ?></span>
        </div>
    </div>
    <div class="card-body">
        <div class="d-flex align-items-start mb-3">
            <?php
            // Get user profile picture
            $announcementProfilePic = '';
            try {
                $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                $stmt->bindParam(':userId', $announcement['created_by']);
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    $userData = $stmt->fetch();
                    if (!empty($userData['profile_picture'])) {
                        $announcementProfilePic = $userData['profile_picture'];
                    }
                }
            } catch (PDOException $e) {
                // Silently fail and use default avatar
            }

            if (!empty($announcementProfilePic)):
            ?>
                <img src="<?php echo htmlspecialchars($announcementProfilePic); ?>" class="rounded-circle mr-3" width="40" height="40" alt="User avatar" style="object-fit: cover;">
            <?php else: ?>
                <div class="user-avatar mr-3">
                    <?php echo strtoupper(substr($announcement['first_name'], 0, 1)); ?>
                </div>
            <?php endif; ?>
            <div>
                <h5 class="mb-0"><?php echo htmlspecialchars($announcement['first_name'] . ' ' . $announcement['last_name']); ?></h5>
                <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($announcement['created_at'])); ?></small>
            </div>
        </div>

        <div class="announcement-content mb-4">
            <?php echo nl2br(htmlspecialchars($announcement['content'])); ?>
        </div>

        <?php if (!empty($files)): ?>
        <div class="announcement-files mb-3">
            <h6><i class="fas fa-paperclip mr-2"></i>Attachments</h6>
            <div class="list-group">
                <?php foreach ($files as $file): ?>
                <?php
                // Determine file icon based on file extension
                $fileExtension = strtolower(pathinfo($file['file_name'], PATHINFO_EXTENSION));
                $fileIcon = 'file';

                // Set icon based on file type
                switch ($fileExtension) {
                    case 'pdf':
                        $fileIcon = 'file-pdf';
                        break;
                    case 'doc':
                    case 'docx':
                        $fileIcon = 'file-word';
                        break;
                    case 'xls':
                    case 'xlsx':
                        $fileIcon = 'file-excel';
                        break;
                    case 'ppt':
                    case 'pptx':
                        $fileIcon = 'file-powerpoint';
                        break;
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                    case 'gif':
                        $fileIcon = 'file-image';
                        break;
                    case 'zip':
                    case 'rar':
                        $fileIcon = 'file-archive';
                        break;
                    case 'txt':
                        $fileIcon = 'file-alt';
                        break;
                }
                ?>
                <a href="universal_file_viewer.php?file=<?php echo urlencode($file['file_path']); ?>" class="list-group-item list-group-item-action">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-<?php echo $fileIcon; ?> mr-3"></i>
                        <div>
                            <div><?php echo htmlspecialchars($file['file_name']); ?></div>
                            <small class="text-muted"><?php echo formatFileSize($file['file_size']); ?></small>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isTeacher() && ($announcement['created_by'] == $_SESSION['user_id'] || $course['created_by'] == $_SESSION['user_id'] || isAdmin())): ?>
        <div class="announcement-actions text-right">
            <a href="announcement_edit.php?id=<?php echo $announcementId; ?>" class="btn" style="min-width: 100px; border: 2px solid white; color: #333; background-color: transparent;">
                <i class="fas fa-edit mr-1"></i> Edit
            </a>
            <a href="announcement_delete.php?id=<?php echo $announcementId; ?>" class="btn" style="min-width: 100px; border: 2px solid #db4437; color: #333; background-color: transparent;"
               onclick="return confirm('Are you sure you want to delete this announcement?');">
                <i class="fas fa-trash-alt mr-1"></i> Delete
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Comments section -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-comments mr-2"></i>Comments</h5>
    </div>
    <div class="card-body">
        <!-- Add comment form -->
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcementId); ?>" method="post" class="mb-4">
            <input type="hidden" name="action" value="add_comment">
            <div class="form-group">
                <textarea name="comment_content" class="form-control <?php echo (!empty($comment_err)) ? 'is-invalid' : ''; ?>" rows="3" placeholder="Add a comment..."><?php echo $comment_content; ?></textarea>
                <span class="invalid-feedback"><?php echo $comment_err; ?></span>
            </div>
            <button type="submit" class="btn btn-primary">Post Comment</button>
        </form>

        <!-- Comments list -->
        <?php if (count($comments) > 0): ?>
            <?php foreach ($comments as $comment): ?>
                <div class="comment mb-4" id="comment-<?php echo $comment['comment_id']; ?>">
                    <div class="d-flex">
                        <?php
                        // Get user profile picture
                        $profilePic = '';
                        try {
                            $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                            $stmt->bindParam(':userId', $comment['user_id']);
                            $stmt->execute();
                            if ($stmt->rowCount() > 0) {
                                $userData = $stmt->fetch();
                                if (!empty($userData['profile_picture'])) {
                                    $profilePic = $userData['profile_picture'];
                                }
                            }
                        } catch (PDOException $e) {
                            // Silently fail and use default avatar
                        }

                        if (!empty($profilePic)):
                        ?>
                            <img src="<?php echo htmlspecialchars($profilePic); ?>" class="rounded-circle mr-3" width="40" height="40" alt="User avatar" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="user-avatar mr-3">
                                <?php echo strtoupper(substr($comment['first_name'], 0, 1)); ?>
                            </div>
                        <?php endif; ?>

                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?php echo htmlspecialchars($comment['first_name'] . ' ' . $comment['last_name']); ?></h6>
                                <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($comment['created_at'])); ?></small>
                            </div>
                            <div class="comment-content mt-2">
                                <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                            </div>

                            <!-- Comment actions -->
                            <div class="comment-actions mt-2">
                                <button class="btn btn-sm btn-link reply-toggle" data-comment-id="<?php echo $comment['comment_id']; ?>">
                                    <i class="fas fa-reply"></i> Reply
                                </button>

                                <?php if ($comment['user_id'] == $_SESSION['user_id'] || isAdmin()): ?>
                                <button class="btn btn-sm btn-link edit-toggle" data-item-id="<?php echo $comment['comment_id']; ?>" data-item-type="comment" data-content="<?php echo htmlspecialchars($comment['content']); ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-link text-danger delete-toggle" data-item-id="<?php echo $comment['comment_id']; ?>" data-item-type="comment">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                <?php endif; ?>
                            </div>

                            <!-- Reply form (hidden by default) -->
                            <div class="reply-form mt-3" id="reply-form-<?php echo $comment['comment_id']; ?>" style="display: none;">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcementId); ?>" method="post">
                                    <input type="hidden" name="action" value="add_reply">
                                    <input type="hidden" name="comment_id" value="<?php echo $comment['comment_id']; ?>">
                                    <div class="form-group">
                                        <textarea name="reply_content" class="form-control" rows="2" placeholder="Write a reply..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-sm btn-primary">Post Reply</button>
                                    <button type="button" class="btn btn-sm btn-secondary reply-cancel" data-comment-id="<?php echo $comment['comment_id']; ?>">Cancel</button>
                                </form>
                            </div>

                            <!-- Replies -->
                            <?php if (!empty($comment['replies'])): ?>
                                <div class="replies mt-3 ml-4">
                                    <?php foreach ($comment['replies'] as $reply): ?>
                                        <div class="reply mb-3" id="reply-<?php echo $reply['reply_id']; ?>">
                                            <div class="d-flex">
                                                <?php
                                                // Get user profile picture
                                                $replyProfilePic = '';
                                                try {
                                                    $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                                                    $stmt->bindParam(':userId', $reply['user_id']);
                                                    $stmt->execute();
                                                    if ($stmt->rowCount() > 0) {
                                                        $userData = $stmt->fetch();
                                                        if (!empty($userData['profile_picture'])) {
                                                            $replyProfilePic = $userData['profile_picture'];
                                                        }
                                                    }
                                                } catch (PDOException $e) {
                                                    // Silently fail and use default avatar
                                                }

                                                if (!empty($replyProfilePic)):
                                                ?>
                                                    <img src="<?php echo htmlspecialchars($replyProfilePic); ?>" class="rounded-circle mr-2" width="30" height="30" alt="User avatar" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="user-avatar user-avatar-sm mr-2">
                                                        <?php echo strtoupper(substr($reply['first_name'], 0, 1)); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($reply['first_name'] . ' ' . $reply['last_name']); ?></h6>
                                                        <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($reply['created_at'])); ?></small>
                                                    </div>
                                                    <div class="reply-content mt-1">
                                                        <?php echo nl2br(htmlspecialchars($reply['content'])); ?>
                                                    </div>

                                                    <!-- Reply actions -->
                                                    <?php if ($reply['user_id'] == $_SESSION['user_id'] || isAdmin()): ?>
                                                    <div class="reply-actions mt-1">
                                                        <button class="btn btn-sm btn-link edit-toggle" data-item-id="<?php echo $reply['reply_id']; ?>" data-item-type="reply" data-content="<?php echo htmlspecialchars($reply['content']); ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <button class="btn btn-sm btn-link text-danger delete-toggle" data-item-id="<?php echo $reply['reply_id']; ?>" data-item-type="reply">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="text-muted mb-0">No comments yet. Be the first to comment!</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Edit Comment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcementId); ?>" method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_comment">
                    <input type="hidden" name="item_id" id="edit_item_id">
                    <input type="hidden" name="item_type" id="edit_item_type">
                    <div class="form-group">
                        <label for="edit_content">Content</label>
                        <textarea name="edit_content" id="edit_content" class="form-control" rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this item? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcementId); ?>" method="post">
                    <input type="hidden" name="action" value="delete_item">
                    <input type="hidden" name="item_id" id="delete_item_id">
                    <input type="hidden" name="item_type" id="delete_item_type">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for comments -->
<style>
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.user-avatar-sm {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
}

.comment {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.comment:last-child {
    border-bottom: none;
}

.comment-content, .reply-content {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
}

.replies {
    border-left: 2px solid #dee2e6;
    padding-left: 1rem;
}
</style>

<!-- JavaScript for comment functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reply toggle functionality
    document.querySelectorAll('.reply-toggle').forEach(function(button) {
        button.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const replyForm = document.getElementById('reply-form-' + commentId);
            replyForm.style.display = replyForm.style.display === 'none' ? 'block' : 'none';
        });
    });

    // Reply cancel functionality
    document.querySelectorAll('.reply-cancel').forEach(function(button) {
        button.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const replyForm = document.getElementById('reply-form-' + commentId);
            replyForm.style.display = 'none';
        });
    });

    // Edit functionality
    document.querySelectorAll('.edit-toggle').forEach(function(button) {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            const content = this.getAttribute('data-content');

            document.getElementById('edit_item_id').value = itemId;
            document.getElementById('edit_item_type').value = itemType;
            document.getElementById('edit_content').value = content;
            document.getElementById('editModalLabel').textContent = 'Edit ' + (itemType.charAt(0).toUpperCase() + itemType.slice(1));

            $('#editModal').modal('show');
        });
    });

    // Delete functionality
    document.querySelectorAll('.delete-toggle').forEach(function(button) {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');

            document.getElementById('delete_item_id').value = itemId;
            document.getElementById('delete_item_type').value = itemType;
            document.getElementById('deleteModalLabel').textContent = 'Delete ' + (itemType.charAt(0).toUpperCase() + itemType.slice(1));

            $('#deleteModal').modal('show');
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';

/**
 * Helper function to format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
