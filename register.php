<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Create a mysqli connection for compatibility with the existing code
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Initialize variables
$first_name = $last_name = $username = $email = $password = $confirm_password = $gender = $birthday = $phone_number = "";
$first_name_err = $last_name_err = $username_err = $email_err = $password_err = $confirm_password_err = $gender_err = $birthday_err = $phone_number_err = $register_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter your first name.";
    } else {
        $first_name = trim($_POST["first_name"]);
    }

    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter your last name.";
    } else {
        $last_name = trim($_POST["last_name"]);
    }

    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        // Prepare a select statement
        $sql = "SELECT user_id FROM users WHERE username = ?";

        if ($stmt = $conn->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bind_param("s", $param_username);

            // Set parameters
            $param_username = trim($_POST["username"]);

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Store result
                $stmt->store_result();

                if ($stmt->num_rows == 1) {
                    $username_err = "This username is already taken.";
                } else {
                    $username = trim($_POST["username"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            $stmt->close();
        }
    }

    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter your email.";
    } elseif (!filter_var(trim($_POST["email"]), FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email address.";
    } else {
        // Prepare a select statement
        $sql = "SELECT user_id FROM users WHERE email = ?";

        if ($stmt = $conn->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bind_param("s", $param_email);

            // Set parameters
            $param_email = trim($_POST["email"]);

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Store result
                $stmt->store_result();

                if ($stmt->num_rows == 1) {
                    $email_err = "This email is already registered.";
                } else {
                    $email = trim($_POST["email"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            $stmt->close();
        }
    }

    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";
    } elseif (strlen(trim($_POST["password"])) < 6) {
        $password_err = "Password must have at least 6 characters.";
    } else {
        $password = trim($_POST["password"]);
    }

    // Validate confirm password
    if (empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "Please confirm password.";
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if (empty($password_err) && ($password != $confirm_password)) {
            $confirm_password_err = "Password did not match.";
        }
    }

    // Validate gender
    if (empty(trim($_POST["gender"]))) {
        $gender_err = "Please select your gender.";
    } else {
        $gender = trim($_POST["gender"]);
    }

    // Validate birthday
    if (empty($_POST["birth_month"]) || empty($_POST["birth_day"]) || empty($_POST["birth_year"])) {
        $birthday_err = "Please select your complete birthday.";
    } else {
        $birth_month = trim($_POST["birth_month"]);
        $birth_day = trim($_POST["birth_day"]);
        $birth_year = trim($_POST["birth_year"]);

        // Format as YYYY-MM-DD for database storage
        $birthday = sprintf('%04d-%02d-%02d', $birth_year, $birth_month, $birth_day);

        // Check if the date is valid
        $date = date_create_from_format('Y-m-d', $birthday);
        if (!$date || date_format($date, 'Y-m-d') !== $birthday) {
            $birthday_err = "Please enter a valid date.";
        }
    }

    // Validate phone number
    if (empty(trim($_POST["phone_number"]))) {
        $phone_number_err = "Please enter your phone number.";
    } else {
        $phone_number = trim($_POST["phone_number"]);
        // Simple validation for phone number format
        if (!preg_match('/^[0-9+\-\s()]{7,20}$/', $phone_number)) {
            $phone_number_err = "Please enter a valid phone number.";
        }
    }

    // Check input errors before inserting in database
    if (empty($first_name_err) && empty($last_name_err) && empty($username_err) && empty($email_err) && empty($password_err) && empty($confirm_password_err) && empty($gender_err) && empty($birthday_err) && empty($phone_number_err)) {

        // Get the student role ID
        $role_query = "SELECT role_id FROM roles WHERE role_name = 'student'";
        $role_result = $conn->query($role_query);
        $role_row = $role_result->fetch_assoc();
        $student_role_id = $role_row['role_id'];

        // Prepare an insert statement
        $sql = "INSERT INTO users (first_name, last_name, gender, birthday, phone_number, username, email, password, role_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        if ($stmt = $conn->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bind_param("ssssssssii", $param_first_name, $param_last_name, $param_gender, $param_birthday, $param_phone_number, $param_username, $param_email, $param_password, $param_role_id, $param_is_active);

            // Set parameters
            $param_first_name = $first_name;
            $param_last_name = $last_name;
            $param_gender = $gender;
            $param_birthday = $birthday;
            $param_phone_number = $phone_number;
            $param_username = $username;
            $param_email = $email;
            $param_password = password_hash($password, PASSWORD_DEFAULT); // Creates a password hash
            $param_role_id = $student_role_id; // Only students can register
            $param_is_active = 1; // Account is active by default

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Set success message and redirect to login page
                $_SESSION['success_message'] = "Registration successful! You can now log in.";
                header("location: login.php");
                exit;
            } else {
                $register_err = "Something went wrong. Error: " . $stmt->error;
            }

            // Close statement
            $stmt->close();
        } else {
            $register_err = "Something went wrong. Error: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/AdobeStock_271791778.jpeg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .register-container {
            width: 448px;
            padding: 48px 40px 36px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .register-logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .register-logo h1 {
            color: var(--primary-color);
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            font-size: 28px;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .register-logo h1 i {
            margin-right: 12px;
            font-size: 32px;
        }

        .register-logo p {
            color: var(--text-secondary);
            margin-top: 12px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-control {
            height: 56px;
            padding: 16px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.25);
            transform: translateY(-2px);
        }

        .btn-register {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .btn-register:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
        }

        .btn-register:active {
            transform: translateY(1px);
        }

        .alert {
            margin-bottom: 24px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .text-center a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .text-center a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .back-to-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-family: 'Google Sans', sans-serif;
            font-size: 16px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .back-to-home i {
            margin-right: 8px;
        }

        .back-to-home:hover {
            transform: translateX(-5px);
            color: rgba(255, 255, 255, 0.8);
        }

        .row {
            margin-left: -10px;
            margin-right: -10px;
        }

        .col-md-6 {
            padding-left: 10px;
            padding-right: 10px;
        }

        .invalid-feedback {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
            display: block;
        }

        /* Password toggle styles */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        /* Date input styling */
        input[type="date"] {
            padding-right: 10px;
        }

        /* Select styling */
        select.form-control {
            height: 56px;
            padding: 16px;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 16px;
        }

        /* Helper text styling */
        .text-muted {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }


    </style>
</head>
<body>
    <a href="landing.php" class="back-to-home">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>
    <div class="register-container">
        <div class="register-logo">
            <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
            <p>Create your account</p>
        </div>

        <?php
        if (!empty($register_err)) {
            echo '<div class="alert alert-danger">' . $register_err . '</div>';
        }
        ?>

        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <input type="text" id="first_name" name="first_name" placeholder="First Name" class="form-control <?php echo (!empty($first_name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $first_name; ?>">
                <span class="invalid-feedback"><?php echo $first_name_err; ?></span>
            </div>
            <div class="form-group">
                <input type="text" id="last_name" name="last_name" placeholder="Last Name" class="form-control <?php echo (!empty($last_name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $last_name; ?>">
                <span class="invalid-feedback"><?php echo $last_name_err; ?></span>
            </div>
            <div class="form-group">
                <input type="text" id="username" name="username" placeholder="Username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $username; ?>">
                <span class="invalid-feedback"><?php echo $username_err; ?></span>
            </div>
            <div class="form-group">
                <input type="email" id="email" name="email" placeholder="Email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>">
                <span class="invalid-feedback"><?php echo $email_err; ?></span>
            </div>
            <div class="form-group">
                <select id="gender" name="gender" class="form-control <?php echo (!empty($gender_err)) ? 'is-invalid' : ''; ?>">
                    <option value="" <?php echo empty($gender) ? 'selected' : ''; ?>>Select Gender</option>
                    <option value="male" <?php echo ($gender == 'male') ? 'selected' : ''; ?>>Male</option>
                    <option value="female" <?php echo ($gender == 'female') ? 'selected' : ''; ?>>Female</option>
                    <option value="other" <?php echo ($gender == 'other') ? 'selected' : ''; ?>>Other</option>
                </select>
                <span class="invalid-feedback"><?php echo $gender_err; ?></span>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-4">
                        <select id="birth_month" name="birth_month" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Month</option>
                            <?php
                            $months = [
                                1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                                5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                                9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                            ];

                            $selected_month = isset($_POST['birth_month']) ? $_POST['birth_month'] : '';
                            if (empty($selected_month) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_month = (int)$date_parts[1];
                                }
                            }

                            foreach ($months as $num => $name) {
                                echo '<option value="' . $num . '"' . ($selected_month == $num ? ' selected' : '') . '>' . $name . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="birth_day" name="birth_day" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Day</option>
                            <?php
                            $selected_day = isset($_POST['birth_day']) ? $_POST['birth_day'] : '';
                            if (empty($selected_day) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_day = (int)$date_parts[2];
                                }
                            }

                            for ($i = 1; $i <= 31; $i++) {
                                echo '<option value="' . $i . '"' . ($selected_day == $i ? ' selected' : '') . '>' . $i . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="birth_year" name="birth_year" class="form-control <?php echo (!empty($birthday_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">Year</option>
                            <?php
                            $current_year = (int)date('Y');
                            $selected_year = isset($_POST['birth_year']) ? $_POST['birth_year'] : '';
                            if (empty($selected_year) && !empty($birthday)) {
                                $date_parts = explode('-', $birthday);
                                if (count($date_parts) == 3) {
                                    $selected_year = (int)$date_parts[0];
                                }
                            }

                            for ($i = $current_year; $i >= $current_year - 100; $i--) {
                                echo '<option value="' . $i . '"' . ($selected_year == $i ? ' selected' : '') . '>' . $i . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <span class="invalid-feedback"><?php echo $birthday_err; ?></span>
            </div>
            <div class="form-group">
                <input type="text" id="phone_number" name="phone_number" placeholder="Phone Number" class="form-control <?php echo (!empty($phone_number_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $phone_number; ?>">
                <span class="invalid-feedback"><?php echo $phone_number_err; ?></span>
            </div>
            <div class="form-group">
                <div class="password-container">
                    <input type="password" name="password" id="password" placeholder="Password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>">
                    <span class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password-eye"></i>
                    </span>
                </div>
                <span class="invalid-feedback"><?php echo $password_err; ?></span>
            </div>
            <div class="form-group">
                <div class="password-container">
                    <input type="password" name="confirm_password" id="confirm_password" placeholder="Confirm Password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>">
                    <span class="password-toggle" onclick="togglePassword('confirm_password')">
                        <i class="fas fa-eye" id="confirm_password-eye"></i>
                    </span>
                </div>
                <span class="invalid-feedback"><?php echo $confirm_password_err; ?></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-register">Register</button>
            </div>
            <div class="text-center mt-3">
                <p>Already have an account? <a href="login.php" style="color: var(--primary-color); text-decoration: none;">Sign in</a></p>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <footer style="position: fixed; bottom: 0; width: 100%; background-color: rgba(255, 255, 255, 0.9); border-top: 1px solid #e9ecef; padding: 10px 0; text-align: center; backdrop-filter: blur(10px);">
        <div class="container">
            <span style="color: #6c757d; font-size: 0.9rem;">
                &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.
            </span>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const eyeIcon = document.getElementById(inputId + '-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }


    </script>
</body>
</html>
