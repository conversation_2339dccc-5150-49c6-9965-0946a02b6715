-- Enrollment Requests Table
CREATE TABLE IF NOT EXISTS enrollment_requests (
    request_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    processed_by INT,
    processed_date TIMESTAMP NULL,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_request (user_id, course_id)
);
