<?php
// Set headers to prevent caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Get the activity ID from the query string
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Redirect to the activity edit page with a timestamp to force refresh
header("Location: activity_edit.php?id=$id&nocache=" . time());
exit;
?>
