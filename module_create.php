<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher
if (!isTeacher()) {
    $_SESSION['error'] = "Only teachers can create modules.";
    header("location: index.php");
    exit;
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    $_SESSION['error'] = "Course ID is required.";
    header("location: index.php");
    exit;
}

$courseId = intval($_GET['course_id']);

// Get course details
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized for this course
if ($course['created_by'] != $_SESSION['user_id'] && !isInstructor($_SESSION['user_id'], $courseId) && !isAdmin()) {
    $_SESSION['error'] = "You are not authorized to create modules for this course.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Initialize variables
$title = $description = "";
$title_err = $description_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate title
    if (empty(trim($_POST["title"]))) {
        $title_err = "Please enter a title.";
    } else {
        $title = trim($_POST["title"]);
    }
    
    // Validate description (optional)
    $description = trim($_POST["description"]);
    
    // Check input errors before creating the module
    if (empty($title_err)) {
        // Create the module
        $result = createModule($courseId, $title, $description);
        
        if (is_numeric($result)) {
            // Module created successfully
            $_SESSION['success'] = "Module created successfully.";
            header("location: course_view_full.php?id=$courseId&tab=classwork");
            exit;
        } else {
            // Error creating module
            $_SESSION['error'] = $result;
        }
    }
}

// Set page title
$page_title = "Create Module";

// Include header
require_once 'includes/header.php';
?>

<!-- Back button -->
<div class="mb-3">
    <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Course
    </a>
</div>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create Module</h1>
</div>

<!-- Course info -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle mr-3 fa-2x"></i>
        <div>
            <h5 class="alert-heading mb-1">Course: <?php echo htmlspecialchars($course['title']); ?></h5>
            <p class="mb-0">Create a module to organize your course content.</p>
        </div>
    </div>
</div>

<!-- Module form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?course_id=' . $courseId); ?>" method="post">
            <div class="form-group">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" class="form-control <?php echo (!empty($title_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $title; ?>">
                <span class="invalid-feedback"><?php echo $title_err; ?></span>
                <small class="form-text text-muted">Examples: "Week 1", "Introduction to Programming", "Final Project"</small>
            </div>
            <div class="form-group">
                <label for="description">Description (optional)</label>
                <textarea name="description" id="description" rows="3" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>"><?php echo $description; ?></textarea>
                <span class="invalid-feedback"><?php echo $description_err; ?></span>
            </div>
            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">Create Module</button>
                <a href="course_view_full.php?id=<?php echo $courseId; ?>&tab=classwork" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
