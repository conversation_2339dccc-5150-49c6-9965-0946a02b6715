<?php
// Include configuration file
require_once 'includes/config.php';

echo "<h1>Creating Activity Answers Table</h1>";

try {
    // Check if activity_answers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
    $activityAnswersExists = $stmt->rowCount() > 0;
    
    if (!$activityAnswersExists) {
        echo "<p>Creating activity_answers table...</p>";
        
        // Create activity_answers table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_answers` (
            `answer_id` int(11) NOT NULL AUTO_INCREMENT,
            `submission_id` int(11) NOT NULL,
            `question_id` int(11) NOT NULL,
            `answer_text` text DEFAULT NULL,
            `selected_option_id` int(11) DEFAULT NULL,
            `is_correct` tinyint(1) DEFAULT NULL,
            `points_earned` decimal(10,2) DEFAULT NULL,
            PRIMARY KEY (`answer_id`),
            <PERSON>EY `submission_id` (`submission_id`),
            <PERSON><PERSON><PERSON> `question_id` (`question_id`),
            <PERSON><PERSON>Y `selected_option_id` (`selected_option_id`),
            CONSTRAINT `activity_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `activity_submissions` (`submission_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $pdo->exec($sql);
        echo "<p>activity_answers table created successfully.</p>";
    } else {
        echo "<p>activity_answers table already exists.</p>";
    }
    
    echo "<p>You can now try submitting the activity again.</p>";
    echo "<p><a href='activity_submit.php?id=6' class='btn btn-primary'>Go to Activity Submit</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>
