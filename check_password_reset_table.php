<?php
// Include configuration file
require_once 'includes/config.php';

// Get the database connection
global $pdo;

// Check if the password_reset table exists
try {
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'password_reset'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "The password_reset table does not exist. Creating it now...<br>";
        
        // Create the password_reset table
        $sql = "CREATE TABLE password_reset (
            reset_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            reset_code VARCHAR(10) NOT NULL,
            expiry_time DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )";
        
        $pdo->exec($sql);
        echo "password_reset table created successfully!<br>";
    } else {
        echo "The password_reset table already exists.<br>";
    }
    
    // Check if APP_EMAIL constant is defined
    if (!defined('APP_EMAIL')) {
        echo "APP_EMAIL constant is not defined in config.php. Please add it.<br>";
    } else {
        echo "APP_EMAIL constant is defined as: " . APP_EMAIL . "<br>";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
