<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/email_helper.php';

// Check if the user is an admin
$isAdmin = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['role_id'])) {
    $isAdmin = ($_SESSION['role_id'] == 1);
}

// Initialize variables
$error_msg = "";
$success_msg = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    if ($_POST['action'] == 'clear_log' && $isAdmin) {
        $logFile = __DIR__ . '/logs/email_log.txt';
        if (file_exists($logFile)) {
            if (unlink($logFile)) {
                $success_msg = "Email log has been cleared successfully.";
            } else {
                $error_msg = "Failed to clear email log.";
            }
        } else {
            $error_msg = "Email log file does not exist.";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Codes - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            padding: 20px;
        }
        .code-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .verification-code {
            font-family: monospace;
            font-size: 1.2em;
            font-weight: bold;
            letter-spacing: 3px;
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
        }
        .email-log {
            max-height: 500px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-link"><i class="fas fa-arrow-left"></i> Back to Home</a>
        
        <h1><i class="fas fa-key"></i> Verification Codes</h1>
        <p class="text-muted">This page is for development purposes only. It shows all verification codes sent to users.</p>
        
        <?php if (!empty($error_msg)): ?>
            <div class="alert alert-danger"><?php echo $error_msg; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success_msg)): ?>
            <div class="alert alert-success"><?php echo $success_msg; ?></div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> Database Verification Codes</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get verification codes from the database
                        global $pdo;
                        try {
                            $stmt = $pdo->query("
                                SELECT pr.*, u.username, u.email 
                                FROM password_reset pr
                                JOIN users u ON pr.user_id = u.user_id
                                ORDER BY pr.expiry_time DESC
                            ");
                            
                            if ($stmt->rowCount() > 0) {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-striped">';
                                echo '<thead><tr><th>Username</th><th>Email</th><th>Code</th><th>Expires</th></tr></thead>';
                                echo '<tbody>';
                                
                                while ($row = $stmt->fetch()) {
                                    $expired = strtotime($row['expiry_time']) < time();
                                    $rowClass = $expired ? 'text-muted' : '';
                                    
                                    echo '<tr class="' . $rowClass . '">';
                                    echo '<td>' . htmlspecialchars($row['username']) . '</td>';
                                    echo '<td>' . htmlspecialchars(maskEmail($row['email'])) . '</td>';
                                    echo '<td><span class="verification-code">' . $row['reset_code'] . '</span></td>';
                                    echo '<td>' . $row['expiry_time'] . ($expired ? ' <span class="badge badge-secondary">Expired</span>' : '') . '</td>';
                                    echo '</tr>';
                                }
                                
                                echo '</tbody></table>';
                                echo '</div>';
                            } else {
                                echo '<p class="text-muted">No verification codes found in the database.</p>';
                            }
                        } catch (PDOException $e) {
                            echo '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Session Data</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_SESSION['dev_verification_code'])): ?>
                            <div class="code-container">
                                <p><strong>Current Verification Code:</strong></p>
                                <p class="verification-code"><?php echo $_SESSION['dev_verification_code']; ?></p>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No verification code in session.</p>
                        <?php endif; ?>
                        
                        <?php if (isset($_SESSION['recovery_email'])): ?>
                            <p><strong>Recovery Email:</strong> <?php echo htmlspecialchars($_SESSION['recovery_email']); ?> (<?php echo htmlspecialchars(maskEmail($_SESSION['recovery_email'])); ?>)</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-envelope"></i> Email Log</h5>
                        <?php if ($isAdmin): ?>
                            <form method="post" onsubmit="return confirm('Are you sure you want to clear the email log?');">
                                <input type="hidden" name="action" value="clear_log">
                                <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> Clear Log</button>
                            </form>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php
                        $logFile = __DIR__ . '/logs/email_log.txt';
                        if (file_exists($logFile)) {
                            $logContent = file_get_contents($logFile);
                            if (!empty($logContent)) {
                                echo '<div class="email-log">' . htmlspecialchars($logContent) . '</div>';
                            } else {
                                echo '<p class="text-muted">Email log is empty.</p>';
                            }
                        } else {
                            echo '<p class="text-muted">Email log file does not exist.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
