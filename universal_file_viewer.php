<?php
// Include configuration file
require_once 'includes/config.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if file path is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    header("HTTP/1.0 404 Not Found");
    echo "File not found.";
    exit;
}

$filePath = urldecode($_GET['file']);

// Security check - make sure the file is within the uploads directory
if (strpos($filePath, 'uploads/') !== 0) {
    header("HTTP/1.0 403 Forbidden");
    echo "Access denied.";
    exit;
}

// Set page title
$page_title = "File Viewer";

// Include header
require_once 'includes/header.php';

// Check if file exists and try different path combinations
$foundFile = false;
$possiblePaths = [
    $filePath,
    './' . $filePath,
    '../' . $filePath,
    dirname(__FILE__) . '/' . $filePath,
    $_SERVER['DOCUMENT_ROOT'] . '/' . $filePath
];

// If the path doesn't start with 'uploads/', try to find it
if (strpos($filePath, 'uploads/') !== 0) {
    // Try to extract the uploads part from the path
    if (preg_match('/.*?(uploads\/.*)/', $filePath, $matches)) {
        $possiblePaths[] = $matches[1];
        $possiblePaths[] = './' . $matches[1];
        $possiblePaths[] = dirname(__FILE__) . '/' . $matches[1];
        $possiblePaths[] = $_SERVER['DOCUMENT_ROOT'] . '/' . $matches[1];
    }
}

// Add more possible paths for announcements
if (strpos($filePath, 'announcements') !== false) {
    // Try different combinations for announcement files
    $possiblePaths[] = str_replace('uploads/announcements', './uploads/announcements', $filePath);
    $possiblePaths[] = str_replace('uploads/announcements', '../uploads/announcements', $filePath);
    
    // Extract announcement ID from path
    if (preg_match('/announcements\/(\d+)\//', $filePath, $matches)) {
        $announcementId = $matches[1];
        $possiblePaths[] = "uploads/announcements/$announcementId/" . basename($filePath);
        $possiblePaths[] = "./uploads/announcements/$announcementId/" . basename($filePath);
        $possiblePaths[] = "../uploads/announcements/$announcementId/" . basename($filePath);
    }
}

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        $foundFile = true;
        break;
    }
}

// Get file information
if ($foundFile) {
    $fileInfo = pathinfo($filePath);
    $fileName = $fileInfo['basename'];
    $fileExtension = strtolower($fileInfo['extension'] ?? '');
    $fileSize = filesize($filePath);
    
    // Get MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $filePath);
    finfo_close($finfo);
    
    // Determine file type for display
    $fileType = 'unknown';
    $fileIcon = 'file';
    
    // Set file type and icon based on extension
    switch ($fileExtension) {
        case 'pdf':
            $fileType = 'pdf';
            $fileIcon = 'file-pdf';
            break;
        case 'doc':
        case 'docx':
            $fileType = 'word';
            $fileIcon = 'file-word';
            break;
        case 'xls':
        case 'xlsx':
            $fileType = 'excel';
            $fileIcon = 'file-excel';
            break;
        case 'ppt':
        case 'pptx':
            $fileType = 'powerpoint';
            $fileIcon = 'file-powerpoint';
            break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'svg':
            $fileType = 'image';
            $fileIcon = 'file-image';
            break;
        case 'txt':
        case 'log':
        case 'md':
            $fileType = 'text';
            $fileIcon = 'file-alt';
            break;
        case 'html':
        case 'htm':
        case 'css':
        case 'js':
        case 'json':
        case 'xml':
            $fileType = 'code';
            $fileIcon = 'file-code';
            break;
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
            $fileType = 'archive';
            $fileIcon = 'file-archive';
            break;
        case 'mp3':
        case 'wav':
        case 'ogg':
            $fileType = 'audio';
            $fileIcon = 'file-audio';
            break;
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
            $fileType = 'video';
            $fileIcon = 'file-video';
            break;
    }
}

// Format file size for display
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<div class="container">
    <div class="mb-3">
        <a href="javascript:history.back()" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left mr-2"></i> Back
        </a>
    </div>
    
    <?php if (!$foundFile): ?>
        <div class="alert alert-danger">
            <h4 class="alert-heading">File Not Found</h4>
            <p>The requested file could not be found. Please check the file path and try again.</p>
            <hr>
            <p class="mb-0">Path attempted: <?php echo htmlspecialchars($filePath); ?></p>
        </div>
    <?php else: ?>
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo $fileIcon; ?> mr-2"></i> <?php echo htmlspecialchars($fileName); ?>
                </h5>
                <div>
                    <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-sm btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt mr-1"></i> Open in New Tab
                    </a>
                    <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>&download=true" class="btn btn-sm btn-secondary">
                        <i class="fas fa-download mr-1"></i> Download
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="file-info mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>File Name:</strong> <?php echo htmlspecialchars($fileName); ?></p>
                            <p><strong>File Type:</strong> <?php echo strtoupper($fileExtension); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>File Size:</strong> <?php echo formatFileSize($fileSize); ?></p>
                            <p><strong>MIME Type:</strong> <?php echo $mimeType; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="file-preview">
                    <?php if ($fileType == 'image'): ?>
                        <div class="text-center">
                            <img src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="img-fluid" alt="<?php echo htmlspecialchars($fileName); ?>">
                        </div>
                    <?php elseif ($fileType == 'pdf'): ?>
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" allowfullscreen></iframe>
                        </div>
                    <?php elseif ($fileType == 'text' || $fileType == 'code'): ?>
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" src="file_viewer.php?file=<?php echo urlencode($filePath); ?>"></iframe>
                        </div>
                    <?php elseif ($fileType == 'video'): ?>
                        <div class="embed-responsive embed-responsive-16by9">
                            <video class="embed-responsive-item" controls>
                                <source src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" type="<?php echo $mimeType; ?>">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    <?php elseif ($fileType == 'audio'): ?>
                        <div class="text-center">
                            <audio controls>
                                <source src="file_viewer.php?file=<?php echo urlencode($filePath); ?>" type="<?php echo $mimeType; ?>">
                                Your browser does not support the audio tag.
                            </audio>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <p>Preview not available for this file type.</p>
                            <div class="mt-4">
                                <div class="d-flex justify-content-center mt-3">
                                    <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>&download=true" class="btn btn-primary mx-2">
                                        <i class="fas fa-download mr-2"></i> Download File
                                    </a>
                                    <a href="file_viewer.php?file=<?php echo urlencode($filePath); ?>" class="btn btn-outline-primary mx-2" target="_blank">
                                        <i class="fas fa-eye mr-2"></i> Try Direct View
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
