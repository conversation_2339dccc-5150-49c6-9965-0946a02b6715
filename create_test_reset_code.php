<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/email_helper.php';

// Get the database connection
global $pdo;

try {
    // Get the student user
    $username = "student";
    $stmt = $pdo->prepare("SELECT user_id, email FROM users WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch();
        $userId = $user['user_id'];
        $email = $user['email'];
        
        // Generate a verification code
        $verificationCode = generateVerificationCode();
        
        // Set expiry time (1 hour from now)
        $expiryTime = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // Check if a reset code already exists for this user
        $stmt = $pdo->prepare("SELECT * FROM password_reset WHERE user_id = :userId");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Update existing record
            $stmt = $pdo->prepare("UPDATE password_reset SET reset_code = :code, expiry_time = :expiryTime WHERE user_id = :userId");
        } else {
            // Insert new record
            $stmt = $pdo->prepare("INSERT INTO password_reset (user_id, reset_code, expiry_time) VALUES (:userId, :code, :expiryTime)");
        }
        
        $stmt->bindParam(':userId', $userId);
        $stmt->bindParam(':code', $verificationCode);
        $stmt->bindParam(':expiryTime', $expiryTime);
        $stmt->execute();
        
        echo "<h2>Test Reset Code Created</h2>";
        echo "<p>User ID: " . $userId . "</p>";
        echo "<p>Username: " . $username . "</p>";
        echo "<p>Email: " . $email . "</p>";
        echo "<p>Verification Code: " . $verificationCode . "</p>";
        echo "<p>Expiry Time: " . $expiryTime . "</p>";
        
        // Set session variable for testing
        $_SESSION['recovery_email'] = $email;
        
        echo "<p>Session variable 'recovery_email' set to: " . $_SESSION['recovery_email'] . "</p>";
        echo "<p><a href='verify_code.php'>Go to Verify Code Page</a></p>";
        
    } else {
        echo "<p>Student user not found. Please run create_test_user.php first.</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
}
?>
