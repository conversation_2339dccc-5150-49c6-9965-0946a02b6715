<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if user is authorized to post announcements
if (!isAdmin() && !isTeacher()) {
    $_SESSION['error'] = "You are not authorized to post announcements.";
    header("location: index.php");
    exit;
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate course ID
    if (!isset($_POST['course_id']) || empty($_POST['course_id'])) {
        $_SESSION['error'] = "Invalid course ID.";
        header("location: index.php");
        exit;
    }
    
    $courseId = intval($_POST['course_id']);
    
    // Validate content
    if (!isset($_POST['content']) || empty(trim($_POST['content']))) {
        $_SESSION['error'] = "Announcement content cannot be empty.";
        header("location: course_view.php?id=" . $courseId);
        exit;
    }
    
    $content = trim($_POST['content']);
    
    // Check if user has access to this course
    $course = getCourseById($courseId);
    
    if (is_string($course)) {
        // Course not found or error occurred
        $_SESSION['error'] = $course;
        header("location: index.php");
        exit;
    }
    
    if (!isAdmin() && $course['created_by'] != $_SESSION['user_id']) {
        $_SESSION['error'] = "You are not authorized to post announcements to this course.";
        header("location: index.php");
        exit;
    }
    
    // In a real implementation, you would save the announcement to the database
    // For now, we'll just redirect back to the course view with a success message
    
    $_SESSION['success'] = "Announcement posted successfully!";
    header("location: course_view.php?id=" . $courseId);
    exit;
} else {
    // If not a POST request, redirect to home page
    header("location: index.php");
    exit;
}
?>
