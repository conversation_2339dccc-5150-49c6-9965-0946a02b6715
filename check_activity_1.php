<?php
// Include configuration file
require_once 'includes/config.php';

// Set the activity ID
$activityId = 1;

try {
    // Get activity details
    $stmt = $pdo->prepare("SELECT activity_id, title, activity_type FROM activities WHERE activity_id = :activityId");
    $stmt->bindParam(':activityId', $activityId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $activity = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h2>Activity Details</h2>";
        echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
        echo "<p><strong>Activity Type:</strong> " . htmlspecialchars($activity['activity_type']) . "</p>";
        
        // Show a link to update the activity type
        echo "<p><a href='fix_this_activity.php?id=" . $activity['activity_id'] . "'>Fix this Activity</a></p>";
    } else {
        echo "<p>Activity not found.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
