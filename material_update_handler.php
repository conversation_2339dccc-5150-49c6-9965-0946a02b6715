<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/content_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is a teacher or admin
if (!isTeacher() && !isAdmin()) {
    $_SESSION['error'] = "You do not have permission to edit materials.";
    header("location: index.php");
    exit;
}

// Check if material ID is provided
if (!isset($_POST['material_id']) || empty($_POST['material_id'])) {
    $_SESSION['error'] = "Material ID is required.";
    header("location: index.php");
    exit;
}

$materialId = intval($_POST['material_id']);

// Get material details
$material = getMaterialById($materialId);

// Check if material exists
if (is_string($material)) {
    $_SESSION['error'] = $material;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $material['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user is authorized to edit this material
if (!isAdmin()) {
    $isInstructor = isInstructor($_SESSION['user_id'], $courseId);
    if ($course['created_by'] != $_SESSION['user_id'] && !$isInstructor) {
        $_SESSION['error'] = "You are not authorized to edit materials for this course.";
        header("location: course_view_full.php?id=$courseId");
        exit;
    }
}

// Process form data
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug information
    error_log("POST data received in material_update_handler.php: " . print_r($_POST, true));

    // Get form data
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $isPublished = isset($_POST['is_published']) ? true : false;

    // Validate form data
    $errors = [];

    if (empty($title)) {
        $errors[] = "Title is required.";
    }

    if (empty($content)) {
        $errors[] = "Content is required.";
    }

    // Check for errors
    if (empty($errors)) {
        // Update the material (which is an activity of type 'material')
        $result = updateActivity($materialId, $title, $content, 0, null, $isPublished, false, null);

        if ($result === true) {
            // Handle file uploads if any
            if (isset($_FILES['files']) && $_FILES['files']['name'][0] != '') {
                // Get allowed file types from settings
                $allowedTypes = getAllowedFileTypes();

                // Get max file size from settings
                $maxFileSize = getMaxFileUploadSize();
                $maxFileSizeMB = $maxFileSize / (1024 * 1024);

                // Create uploads directory if it doesn't exist
                $uploadDir = 'uploads/materials/' . $materialId . '/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Process each uploaded file
                $fileCount = count($_FILES['files']['name']);
                $fileErrors = [];

                for ($i = 0; $i < $fileCount; $i++) {
                    if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = basename($_FILES['files']['name'][$i]);
                        $fileType = $_FILES['files']['type'][$i];
                        $fileSize = $_FILES['files']['size'][$i];
                        $fileTmpName = $_FILES['files']['tmp_name'][$i];

                        // Check file type - allow pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, zip
                        $allowedMimeTypes = [
                            'application/pdf',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'application/vnd.ms-powerpoint',
                            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                            'image/jpeg',
                            'image/png',
                            'application/zip'
                        ];

                        if (!in_array($fileType, $allowedMimeTypes)) {
                            $fileErrors[] = "File type not allowed: " . $fileType . ". Please upload only pdf, doc, docx, ppt, pptx, xls, xlsx, jpg, png, or zip files.";
                            continue;
                        }

                        // Check file size - 10GB limit (10737418240 bytes)
                        if ($fileSize > 10737418240) {
                            $fileErrors[] = "File size exceeds the maximum allowed size of 10GB.";
                            continue;
                        }

                        // Generate a unique filename to prevent overwriting
                        $uniqueFileName = uniqid() . '_' . $fileName;
                        $targetFilePath = $uploadDir . $uniqueFileName;

                        // Move the uploaded file to the target directory
                        if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                            // Save file information to the database
                            try {
                                global $pdo;
                                $stmt = $pdo->prepare("
                                    INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size, uploaded_by)
                                    VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize, :uploadedBy)
                                ");

                                $relativePath = 'uploads/materials/' . $materialId . '/' . $uniqueFileName;

                                $stmt->bindParam(':activityId', $materialId);
                                $stmt->bindParam(':fileName', $fileName);
                                $stmt->bindParam(':filePath', $relativePath);
                                $stmt->bindParam(':fileType', $fileType);
                                $stmt->bindParam(':fileSize', $fileSize);
                                $stmt->bindParam(':uploadedBy', $_SESSION['user_id']);

                                $stmt->execute();
                            } catch (PDOException $e) {
                                $fileErrors[] = "Database error: " . $e->getMessage();
                            }
                        } else {
                            $fileErrors[] = "Failed to upload file: " . $fileName;
                        }
                    } elseif ($_FILES['files']['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                        $fileErrors[] = "Error uploading file: " . getFileUploadError($_FILES['files']['error'][$i]);
                    }
                }

                if (!empty($fileErrors)) {
                    $_SESSION['error'] = "Material updated but there were issues with file uploads: " . implode("<br>", $fileErrors);
                } else {
                    $_SESSION['success'] = "Material updated successfully!";
                }
            } else {
                $_SESSION['success'] = "Material updated successfully!";
            }
        } else {
            $_SESSION['error'] = $result;
        }
    } else {
        $_SESSION['error'] = implode("<br>", $errors);
    }

    // Redirect back to the material edit page
    header("location: material_edit.php?id=$materialId");
    exit;
} else {
    // If not a POST request, redirect to the material edit page
    header("location: material_edit.php?id=$materialId");
    exit;
}
?>
