<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/activity_functions.php';

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "No activity ID provided.";
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    echo "Error: " . $activity;
    exit;
}

// Display activity details
echo "<h2>Activity Details</h2>";
echo "<p><strong>ID:</strong> " . $activity['activity_id'] . "</p>";
echo "<p><strong>Title:</strong> " . htmlspecialchars($activity['title']) . "</p>";
echo "<p><strong>Type:</strong> " . htmlspecialchars($activity['activity_type']) . "</p>";
echo "<p><strong>Should show Questions tab:</strong> " . (($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz') ? 'Yes' : 'No') . "</p>";

// Check if we should update the activity type
if (isset($_GET['fix']) && $_GET['fix'] == 1) {
    // Connect to the database
    $conn = getConnection();
    
    // Update the activity type to 'activity' if it's not already one of the types that should show questions
    if ($activity['activity_type'] != 'activity' && $activity['activity_type'] != 'assignment' && $activity['activity_type'] != 'quiz') {
        $sql = "UPDATE activities SET activity_type = 'activity' WHERE activity_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $activityId);
        
        if ($stmt->execute()) {
            echo "<p class='text-success'>Activity type updated to 'activity'.</p>";
            echo "<p>Please <a href='activity_edit.php?id=$activityId'>click here</a> to go to the edit page.</p>";
        } else {
            echo "<p class='text-danger'>Error updating activity type: " . $conn->error . "</p>";
        }
        
        $stmt->close();
    } else {
        echo "<p>No need to update activity type.</p>";
    }
    
    $conn->close();
} else {
    // Show links to fix or view
    echo "<p><a href='?id=$activityId&fix=1' class='btn btn-warning'>Fix Activity Type</a></p>";
    echo "<p><a href='activity_edit.php?id=$activityId' class='btn btn-primary'>Go to Edit Page</a></p>";
    echo "<p><a href='fix_activity_edit.php?id=$activityId' class='btn btn-success'>Use New Edit Page</a></p>";
}
?>
